log_format custom_combined '$remote_addr - $remote_user [$time_local] '
                           '"$request" $status $body_bytes_sent '
                           '"$http_referer" "$http_user_agent" '
                           '$http_x_forwarded_for $request_time $upstream_response_time '
                           '$host "$request_body"';

access_log /dev/stdout custom_combined;
error_log /dev/stderr warn;

server {
    listen 9000;
    server_name _; # Accepts all hostnames since it's for a Kubernetes service

    root /usr/share/nginx/html; # Default directory in the container for static files
    index index.html;

    # Enable gzip
    gzip on;

    # Set gzip compression level (1-9, higher = more compression but slower)
    gzip_comp_level 6;

    # File types to compress
    gzip_types text/plain text/css application/json application/octet-stream application/javascript text/xml application/xml application/xml+rss text/javascript font/ttf font/otf font/eot font/woff font/woff2 image/svg+xml image/jpeg image/png image/gif image/svg+xml image/webp;

    # Enable gzip for proxied requests and HTTP 1.0
    gzip_proxied any;
    gzip_http_version 1.0;

    # Disable gzip for very small files
    gzip_min_length 512;

    # Don't compress responses to older browsers that don't support it
    # gzip_disable "msie6";

    # Handle application routes: fallback to index.html if the file does not exist
    location / {
        try_files $uri /index.html;
    }

    # Static asset caching for performance optimization
    location ~* \.(?:ico|css|woff2?|ttf|otf|eot|svg|png|jpg|jpeg|gif|webp|wasm)$ {
        expires 6M; # Cache for six months
        access_log off; # Disable access logs for these file types
        add_header Cache-Control "public";
    }

    # JavaScript files with cache-busting
    location ~* \.js$ {
        add_header Cache-Control "public, max-age=31536000, immutable";
    }

    # Critical files like index.html should not be aggressively cached
    location = /index.html {
        expires 0;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
    }

}