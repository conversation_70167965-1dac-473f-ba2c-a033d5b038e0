<!DOCTYPE html>
<html>

<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project.">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="admin_portal">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png" />

  <title>admin_portal</title>
  <link rel="manifest" href="manifest.json">

  <script>
    // The value below is injected by flutter build, do not touch.
    const serviceWorkerVersion = null;
  </script>

  <!-- CORS and Credentials Configuration -->
  <script>
    // Configure XMLHttpRequest to include credentials by default
    (function () {
      const originalOpen = XMLHttpRequest.prototype.open;
      XMLHttpRequest.prototype.open = function (method, url, async, user, password) {
        originalOpen.call(this, method, url, async, user, password);
        // Set withCredentials to true for cross-origin requests to our API
        if (url.includes('devapi.dks.nexqloud.net') ||
          url.includes('api.nexqloud.net') ||
          url.includes('stageapi.dks.nexqloud.net')) {
          this.withCredentials = true;
        }
      };
    })();

    // Configure fetch to include credentials by default
    (function () {
      const originalFetch = window.fetch;
      window.fetch = function (input, init) {
        // If init is not provided, create it
        if (!init) {
          init = {};
        }

        // Check if the URL is for our API
        const url = typeof input === 'string' ? input : input.url;
        if (url.includes('devapi.dks.nexqloud.net') ||
          url.includes('api.nexqloud.net') ||
          url.includes('stageapi.dks.nexqloud.net')) {
          // Set credentials to 'include' for cross-origin requests
          init.credentials = 'include';
          init.mode = 'cors';
        }

        return originalFetch.call(this, input, init);
      };
    })();
  </script>

  <!-- This script adds the flutter initialization JS code -->
  <script src="flutter.js?version=1.0.1+1" defer></script>
</head>

<body>
  <script>
    window.addEventListener('load', function (ev) {
      const appVersion = "1.0.1+1";
      // Download main.dart.js
      _flutter.loader.loadEntrypoint({
        serviceWorker: {
          serviceWorkerVersion: serviceWorkerVersion,
        },
        entrypointUrl: '/main.dart.js?version=' + appVersion,
        onEntrypointLoaded: function (engineInitializer) {
          engineInitializer.initializeEngine().then(function (appRunner) {
            appRunner.runApp();
          });
        }
      });
    });
  </script>
</body>

</html>