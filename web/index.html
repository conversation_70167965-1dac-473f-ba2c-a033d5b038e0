<!DOCTYPE html>
<html>

<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project.">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="admin_portal">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png" />

  <title>admin_portal</title>
  <link rel="manifest" href="manifest.json">

  <script>
    // The value below is injected by flutter build, do not touch.
    const serviceWorkerVersion = null;
  </script>

  <!-- CORS and Credentials Configuration -->
  <script>
    // More aggressive approach to ensure credentials are included
    (function () {
      // Override XMLHttpRequest
      const OriginalXHR = window.XMLHttpRequest;
      window.XMLHttpRequest = function () {
        const xhr = new OriginalXHR();
        const originalOpen = xhr.open;
        const originalSend = xhr.send;

        xhr.open = function (method, url, async, user, password) {
          originalOpen.call(this, method, url, async, user, password);

          // Check if URL is for our API domains
          if (url.includes('localapi.dks.nexqloud.net') ||
            url.includes('devapi.dks.nexqloud.net') ||
            url.includes('api.nexqloud.net') ||
            url.includes('stageapi.dks.nexqloud.net')) {
            this.withCredentials = true;
            console.log('Setting withCredentials=true for:', url);
          }
        };

        return xhr;
      };

      // Copy static properties
      for (const prop in OriginalXHR) {
        if (OriginalXHR.hasOwnProperty(prop)) {
          window.XMLHttpRequest[prop] = OriginalXHR[prop];
        }
      }
      window.XMLHttpRequest.prototype = OriginalXHR.prototype;
    })();

    // Override fetch API
    (function () {
      const originalFetch = window.fetch;
      window.fetch = function (input, init) {
        const url = typeof input === 'string' ? input : input.url;

        // Check if URL is for our API domains
        if (url.includes('localapi.dks.nexqloud.net') ||
          url.includes('devapi.dks.nexqloud.net') ||
          url.includes('api.nexqloud.net') ||
          url.includes('stageapi.dks.nexqloud.net')) {

          // Ensure init object exists
          if (!init) {
            init = {};
          }

          // Force credentials and CORS mode
          init.credentials = 'include';
          init.mode = 'cors';

          console.log('Setting credentials=include for fetch:', url, init);
        }

        return originalFetch.call(this, input, init);
      };
    })();

    // Additional safety net - intercept all network requests
    if (window.navigator && window.navigator.serviceWorker) {
      // Ensure service worker doesn't interfere
      window.navigator.serviceWorker.getRegistrations().then(function (registrations) {
        for (let registration of registrations) {
          registration.unregister();
        }
      });
    }
  </script>

  <!-- This script adds the flutter initialization JS code -->
  <script src="flutter.js?version=1.0.1+1" defer></script>
</head>

<body>
  <script>
    window.addEventListener('load', function (ev) {
      const appVersion = "1.0.1+1";
      // Download main.dart.js
      _flutter.loader.loadEntrypoint({
        serviceWorker: {
          serviceWorkerVersion: serviceWorkerVersion,
        },
        entrypointUrl: '/main.dart.js?version=' + appVersion,
        onEntrypointLoaded: function (engineInitializer) {
          engineInitializer.initializeEngine().then(function (appRunner) {
            appRunner.runApp();
          });
        }
      });
    });
  </script>
</body>

</html>