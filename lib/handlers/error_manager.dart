import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

class ErrorManager {
  ErrorManager._();

  /// Initializes Sentry.
  static Future<void> init() async {
    const kSentryDsn =
        'https://<EMAIL>/4508490195140608';

    await SentryFlutter.init(
      (options) {
        options.dsn = kSentryDsn;
        options.tracesSampleRate = 1.0;
        options.attachScreenshot = true;
        options.enableAutoNativeBreadcrumbs = true;
        options.enableAutoSessionTracking = true;
        options.enableAutoPerformanceTracing = true;
        options.enableWatchdogTerminationTracking = true;
        options.environment = kDebugMode ? 'Debug' : 'main';
      },
    );

    await setInitialAnonymousUser();
  }

  static void setSentryUser({
    required String userID,
  }) {
    if (userID.isEmpty) return;

    // if (userID == null) return;

    final user = SentryUser(
      id: userID,
      // email: userEmail,
    );

    Sentry.configureScope((scope) => scope.setUser(user));
  }

  /// Sets the Sentry user to an anonymous user.
  static Future<void> setInitialAnonymousUser() async {
    final deviceInfo = DeviceInfoPlugin();

    var deviceID = '';
    if (Platform.isIOS) {
      final iosInfo = await deviceInfo.iosInfo;
      deviceID = iosInfo.identifierForVendor ?? '';
    } else if (Platform.isAndroid) {
      deviceID = await deviceInfo.androidInfo.then((value) => value.id);
    } else {
      deviceID = await deviceInfo.webBrowserInfo
          .then((value) => value.browserName.name);
    }

    final user = SentryUser(
      id: 'Before Login',
      username: deviceID,
    );

    Sentry.configureScope((scope) => scope.setUser(user));
  }

  /// Reports an error to Sentry.
  static void reportError(Object error, StackTrace? stackTrace) {
    Sentry.captureException(
      error,
      stackTrace: stackTrace,
    );
  }
}
