import 'package:flutter/material.dart';

import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:responsive_framework/responsive_framework.dart';

import 'package:admin_portal/configs/configs_export.dart';
import 'package:admin_portal/provider/provider_export.dart';
import 'package:admin_portal/services/services_export.dart';
import 'package:admin_portal/utils/utils_export.dart';

class DashBoardScreen extends StatefulWidget {
  const DashBoardScreen({
    super.key,
    required this.child,
  });
  final StatefulNavigationShell child;
  @override
  State<DashBoardScreen> createState() => _DashBoardScreenState();
}

class _DashBoardScreenState extends State<DashBoardScreen>
    with TickerProviderStateMixin {
  late TabController _controller;

  void _changeSelectedIndex(
    int index,
  ) {
    widget.child.goBranch(index);
  }

  @override
  void initState() {
    super.initState();
    _initializeDashboard();

    _controller = TabController(
      length: 2, // Default length, will be updated after checking user
      vsync: this,
    );
  }

  Future<void> _initializeDashboard() async {
    try {
      final userName = await SecureStorageService.getUserName();

      // Update tab controller length based on user type
      if (userName == AppStrings.username2) {
        _controller = TabController(length: 1, vsync: this);
      } else {
        _controller = TabController(length: 2, vsync: this);
      }
      
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      print('Error initializing dashboard: $e');
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _controller.addListener(() {
        if (_controller.indexIsChanging) {
          _changeSelectedIndex(_controller.index);
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    if (ResponsiveBreakpoints.of(context).isDesktop) {
      print('Desktop');
    } else if (ResponsiveBreakpoints.of(context).isTablet) {
      print('Tablet');
    } else if (ResponsiveBreakpoints.of(context).isMobile) {
      print('Mobile');
    }
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: Column(
        children: [
          Expanded(
            child: Row(
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(left: 50),
                    child: Text(
                      AppStrings.welcomeText,
                      style: AppTextStyles.textStyleDashboardTitle(context),
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Container(
                    height: 50,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 5,
                    ),
                    child: DecoratedBox(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(
                          10,
                        ),
                        color: Provider.of<DarkThemeProvider>(context).darkTheme
                            ? AppColors.kDarkGrayLight
                            : AppColors.kLightGray.withOpacity(0.8),
                      ),
                      child: TabBar(
                        controller: _controller,
                        indicatorSize: TabBarIndicatorSize.tab,
                        labelColor: Colors.white,
                        unselectedLabelColor: Colors.black,
                        indicator: BoxDecoration(
                          borderRadius: BorderRadius.circular(
                            10,
                          ),
                          color:
                              Provider.of<DarkThemeProvider>(context).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                        ),
                        labelStyle: AppTextStyles.textStyleBold14(context),
                        unselectedLabelStyle:
                            AppTextStyles.textStyleBold14(context),
                        tabs: PreferenceUtils().getString('username') ==
                                AppStrings.username2
                            ? const [
                                Tab(
                                  text: AppStrings.supportDashboard,
                                ),
                              ]
                            : const [
                                Tab(
                                  text: AppStrings.manageMerchant,
                                ),
                                Tab(
                                  text: AppStrings.supportDashboard,
                                ),
                              ],
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: Padding(
                      padding: const EdgeInsets.only(right: 50),
                      child: Consumer<AuthProvider>(
                        builder: (context, authProvider, child) {
                          return ElevatedButton.icon(
                            onPressed: () async {
                              // Show confirmation dialog
                              final shouldLogout = await showDialog<bool>(
                                context: context,
                                builder: (BuildContext context) {
                                  return AlertDialog(
                                    title: const Text('Logout'),
                                    content: const Text('Are you sure you want to logout?'),
                                    actions: [
                                      TextButton(
                                        onPressed: () => Navigator.of(context).pop(false),
                                        child: const Text('Cancel'),
                                      ),
                                      TextButton(
                                        onPressed: () => Navigator.of(context).pop(true),
                                        child: const Text('Logout'),
                                      ),
                                    ],
                                  );
                                },
                              );
                              
                              if (shouldLogout == true) {
                                await authProvider.logout(context);
                              }
                            },
                            icon: const Icon(Icons.logout),
                            label: const Text('Logout'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 20,
                                vertical: 10,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8),
            child: Divider(
              color: AppColors.kBlack,
              thickness: 1,
            ),
          ),
          Expanded(
            flex: 11,
            child: Row(
              children: [
                const SizedBox(width: 50),
                Expanded(
                  child: widget.child,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
