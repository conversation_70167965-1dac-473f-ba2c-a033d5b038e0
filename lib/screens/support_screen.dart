import 'dart:async';

import 'package:admin_portal/configs/configs_export.dart';
import 'package:admin_portal/models/support_datamodel.dart';
import 'package:admin_portal/provider/provider_export.dart';
import 'package:admin_portal/utils/constants/common_const.dart';
import 'package:admin_portal/utils/constants/global_config.dart';
import 'package:admin_portal/utils/utils_export.dart';
import 'package:admin_portal/widgets/custom_search_field.dart';
import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class SupportScreen extends StatefulWidget {
  const SupportScreen({super.key});

  @override
  State<SupportScreen> createState() => _SupportScreenState();
}

class _SupportScreenState extends State<SupportScreen>
    with TickerProviderStateMixin {
  late TabController _controller;

  final TextEditingController textController = TextEditingController();

  late ManageMerchantProvider provider;
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    provider = Provider.of<ManageMerchantProvider>(context, listen: false);
    provider.isLoading = true;
    _controller = TabController(length: 4, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      provider.getSupportListData(1, GlobalConfig().pageLimit,context);
      _controller.addListener(() {
        if (_controller.indexIsChanging) {
          provider.changeSelectedIndex(_controller.index);
        }
      });
    });
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _controller.removeListener(() {});
    _controller.dispose();
    textController.dispose();
    EasyDebounce.cancelAll();
    super.dispose();
  }

  // Debounced search method
  void _onSearchChanged(String value,{int duration = 2000}) {
    // Cancel the previous timer
    _debounceTimer?.cancel();

    // Start a new timer
    _debounceTimer = Timer(Duration(milliseconds: duration), () {
      // This will execute after user stops typing for 500ms
      if (mounted) {
        provider.isSearching = true;
        Provider.of<ManageMerchantProvider>(context, listen: false)
            .getSupportListData(1, GlobalConfig().pageLimit,context, search : textController.text.trim());
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ManageMerchantProvider>(
      builder: (context, _, child) {
        return provider.isLoading
            ? const Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    vertical: 200,
                    horizontal: 100,
                  ),
                  child: CupertinoActivityIndicator(),
                ),
              )
            : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: [
                          SizedBox(
                            width: 150,
                            height: 48,
                            child: ElevatedButton(
                              style: ButtonStyle(
                                foregroundColor: WidgetStateProperty.all<Color>(
                                  Colors.white,
                                ),
                                backgroundColor: WidgetStateProperty.all<Color>(
                                  Provider.of<DarkThemeProvider>(context)
                                          .darkTheme
                                      ? AppColors.kDarkPrimarySwatch
                                      : AppColors.kLightPrimarySwatch,
                                ),
                                shape: WidgetStateProperty.all<
                                    RoundedRectangleBorder>(
                                  RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(20),
                                    side: const BorderSide(
                                      color: Colors.blueAccent,
                                    ),
                                  ),
                                ),
                              ),
                              onPressed: () {
                                Provider.of<MyTeamProvider>(
                                  context,
                                  listen: false,
                                ).sendEmailDialog(
                                  context,
                                );
                              },
                              child: const Text(
                                'Sent Email',
                                style: TextStyle(fontSize: 14),
                              ),
                            ),
                          ),
                          SizedBox(
                            width: 400,
                            height: 56,
                            child: Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 20),
                              child: CustomSearchField(
                                textController: textController,
                                onSearchPressed: (value) {
                                 _onSearchChanged(value,duration: 0);
                                },
                                onChanged: (value) {
                                  if (value.isEmpty) {
                                    _onSearchChanged(value,duration: 3000);
                                  }
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                      const Spacer(),
                    ],
                  ),
                  const SizedBox(height: 20),
                  Expanded(
                    child: DecoratedBox(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(color: AppColors.kLightGrayLight),
                      ),
                      child: Scrollbar(
                        controller: ScrollController(),
                        child: ListView(
                          children: [
                            provider.isSearching
                                ? const Center(
                                    child: Padding(
                                      padding: EdgeInsets.symmetric(
                                        vertical: 200,
                                        horizontal: 100,
                                      ),
                                      child: CupertinoActivityIndicator(),
                                    ),
                                  )
                                : Padding(
                                    padding: const EdgeInsets.only(
                                      left: 16,
                                      right: 16,
                                    ),
                                    child: provider.isFoundNothing
                                        ? const Center(
                                            child: Padding(
                                              padding: EdgeInsets.all(100),
                                              child: Text('No customer found'),
                                            ),
                                          )
                                        : Stack(children: [
                                            SizedBox(
                                              width: double.infinity,
                                              child: PaginatedDataTable(
                                                availableRowsPerPage: const [
                                                  10,
                                                ],
                                                columns: [
                                                  DataColumn(
                                                    label: Checkbox(
                                                      onChanged: (value) {
                                                        provider
                                                            .setAllChecked(value);
                                                      },
                                                      value: provider.allChecked,
                                                    ),
                                                  ),
                                                  DataColumn(
                                                    label: Row(
                                                      children: [
                                                        Text(
                                                          'USER ID',
                                                          style: AppTextStyles
                                                              .textStyleBold12(
                                                            context,
                                                          ),
                                                        ),
                                                        CupertinoButton(
                                                          onPressed: () {
                                                            // Cancel any pending search when sorting
                                                            _debounceTimer
                                                                ?.cancel();

                                                            provider.lastSupportListSort =
                                                                orderByUserID;
                                                            provider.getSupportListData(
                                                                1,
                                                                GlobalConfig()
                                                                    .pageLimit,
                                                                context,
                                                                search:
                                                                textController
                                                                    .text
                                                                    .trim(),
                                                                sort: provider
                                                                    .isSupportListUserIdAscending
                                                                    ? ascending
                                                                    : descending);
                                                            provider.isSupportListUserIdAscending =
                                                            !provider
                                                                .isSupportListUserIdAscending;
                                                          },
                                                          child: Transform.rotate(
                                                            angle: 900,
                                                            child: const Icon(
                                                              Icons
                                                                  .compare_arrows_rounded,
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  DataColumn(
                                                    label: Row(
                                                      children: [
                                                        Text(
                                                          AppStrings.merchantName,
                                                          style: AppTextStyles
                                                              .textStyleBold12(
                                                            context,
                                                          ),
                                                        ),
                                                        CupertinoButton(
                                                          onPressed: () {
                                                            // Cancel any pending search when sorting
                                                            _debounceTimer
                                                                ?.cancel();

                                                            provider.lastSupportListSort =
                                                                orderByFirstName;
                                                            provider.getSupportListData(
                                                                1,
                                                                GlobalConfig()
                                                                    .pageLimit,
                                                                context,
                                                                search:
                                                                textController
                                                                    .text
                                                                    .trim(),
                                                                sort: provider
                                                                    .isSupportListFullNameAscending
                                                                    ? ascending
                                                                    : descending);
                                                            provider.isSupportListFullNameAscending =
                                                            !provider
                                                                .isSupportListFullNameAscending;
                                                          },
                                                          child: Transform.rotate(
                                                            angle: 900,
                                                            child: const Icon(
                                                              Icons
                                                                  .compare_arrows_rounded,
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  DataColumn(
                                                    label: Row(
                                                      children: [
                                                        Text(
                                                          AppStrings
                                                              .supportMobileNumber,
                                                          style: AppTextStyles
                                                              .textStyleBold12(
                                                            context,
                                                          ),
                                                        ),
                                                        CupertinoButton(
                                                          onPressed: () {
                                                            // Cancel any pending search when sorting
                                                            _debounceTimer
                                                                ?.cancel();

                                                            provider.lastSupportListSort =
                                                                orderByMobileNumber;
                                                            provider.getSupportListData(
                                                                1,
                                                                GlobalConfig()
                                                                    .pageLimit,
                                                                context,
                                                                search:
                                                                textController
                                                                    .text
                                                                    .trim(),
                                                                sort: provider
                                                                    .isSupportListMobileNumberAscending
                                                                    ? ascending
                                                                    : descending);
                                                            provider.isSupportListMobileNumberAscending =
                                                            !provider
                                                                .isSupportListMobileNumberAscending;
                                                          },
                                                          child: Transform.rotate(
                                                            angle: 900,
                                                            child: const Icon(
                                                              Icons
                                                                  .compare_arrows_rounded,
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  DataColumn(
                                                    label: Row(
                                                      children: [
                                                        Text(
                                                          AppStrings.supportEmails,
                                                          style: AppTextStyles
                                                              .textStyleBold12(
                                                            context,
                                                          ),
                                                        ),
                                                        CupertinoButton(
                                                          onPressed: () {
                                                            // Cancel any pending search when sorting
                                                            _debounceTimer
                                                                ?.cancel();

                                                            provider.lastSupportListSort =
                                                                orderByEmailAddress;
                                                            provider.getSupportListData(
                                                                1,
                                                                GlobalConfig()
                                                                    .pageLimit,
                                                                context,
                                                                search:
                                                                textController
                                                                    .text
                                                                    .trim(),
                                                                sort: provider
                                                                    .isSupportListEmailAscending
                                                                    ? ascending
                                                                    : descending);
                                                            provider.isSupportListEmailAscending =
                                                            !provider
                                                                .isSupportListEmailAscending;
                                                          },
                                                          child: Transform.rotate(
                                                            angle: 900,
                                                            child: const Icon(
                                                              Icons
                                                                  .compare_arrows_rounded,
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  DataColumn(
                                                    label: Row(
                                                      children: [
                                                        Text(
                                                          AppStrings
                                                              .supportCryptoAddress,
                                                          style: AppTextStyles
                                                              .textStyleBold12(
                                                            context,
                                                          ),
                                                        ),
                                                        CupertinoButton(
                                                          onPressed: () {
                                                            // Cancel any pending search when sorting
                                                            _debounceTimer
                                                                ?.cancel();

                                                            provider.lastSupportListSort =
                                                                orderByCryptoWalletAddress;
                                                            provider.getSupportListData(
                                                                1,
                                                                GlobalConfig()
                                                                    .pageLimit,
                                                                context,
                                                                search:
                                                                textController
                                                                    .text
                                                                    .trim(),
                                                                sort: provider
                                                                    .isSupportListCryptoAddressAscending
                                                                    ? ascending
                                                                    : descending);
                                                            provider.isSupportListCryptoAddressAscending =
                                                            !provider
                                                                .isSupportListCryptoAddressAscending;
                                                          },
                                                          child: Transform.rotate(
                                                            angle: 900,
                                                            child: const Icon(
                                                              Icons
                                                                  .compare_arrows_rounded,
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                                onPageChanged: (firstRowIndex) {
                                                  _debounceTimer?.cancel();
                                                  final provider = Provider.of<ManageMerchantProvider>(
                                                      context,
                                                      listen: false,);
                                                  int pageNumber = (firstRowIndex / GlobalConfig().pageLimit).floor() + 1;
                                                  provider.updateFirstRowSupportIndex(firstRowIndex);
                                                  provider.getSupportListData(
                                                      pageNumber,
                                                      GlobalConfig().pageLimit,context);
                                                },
                                                source: provider.supportTableSource ??
                                                        SupportData(0, []),
                                              ),
                                            ),

                                            if (provider.isPaginationLoading)
                                              Positioned(
                                                bottom: 7.5,
                                                right: 265,
                                                child: Padding(
                                                  padding: const EdgeInsets
                                                      .symmetric(vertical: 16),
                                                  child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .center,
                                                    children: [
                                                      const CupertinoActivityIndicator(),
                                                      const SizedBox(width: 8),
                                                      Text(
                                                        'Loading...',
                                                        style: AppTextStyles
                                                            .textStyleRegular14(
                                                                context,),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                )
                                            ],
                                          ),
                                  ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              );
      },
    );
  }
}
