import 'dart:async'; // Add this import for Timer

import 'package:admin_portal/configs/configs_export.dart';
import 'package:admin_portal/models/models_export.dart';
import 'package:admin_portal/provider/provider_export.dart';
import 'package:admin_portal/utils/constants/common_const.dart';
import 'package:admin_portal/utils/constants/global_config.dart';
import 'package:admin_portal/utils/utils_export.dart';
import 'package:admin_portal/widgets/custom_search_field.dart';
import 'package:admin_portal/widgets/loading_overlay.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class ManageMerchantScreen extends StatefulWidget {
  const ManageMerchantScreen({super.key});

  @override
  State<ManageMerchantScreen> createState() => _ManageMerchantScreenState();
}

class _ManageMerchantScreenState extends State<ManageMerchantScreen>
    with TickerProviderStateMixin {
  late TabController _controller;
  final TextEditingController textController = TextEditingController();

  // Add debounce timer
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    _controller = TabController(length: 4, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<ManageMerchantProvider>(context, listen: false).isLoading =
          true;
      Provider.of<ManageMerchantProvider>(context, listen: false)
          .getOrderListData(1, GlobalConfig().pageLimit, context,
              search: textController.text.trim());
      _controller.addListener(() {
        if (_controller.indexIsChanging) {
          Provider.of<ManageMerchantProvider>(context, listen: false)
              .changeSelectedIndex(_controller.index);
        }
      });
    });
  }

  @override
  void dispose() {
    // Cancel debounce timer and dispose controllers
    _debounceTimer?.cancel();
    _controller.removeListener(() {});
    _controller.dispose();
    textController.dispose();
    super.dispose();
  }

  // Debounced search method
  void _onSearchChanged(String value) {
    // Cancel the previous timer
    _debounceTimer?.cancel();

    // Start a new timer
    _debounceTimer = Timer(const Duration(milliseconds: 1000), () {
      // This will execute after user stops typing for 500ms
      if (mounted) {
        Provider.of<ManageMerchantProvider>(context, listen: false).isLoading =
            true;
        Provider.of<ManageMerchantProvider>(context, listen: false)
            .getOrderListData(1, GlobalConfig().pageLimit, context,
                search: textController.text.trim());
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ManageMerchantProvider>(
      builder: (context, provider, child) {
        return provider.isLoading
            ? const Center(
                child: CupertinoActivityIndicator(),
              )
            : Stack(
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SizedBox(
                            width: 400,
                            height: 56,
                            child: Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 20),
                              child: CustomSearchField(
                                textController: textController,
                                onChanged:
                                    _onSearchChanged, // Use debounced method
                              ),
                            ),
                          ),
                          const SizedBox(
                            width: 20,
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              RichText(
                                text: TextSpan(
                                  text:
                                      'Total Orders: ${provider.getOrderListDetails.totlaOrderCount ?? "0"} (Total Devices: ${provider.getOrderListDetails.totalHardwareItems ?? "0"} - ',
                                  style: AppTextStyles.textStyleBold18(context)
                                      .copyWith(
                                    color: (Provider.of<DarkThemeProvider>(
                                      context,
                                      listen: false,
                                    ).darkTheme
                                        ? AppColors.kDarkPrimarySwatch
                                        : AppColors.kLightPrimarySwatch),
                                  ),
                                  children: <TextSpan>[
                                    TextSpan(
                                      text:
                                          'Completed: ${provider.getOrderListDetails.completedHardwaresCount ?? "0"}',
                                      style: AppTextStyles.textStyleBold18(
                                        context,
                                      ).copyWith(
                                        color: AppColors.kGreen,
                                      ),
                                    ),
                                    const TextSpan(text: ', '),
                                    TextSpan(
                                      text:
                                          'Pending: ${provider.getOrderListDetails.pendingHardwaresCount ?? "0"}',
                                      style: AppTextStyles.textStyleBold18(
                                        context,
                                      ).copyWith(
                                        color: AppColors.kRed,
                                      ),
                                    ),
                                    const TextSpan(text: ')'),
                                  ],
                                ),
                              ),
                              RichText(
                                text: TextSpan(
                                  text: 'Device Status - ',
                                  style: AppTextStyles.textStyleBold18(context)
                                      .copyWith(
                                    color: (Provider.of<DarkThemeProvider>(
                                      context,
                                      listen: false,
                                    ).darkTheme
                                        ? AppColors.kDarkPrimarySwatch
                                        : AppColors.kLightPrimarySwatch),
                                  ),
                                  children: <TextSpan>[
                                    TextSpan(
                                      text:
                                          'Online: ${provider.getOrderListDetails.activeHardwaresCount ?? "0"}',
                                      style: AppTextStyles.textStyleBold18(
                                        context,
                                      ).copyWith(
                                        color: AppColors.kGreen,
                                      ),
                                    ),
                                    const TextSpan(text: ', '),
                                    TextSpan(
                                      text:
                                          'Offline: ${provider.getOrderListDetails.disconnectedHardwaresCount ?? "0"}',
                                      style: AppTextStyles.textStyleBold18(
                                        context,
                                      ).copyWith(
                                        color: AppColors.kRed,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const Spacer(),
                          Padding(
                            padding: const EdgeInsets.only(right: 30),
                            child: CupertinoButton(
                              onPressed: () {
                                showFilterPopup(context);
                              },
                              child: Row(
                                children: [
                                  Icon(
                                    provider.isFilterApplied
                                        ? Icons.filter_alt_off_rounded
                                        : Icons.filter_alt_rounded,
                                    size: 35,
                                  ),
                                  Text(
                                    'Filter',
                                    style:
                                        AppTextStyles.textStyleBold18(context),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      Expanded(
                        child: DecoratedBox(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: AppColors.kLightGrayLight,
                            ),
                          ),
                          child: Scrollbar(
                            controller: ScrollController(),
                            child: ListView(
                              shrinkWrap: true,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(
                                    left: 16,
                                    right: 16,
                                  ),
                                  child: Consumer<ManageMerchantProvider>(
                                    builder: (context, provider, child) {
                                      return Stack(
                                        children: [
                                          SizedBox(
                                            width: double.infinity,
                                            child: PaginatedDataTable(
                                              key: ValueKey(provider.filterStateCounter),
                                              horizontalMargin: 0,
                                              columns: [
                                              DataColumn(
                                                label: Row(
                                                  children: [
                                                    Text(
                                                      AppStrings.merchantId,
                                                      style: AppTextStyles
                                                          .textStyleBold12(
                                                        context,
                                                      ),
                                                    ),
                                                    CupertinoButton(
                                                      onPressed: () {
                                                        // Cancel any pending search when sorting
                                                        _debounceTimer
                                                            ?.cancel();

                                                        provider.lastOrderSort =
                                                            orderId;
                                                        provider.getOrderListData(
                                                            1,
                                                            GlobalConfig()
                                                                .pageLimit,
                                                            context,
                                                            search:
                                                                textController
                                                                    .text
                                                                    .trim(),
                                                            sort: provider
                                                                    .isOrderIdAscending
                                                                ? ascending
                                                                : descending);
                                                        provider.isOrderIdAscending =
                                                            !provider
                                                                .isOrderIdAscending;
                                                      },
                                                      child: Transform.rotate(
                                                        angle: 900,
                                                        child: const Icon(
                                                          Icons
                                                              .compare_arrows_rounded,
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              DataColumn(
                                                label: Row(
                                                  children: [
                                                    Text(
                                                      AppStrings.merchantStatus,
                                                      style: AppTextStyles
                                                          .textStyleBold12(
                                                        context,
                                                      ),
                                                    ),
                                                    CupertinoButton(
                                                      onPressed: () {
                                                        // Cancel any pending search when sorting
                                                        _debounceTimer
                                                            ?.cancel();

                                                        provider.lastOrderSort =
                                                            status;
                                                        provider.getOrderListData(
                                                            1,
                                                            GlobalConfig()
                                                                .pageLimit,
                                                            context,
                                                            search:
                                                                textController
                                                                    .text
                                                                    .trim(),
                                                            sort: provider
                                                                    .isStatusAscending
                                                                ? ascending
                                                                : descending);
                                                        provider.isStatusAscending =
                                                            !provider
                                                                .isStatusAscending;
                                                      },
                                                      child: Transform.rotate(
                                                        angle: 900,
                                                        child: const Icon(
                                                          Icons
                                                              .compare_arrows_rounded,
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              DataColumn(
                                                label: Text(
                                                  AppStrings.assignHostnames,
                                                  style: AppTextStyles
                                                      .textStyleBold12(
                                                    context,
                                                  ),
                                                ),
                                              ),
                                              DataColumn(
                                                label: Row(
                                                  children: [
                                                    Text(
                                                      AppStrings.assignHostname,
                                                      style: AppTextStyles
                                                          .textStyleBold12(
                                                        context,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              DataColumn(
                                                label: Row(
                                                  children: [
                                                    Text(
                                                      AppStrings.assignOnline,
                                                      style: AppTextStyles
                                                          .textStyleBold12(
                                                        context,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              DataColumn(
                                                label: Row(
                                                  children: [
                                                    Text(
                                                      AppStrings
                                                          .merchantAddedDate,
                                                      style: AppTextStyles
                                                          .textStyleBold12(
                                                        context,
                                                      ),
                                                    ),
                                                    CupertinoButton(
                                                      onPressed: () {
                                                        // Cancel any pending search when sorting
                                                        _debounceTimer
                                                            ?.cancel();

                                                        provider.lastOrderSort =
                                                            acceptedOn;
                                                        provider.getOrderListData(
                                                            1,
                                                            GlobalConfig()
                                                                .pageLimit,
                                                            context,
                                                            search:
                                                                textController
                                                                    .text
                                                                    .trim(),
                                                            sort: provider
                                                                    .isAcceptedOnAscending
                                                                ? ascending
                                                                : descending);
                                                        provider.isAcceptedOnAscending =
                                                            !provider
                                                                .isAcceptedOnAscending;
                                                      },
                                                      child: Transform.rotate(
                                                        angle: 900,
                                                        child: const Icon(
                                                          Icons
                                                              .compare_arrows_rounded,
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              DataColumn(
                                                label: Row(
                                                  children: [
                                                    Text(
                                                      AppStrings.merchantName,
                                                      style: AppTextStyles
                                                          .textStyleBold12(
                                                        context,
                                                      ),
                                                    ),
                                                    CupertinoButton(
                                                      onPressed: () {
                                                        // Cancel any pending search when sorting
                                                        _debounceTimer
                                                            ?.cancel();

                                                        provider.lastOrderSort =
                                                            customerInfoFullName;
                                                        provider.getOrderListData(
                                                            1,
                                                            GlobalConfig()
                                                                .pageLimit,
                                                            context,
                                                            search:
                                                                textController
                                                                    .text
                                                                    .trim(),
                                                            sort: provider
                                                                    .isAscending
                                                                ? ascending
                                                                : descending);
                                                        provider.isAscending =
                                                            !provider
                                                                .isAscending;
                                                      },
                                                      child: Transform.rotate(
                                                        angle: 900,
                                                        child: const Icon(
                                                          Icons
                                                              .compare_arrows_rounded,
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              DataColumn(
                                                label: Row(
                                                  children: [
                                                    Text(
                                                      AppStrings.hideMerchant,
                                                      style: AppTextStyles
                                                          .textStyleBold12(
                                                        context,
                                                      ),
                                                    ),
                                                    CupertinoButton(
                                                      onPressed: () {
                                                        // Cancel any pending search when sorting
                                                        _debounceTimer
                                                            ?.cancel();

                                                        provider.lastOrderSort =
                                                            customerInfoEmail;
                                                        provider.getOrderListData(
                                                            1,
                                                            GlobalConfig()
                                                                .pageLimit,
                                                            context,
                                                            search:
                                                                textController
                                                                    .text
                                                                    .trim(),
                                                            sort: provider
                                                                    .isEmailAscending
                                                                ? ascending
                                                                : descending);
                                                        provider.isEmailAscending =
                                                            !provider
                                                                .isEmailAscending;
                                                      },
                                                      child: Transform.rotate(
                                                        angle: 900,
                                                        child: const Icon(
                                                          Icons
                                                              .compare_arrows_rounded,
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                            rowsPerPage:
                                                GlobalConfig().pageLimit,
                                            availableRowsPerPage: const [10],
                                            onPageChanged: (firstRowIndex) {
                                              // Cancel any pending search when changing page
                                              _debounceTimer?.cancel();

                                              final provider = Provider.of<
                                                      ManageMerchantProvider>(
                                                  context,
                                                  listen: false);

                                              int pageNumber = (firstRowIndex /
                                                          GlobalConfig()
                                                              .pageLimit)
                                                      .floor() +
                                                  1;

                                              provider.updateFirstRowIndex(
                                                  firstRowIndex);
                                              provider.getOrderListData(
                                                  pageNumber,
                                                  GlobalConfig().pageLimit,
                                                  context,
                                                  search: textController.text
                                                      .trim());
                                            },
                                            initialFirstRowIndex:
                                                provider.firstRowIndex,
                                            source: provider.tableSource ??
                                                ManageMerchantData(
                                                    0, [], [], context),
                                          ),
                                        ),
                                          if (provider.isPaginationLoading)
                                            Positioned(
                                              bottom: 7.5,
                                              right: 265,
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        vertical: 16),
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    const CupertinoActivityIndicator(),
                                                    const SizedBox(width: 8),
                                                    Text(
                                                      'Loading...',
                                                      style: AppTextStyles
                                                          .textStyleRegular14(
                                                              context),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            )
                                        ],
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  LoadingOverlay(isLoading: provider.isLoading),
                ],
              );
      },
    );
  }

  void showFilterPopup(BuildContext context) {
    final provider =
        Provider.of<ManageMerchantProvider>(context, listen: false);
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Filter Orders'),
              content: SizedBox(
                width: MediaQuery.of(context).size.width * 0.2,
                height: MediaQuery.of(context).size.height * 0.2,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Expanded(
                      child: ListView(
                        children: [
                          CheckboxListTile(
                            title: Text(AppStrings.Provisioning),
                            value: provider.selectedStatuses
                                .contains(AppStrings.Provisioning),
                            onChanged: (value) {
                              setState(() {
                                _onStatusChanged(
                                  AppStrings.Provisioning,
                                  value,
                                  provider,
                                );
                              });
                            },
                          ),
                          CheckboxListTile(
                            title: Text(AppStrings.Configuring),
                            value: provider.selectedStatuses
                                .contains(AppStrings.Configuring),
                            onChanged: (value) {
                              setState(() {
                                _onStatusChanged(
                                  AppStrings.Configuring,
                                  value,
                                  provider,
                                );
                              });
                            },
                          ),
                          CheckboxListTile(
                            title: Text(AppStrings.QATesting),
                            value: provider.selectedStatuses
                                .contains(AppStrings.QA_Testing),
                            onChanged: (value) {
                              setState(() {
                                _onStatusChanged(
                                  AppStrings.QA_Testing,
                                  value,
                                  provider,
                                );
                              });
                            },
                          ),
                          CheckboxListTile(
                            title: Text(AppStrings.PackShip),
                            value: provider.selectedStatuses
                                .contains(AppStrings.PackShip),
                            onChanged: (value) {
                              setState(() {
                                _onStatusChanged(
                                  AppStrings.PackShip,
                                  value,
                                  provider,
                                );
                              });
                            },
                          ),
                          CheckboxListTile(
                            title: Text(AppStrings.Shipped),
                            value: provider.selectedStatuses
                                .contains(AppStrings.Shipped),
                            onChanged: (value) {
                              setState(() {
                                _onStatusChanged(
                                  AppStrings.Shipped,
                                  value,
                                  provider,
                                );
                              });
                            },
                          ),
                        ],
                      ),
                    ),
                    ListTile(
                      title: const Text('Show orders after'),
                      subtitle: Text(
                        provider.selectedDate == null
                            ? 'Select Date'
                            : '${provider.selectedDate!.toLocal()}'
                                .split(' ')[0],
                      ),
                      trailing: const Icon(Icons.calendar_month),
                      onTap: () {
                        _selectDate(context, provider, setState);
                      },
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    // Cancel any pending search when clearing filter
                    _debounceTimer?.cancel();
                    Navigator.of(context).pop();
                    provider.clearFilter(context);
                  },
                  child: const Text('Clear Filter'),
                ),
                TextButton(
                  onPressed: () {
                    // Cancel any pending search when applying filter
                    _debounceTimer?.cancel();
                    Navigator.of(context).pop();
                    provider.applyFilter(context);
                  },
                  child: const Text('Apply Filter'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _onStatusChanged(
    String status,
    bool? value,
    ManageMerchantProvider provider,
  ) {
    if (value == true) {
      provider.addSelectedStatus(status);
    } else {
      provider.removeSelectedStatus(status);
    }
  }

  Future<void> _selectDate(
    BuildContext context,
    ManageMerchantProvider provider,
    StateSetter setState,
  ) async {
    final picked = await showDatePicker(
      context: context,
      initialDate: provider.selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2101),
    );
    if (picked != null && picked != provider.selectedDate) {
      setState(() {
        provider.setSelectedDate(picked);
      });
    }
  }
}
