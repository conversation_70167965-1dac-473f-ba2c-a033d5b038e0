import 'dart:async';

import 'package:admin_portal/routes/route_names.dart';
import 'package:admin_portal/services/services_export.dart';
import 'package:admin_portal/utils/utils_export.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _checkAuthenticationStatus();
  }

  Future<void> _checkAuthenticationStatus() async {
    // Wait for splash screen duration
    await Future.delayed(const Duration(seconds: 3));
    
    try {
      // Check if user is logged in using secure storage
      final isLoggedIn = await SecureStorageService.isUserLoggedIn();
      
      // print("IsLoggedIn :: $isLoggedIn");

      if (isLoggedIn) {
        // User is authenticated, navigate to appropriate screen
        if (mounted) {
          context.go(context.namedLocation(AppRouteNames.manageMerchant));
        }
      } else {
        // User is not authenticated, navigate to login
        if (mounted) {
          context.goNamed(AppRouteNames.login);
        }
      }
    } catch (e) {
      // print('Error checking authentication status: $e');
      
      // If there's a persistent OperationError, try to force reset storage
      if (e.toString().contains('OperationError')) {
        // print('Attempting to force reset secure storage...');
        await SecureStorageService.forceResetStorage();
      }
      
      // Navigate to login as fallback
      if (mounted) {
        context.goNamed(AppRouteNames.login);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Image.asset(
      ImageConstants.payNationTag,
      color: const Color(0xFF221F3A),
    );
  }
}
