import 'dart:developer';

import 'package:admin_portal/configs/configs_export.dart';
import 'package:admin_portal/models/support_list_model.dart';
import 'package:admin_portal/provider/provider_export.dart';
import 'package:admin_portal/utils/helper_functions.dart';
import 'package:admin_portal/utils/utils_export.dart';
import 'package:admin_portal/widgets/widgets_export.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class MerchantDetailsPage extends StatefulWidget {
  const MerchantDetailsPage({super.key, required this.id, required this.email});
  final String id, email;

  @override
  State<MerchantDetailsPage> createState() => _MerchantDetailsPageState();
}

class _MerchantDetailsPageState extends State<MerchantDetailsPage>
    with TickerProviderStateMixin {
  late TabController _controller;
  List<Map<String, dynamic>> data = [
    {
      '_id': '6602c4be86ce88c1d7fc1581',
      'user_name': 'Your Device',
      'user_email': 'SRV-3KLYGI',
      'role': '1.685676',
    },
    {
      '_id': '6602c4be86ce88c1d7fc1581',
      'user_name': 'Your Device',
      'user_email': 'SRV-3KLYGI',
      'role': '2.685676',
    },
    {
      '_id': '6602c4be86ce88c1d7fc15823',
      'user_name': 'Your Device',
      'user_email': 'SRV-3KLYGI',
      'role': '0.685676',
    },
  ];

  List headers = [
    'Server ID',
    'Mac Address Model',
    'NFT License',
    'Purchase Date',
    'Device Status',
    'Total Earning',
    'CPU Score',
    'GPU Score',
    'RAM Score',
    'DISK Score',
    'Normalized CPU Score',
    'Normalized GPU Score',
    'Normalized RAM Score',
    'Normalized DISK Score',
    'Normalized ISP Score',
    'Normalized Device Score',
    'Cloud Score',
  ];

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _controller = TabController(length: 3, vsync: this);
    // WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
    //   Provider.of<MerchantDetailsProvider>(context, listen: false)
    //       .getMerchantDetails(widget.id);
    // });
    log('Support Details Email: ${widget.email}');
    // _controller = TabController(length: 3, vsync: this, initialIndex: 0);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      Provider.of<ManageMerchantProvider>(context, listen: false)
          .getSupportDetailsData(1, 1000, widget.id);
    });
  }

  @override
  void dispose() {
    // TODO: implement dispose
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: buildAppBar(context, AppStrings.merchantPage),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SelectableText(
            AppStrings.merchantDetailsMessage,
            style: AppTextStyles.textStyleBold14(context),
          ),
          const SizedBox(
            height: 30,
          ),
          // Row(
          //   children: [
          //     Container(
          //       width: 100,
          //       height: 100,
          //       decoration: BoxDecoration(
          //         borderRadius: BorderRadius.circular(20),
          //         border: Border.all(color: AppColors.kLightGray),
          //       ),
          //     ),
          //     SizedBox(
          //       width: 20,
          //     ),
          //     Consumer<MerchantDetailsProvider>(
          //         builder: (context, provider, child) {
          //       return Column(
          //         crossAxisAlignment: CrossAxisAlignment.start,
          //         children: [
          //           Row(
          //             children: [
          //               SelectableText(provider.merchantDetails!.userName,
          //                   style: AppTextStyles.textStyleBold26(context)),
          //               SizedBox(
          //                 width: 10,
          //               ),
          //               SelectableText(provider.merchantDetails!.status.name,
          //                   style: AppTextStyles.textStyleBold14(context)),
          //               SizedBox(
          //                 width: 4,
          //               ),
          //               Switch.adaptive(
          //                   value: provider.merchantDetails!.status.name ==
          //                           "ACTIVE"
          //                       ? true
          //                       : false,
          //                   activeColor: AppColors.kGreen,
          //                   inactiveThumbColor: AppColors.kBlack,
          //                   thumbColor:
          //                       MaterialStateProperty.all(AppColors.kWhite),
          //                   onChanged: (value) {
          //                     provider
          //                         .toggleMerchant(provider.merchantDetails!.id);
          //                   }),
          //             ],
          //           ),
          //           SelectableText(provider.merchantDetails!.siteName,
          //               style: AppTextStyles.textStyleBold16(context).copyWith(
          //                   color: Provider.of<DarkThemeProvider>(context,
          //                               listen: false)
          //                           .darkTheme
          //                       ? AppColors.kDarkPrimarySwatch
          //                       : AppColors.kLightPrimarySwatch,
          //                   decoration: TextDecoration.underline,
          //                   decorationColor: AppColors.kLightPrimarySwatch)),
          //           Row(
          //             children: [
          //               IconButton(
          //                 onPressed: () {},
          //                 icon: provider.merchantDetails!.hidden
          //                     ? const Icon(Icons.visibility_off_outlined)
          //                     : const Icon(Icons.visibility_outlined),
          //               ),
          //               SelectableText(
          //                 "Hide Merchant",
          //                 style: AppTextStyles.textStyleBold14(context),
          //               ),
          //             ],
          //           )
          //         ],
          //       );
          //     }),
          //     Spacer(),
          //     Container(
          //       height: 100,
          //       padding: EdgeInsets.only(left: 20, right: 20),
          //       decoration: BoxDecoration(
          //         borderRadius: BorderRadius.circular(20),
          //         border: Border.all(color: AppColors.kLightGray),
          //       ),
          //       child: Column(children: [
          //         SelectableText(
          //           "Customers",
          //           style: AppTextStyles.textStyleBold16(context).copyWith(
          //             color:
          //                 Provider.of<DarkThemeProvider>(context, listen: false)
          //                         .darkTheme
          //                     ? AppColors.kDarkPrimarySwatch
          //                     : AppColors.kLightPrimarySwatch,
          //           ),
          //         ),
          //         Row(
          //           children: [
          //             customerCount("12", "Active", AppColors.kGreen),
          //             VerticalDivider(
          //               color: AppColors.kRed,
          //               thickness: 1,
          //             ),
          //             customerCount("5", "Inactive", AppColors.kRed),
          //             VerticalDivider(
          //               color: AppColors.kLightGray,
          //               thickness: 1,
          //             ),
          //             customerCount("2", "Invited", AppColors.kYellow),
          //           ],
          //         )
          //       ]),
          //     ),
          //     SizedBox(
          //       width: 20,
          //     ),
          //   ],
          // ),
          // SizedBox(
          //   height: 100,
          // ),
          // Divider(
          //   color: AppColors.kLightGray,
          //   thickness: 1,
          // ),
          Expanded(
            child: Container(
              child: Consumer<ManageMerchantProvider>(
                builder: (context, provider, child) {
                  return ListView(
                    shrinkWrap: true,
                    // scrollDirection: Axis.vertical,
                    children: [
                      Align(
                        alignment: Alignment.centerLeft,
                        child: SelectableText(
                          'ORDERS',
                          style: AppTextStyles.textStyleBold16(context),
                        ),
                      ),
                      provider.getSupportDetails[0].orders!.isNotEmpty
                          ? OrdersWidget(
                              provider.getSupportDetails[0].orders!,
                              screenHeight,
                            )
                          : noDataWidget(),
                      Align(
                        alignment: Alignment.centerLeft,
                        child: SelectableText(
                          'NFTs',
                          style: AppTextStyles.textStyleBold16(context),
                        ),
                      ),
                      // Expanded(
                      // height: screenHeight * 0.3,
                      // child:
                      provider.getSupportDetails[0].nFTs!.isNotEmpty
                          ? NftWidget(provider.getSupportDetails[0].nFTs!)
                          : noDataWidget(),
                      // ),

                      // Container(
                      //   // constraints: BoxConstraints(
                      //   //     minHeight: 200, maxHeight: double.maxFinite),
                      //   height: double.negativeInfinity,
                      //   child: ScrollableTableView(
                      //     headers: headers.map((column) {
                      //       return TableViewHeader(
                      //         label: column,
                      //       );
                      //     }).toList(),
                      //     rows: provider.getSupportDetails[0].servers!
                      //         .map((product) {
                      //       return TableViewRow(
                      //         height: 60,
                      //         cells: headers.map((column) {
                      //           return TableViewCell(
                      //             child: SelectableText(
                      //               product.serverID ?? "",
                      //               style:
                      //                   AppTextStyles.textStyleRegular16(context),
                      //             ),
                      //           );
                      //         }).toList(),
                      //       );
                      //     }).toList(),
                      //   ),
                      // ),
                      // Container(
                      //   constraints: const BoxConstraints(minHeight: 50),
                      //   height: 500,
                      //   width: 400,
                      //   child: ListView.builder(
                      //       scrollDirection: Axis.horizontal,
                      //       physics: const AlwaysScrollableScrollPhysics(),
                      //       // shrinkWrap: true,
                      //       itemCount: 2,
                      //       // physics: const BouncingScrollPhysics(),
                      //       itemBuilder: (BuildContext context, int index) {
                      //         return ListTile(
                      //           title: Row(
                      //             children: [
                      //               SelectableText(
                      //                 "Normalized GpuScore",
                      //                 style: AppTextStyles.textStyleBold14(
                      //                         context)
                      //                     .copyWith(
                      //                         color:
                      //                             Provider.of<DarkThemeProvider>(
                      //                                         context)
                      //                                     .darkTheme
                      //                                 ? AppColors
                      //                                     .kDarkPrimarySwatch
                      //                                 : AppColors
                      //                                     .kLightPrimarySwatch),
                      //               ),
                      //               SizedBox(
                      //                 width: 100,
                      //               ),
                      //               SelectableText(
                      //                 "Normalized GpuScore",
                      //                 style: AppTextStyles.textStyleBold14(
                      //                         context)
                      //                     .copyWith(
                      //                         color:
                      //                             Provider.of<DarkThemeProvider>(
                      //                                         context)
                      //                                     .darkTheme
                      //                                 ? AppColors
                      //                                     .kDarkPrimarySwatch
                      //                                 : AppColors
                      //                                     .kLightPrimarySwatch),
                      //               ),
                      //               SizedBox(
                      //                 width: 100,
                      //               ),
                      //               SelectableText(
                      //                 "Normalized GpuScore",
                      //                 style: AppTextStyles.textStyleBold14(
                      //                         context)
                      //                     .copyWith(
                      //                         color:
                      //                             Provider.of<DarkThemeProvider>(
                      //                                         context)
                      //                                     .darkTheme
                      //                                 ? AppColors
                      //                                     .kDarkPrimarySwatch
                      //                                 : AppColors
                      //                                     .kLightPrimarySwatch),
                      //               ),
                      //               SizedBox(
                      //                 width: 100,
                      //               ),
                      //               SelectableText(
                      //                 "Normalized GpuScore",
                      //                 style: AppTextStyles.textStyleBold14(
                      //                         context)
                      //                     .copyWith(
                      //                         color:
                      //                             Provider.of<DarkThemeProvider>(
                      //                                         context)
                      //                                     .darkTheme
                      //                                 ? AppColors
                      //                                     .kDarkPrimarySwatch
                      //                                 : AppColors
                      //                                     .kLightPrimarySwatch),
                      //               ),
                      //               SizedBox(
                      //                 width: 100,
                      //               ),
                      //               SelectableText(
                      //                 "Normalized GpuScore",
                      //                 style: AppTextStyles.textStyleBold14(
                      //                         context)
                      //                     .copyWith(
                      //                         color:
                      //                             Provider.of<DarkThemeProvider>(
                      //                                         context)
                      //                                     .darkTheme
                      //                                 ? AppColors
                      //                                     .kDarkPrimarySwatch
                      //                                 : AppColors
                      //                                     .kLightPrimarySwatch),
                      //               ),
                      //               SizedBox(
                      //                 width: 100,
                      //               ),
                      //               SelectableText(
                      //                 "Normalized GpuScore",
                      //                 style: AppTextStyles.textStyleBold14(
                      //                         context)
                      //                     .copyWith(
                      //                         color:
                      //                             Provider.of<DarkThemeProvider>(
                      //                                         context)
                      //                                     .darkTheme
                      //                                 ? AppColors
                      //                                     .kDarkPrimarySwatch
                      //                                 : AppColors
                      //                                     .kLightPrimarySwatch),
                      //               ),
                      //               SizedBox(
                      //                 width: 100,
                      //               ),
                      //               SelectableText(
                      //                 "Normalized GpuScore",
                      //                 style: AppTextStyles.textStyleBold14(
                      //                         context)
                      //                     .copyWith(
                      //                         color:
                      //                             Provider.of<DarkThemeProvider>(
                      //                                         context)
                      //                                     .darkTheme
                      //                                 ? AppColors
                      //                                     .kDarkPrimarySwatch
                      //                                 : AppColors
                      //                                     .kLightPrimarySwatch),
                      //               ),
                      //               SizedBox(
                      //                 width: 100,
                      //               ),
                      //               SelectableText(
                      //                 "Normalized GpuScore",
                      //                 style: AppTextStyles.textStyleBold14(
                      //                         context)
                      //                     .copyWith(
                      //                         color:
                      //                             Provider.of<DarkThemeProvider>(
                      //                                         context)
                      //                                     .darkTheme
                      //                                 ? AppColors
                      //                                     .kDarkPrimarySwatch
                      //                                 : AppColors
                      //                                     .kLightPrimarySwatch),
                      //               ),
                      //               SizedBox(
                      //                 width: 100,
                      //               ),
                      //             ],
                      //           ),
                      //         );
                      //       }),
                      // ),
                      Align(
                        alignment: Alignment.centerLeft,
                        child: SelectableText(
                          'Servers',
                          style: AppTextStyles.textStyleBold16(context),
                        ),
                      ),
                      provider.getSupportDetails[0].servers!.isNotEmpty
                          ? servers(provider.getSupportDetails[0].servers!)
                          : noDataWidget(),

                      Align(
                        alignment: Alignment.centerLeft,
                        child: SelectableText(
                          'Referral Stats',
                          style: AppTextStyles.textStyleBold16(context),
                        ),
                      ),
                      provider.getSupportDetails[0].refferalStats!.isNotEmpty
                          ? refferalStatus(
                              provider.getSupportDetails[0].refferalStats!,
                            )
                          : noDataWidget(),
                      // Align(
                      //   alignment: Alignment.centerLeft,
                      //   child: SelectableText("Daily Payout",
                      //       style: AppTextStyles.textStyleBold14(context)),
                      // ),
                      // provider.getSupportDetails[0].orders!.isNotEmpty
                      //     ? DailyPayout(
                      //         provider.getSupportDetails[0].dailyEarnings!)
                      //     : noDataWidget(),
                      const SizedBox(
                        height: 30,
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
          // Expanded(
          //   child: Container(
          //       decoration: BoxDecoration(
          //           borderRadius: BorderRadius.circular(20),
          //           border:
          //               Border.all(color: AppColors.kLightGrayLight, width: 1)),
          //       child: Scrollbar(
          //           controller: ScrollController(),
          //           child: ListView(shrinkWrap: true, children: [
          //             // TabBar(
          //             //     controller: _controller,
          //             //     labelStyle: AppTextStyles.textStyleBold14(context),
          //             //     unselectedLabelStyle:
          //             //         AppTextStyles.textStyleBold14(context),
          //             //     tabs: [
          //             //       Tab(
          //             //         text: AppStrings.allUsers,
          //             //       ),
          //             //       Tab(
          //             //         text: AppStrings.activeUsers,
          //             //       ),
          //             //       Tab(
          //             //         text: AppStrings.inactiveUsers,
          //             //       ),
          //             //     ]),
          //             Consumer<ManageMerchantProvider>(
          //                 builder: (context, provider, child) {
          //               return Padding(
          //                   padding: const EdgeInsets.only(left: 16, right: 16),
          //                   child: PaginatedDataTable(
          //                     dataRowHeight: 300,
          //                     columns: [
          //                       DataColumn(
          //                         label: SelectableText(AppStrings.userId,
          //                             style: AppTextStyles.textStyleBold12(
          //                                 context)),
          //                       ),
          //                       DataColumn(
          //                         label: SelectableText("NFTS",
          //                             style: AppTextStyles.textStyleBold12(
          //                                 context)),
          //                       ),
          //                       DataColumn(
          //                         label: SelectableText("SERVERS",
          //                             style: AppTextStyles.textStyleBold12(
          //                                 context)),
          //                       ),
          //                       DataColumn(
          //                         label: SelectableText("ORDERS",
          //                             style: AppTextStyles.textStyleBold12(
          //                                 context)),
          //                       ),
          //                       DataColumn(
          //                         label: SelectableText("ADDRESS",
          //                             style: AppTextStyles.textStyleBold12(
          //                                 context)),
          //                       ),
          //                     ],
          //                     source: MerchantData(
          //                         provider.getSupportDetails, context),
          //                     // source: MerchantData(
          //                     //     data
          //                     //         .map((e) => MerchantUsers.fromJson(e))
          //                     //         .toList(),
          //                     //     context),
          //                   ));
          //             })
          //           ]))),
          // )
        ],
      ),
    );
  }

  Widget customerCount(String count, String status, Color color) {
    return Column(
      children: [
        SelectableText(
          count,
          style: AppTextStyles.textStyleBold22(context).copyWith(color: color),
        ),
        SelectableText(status, style: AppTextStyles.textStyleBold16(context)),
      ],
    );
  }

  Widget noDataWidget() {
    return Padding(
      padding: const EdgeInsets.all(8),
      child: DecoratedBox(
        // height: screenHeight * 0.3,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: AppColors.kLightGrayLight),
        ),
        child: Padding(
          padding: const EdgeInsets.all(50),
          child: SelectableText(
            'No Data',
            style: AppTextStyles.textStyleBold14(context).copyWith(
              color: Provider.of<DarkThemeProvider>(context).darkTheme
                  ? AppColors.kDarkPrimarySwatch
                  : AppColors.kLightPrimarySwatch,
            ),
          ),
        ),
      ),
    );
  }

  Widget OrdersWidget(List<Orders> list, double screenHeight) {
    return Padding(
      padding: const EdgeInsets.all(8),
      child: DecoratedBox(
        // height: screenHeight * 0.35,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: AppColors.kLightGrayLight),
        ),
        child: Column(
          children: [
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(
                    height: 20,
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // SizedBox(
                      //   width: 20,
                      // ),
                      retunItemWidet('Order Id', list[0].orderId!, context),
                      retunItemWidet('Status', list[0].status!, context),
                      retunItemWidet(
                        'Quantity',
                        '${list[0].purchasedItems![0].count!}',
                        context,
                      ),
                      retunItemWidet(
                        'Product Name',
                        list[0].purchasedItems![0].productName!,
                        context,
                      ),
                      retunItemWidet(
                        'Sub Total Cost',
                        list[0].totals!.subtotal!.string!,
                        context,
                      ),
                      retunItemWidet(
                        'Shipping Cost',
                        list[0].totals!.extras![0].price!.string!,
                        context,
                      ),
                      retunItemWidet(
                        'Total Cost',
                        list[0].totals!.total!.string!,
                        context,
                      ),
                      retunItemWidet(
                        'Payment Type',
                        list[0].paymentProcessor!,
                        context,
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 35,
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // SizedBox(
                      //   width: 20,
                      // ),
                      SelectableText(
                        list.isNotEmpty
                            ? 'Shipping Address: \n${list[0].shippingAddress!.addressee!} \n${list[0].shippingAddress!.line1!} \n${list[0].shippingAddress!.city!}, ${list[0].shippingAddress!.state}, ${list[0].shippingAddress!.country!} ${list[0].shippingAddress!.postalCode!}'
                            : '',
                        style: AppTextStyles.textStyleBold14(context),
                      ),
                      const SizedBox(
                        width: 100,
                      ),
                      SelectableText(
                        list.isNotEmpty
                            ? 'Billing Address: \n${list[0].billingAddress!.addressee!} \n${list[0].billingAddress!.line1!} \n${list[0].billingAddress!.city!}, ${list[0].billingAddress!.state}, ${list[0].billingAddress!.country!} ${list[0].billingAddress!.postalCode!}'
                            : '',
                        style: AppTextStyles.textStyleBold14(context),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget refferalStatus(List<RefferalStats> items) {
    return Padding(
      padding: const EdgeInsets.all(8),
      child: DecoratedBox(
        // height: screenHeight * 0.3,
        // height: 200,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: AppColors.kLightGrayLight),
        ),
        child: Padding(
          padding: const EdgeInsets.all(8),
          // child: Scrollbar(
          //   controller: ScrollController(),
          child: SizedBox(
            // height: MediaQuery.of(context).size.height,
            width: 2000,

            child: ListView.builder(
              // physics: const AlwaysScrollableScrollPhysics(), // new

              shrinkWrap: true,
              itemCount: items.isEmpty ? 1 : items.length + 1,
              itemBuilder: (context, index) {
                if (index == 0) {
                  // return the header
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 20),
                    child: Row(
                      children: [
                        // SizedBox(
                        //   width: 20,
                        // ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'Invite Status',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        // SizedBox(
                        //   width: 100,
                        // ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'Personal Devices',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        // SizedBox(
                        //   width: 100,
                        // ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'Device Uptime',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        // SizedBox(
                        //   width: 100,
                        // ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'Managed Devices',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        // SizedBox(
                        //   width: 100,
                        // ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'Team Devices ',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        // SizedBox(
                        //   width: 50,
                        // ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'Average Score',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        // SizedBox(
                        //   width: 100,
                        // ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'Normalized Score',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }
                index -= 1;

                // return row
                final row = items[index];
                return Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Expanded(
                      child: SelectableText(
                        textAlign: TextAlign.center,
                        row.tier!,
                        style: AppTextStyles.textStyleRegular16(context),
                      ),
                    ),
                    // SizedBox(
                    //   width: 50,
                    // ),
                    Expanded(
                      child: SelectableText(
                        textAlign: TextAlign.center,
                        row.personalDevicesCount!.toString(),
                        // HelperFunctions.convertToDate(row.createdAt!),
                        style: AppTextStyles.textStyleRegular16(context),
                      ),
                    ),
                    // SizedBox(
                    //   width: 100,
                    // ),
                    Expanded(
                      child: SelectableText(
                        textAlign: TextAlign.center,
                        row.deviceUpTime!.toString(),
                        // "${HelperFunctions.formatAmountTwoDecimals(row.totalEarning!)}",
                        style: AppTextStyles.textStyleRegular16(context),
                      ),
                    ),
                    // SizedBox(
                    //   width: 100,
                    // ),
                    Expanded(
                      child: SelectableText(
                        textAlign: TextAlign.center,
                        row.managedDevicesCount!.toString(),
                        // "\$${HelperFunctions.formatAmountTwoDecimals(row.totalEarningUSD!)}",
                        style: AppTextStyles.textStyleRegular16(context),
                      ),
                    ),
                    // SizedBox(
                    //   width: 100,
                    // ),
                    Expanded(
                      child: SelectableText(
                        textAlign: TextAlign.center,
                        row.managedTeamCount.toString(),
                        // HelperFunctions.convertToDate(row.purchaseDate!),
                        // HelperFunctions.formatAmountTwoDecimals(
                        //     row.totalEarningDailyUsagePay!),
                        style: AppTextStyles.textStyleRegular16(context),
                      ),
                    ),
                    // SizedBox(
                    //   width: 100,
                    // ),

                    Expanded(
                      child: SelectableText(
                        textAlign: TextAlign.center,
                        row.averageScore!.toString(),
                        style: AppTextStyles.textStyleRegular16(context),
                      ),
                    ),
                    Expanded(
                      child: SelectableText(
                        textAlign: TextAlign.center,
                        row.normalizedScore!.toString(),
                        style: AppTextStyles.textStyleRegular16(context),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget servers(List<Servers> items) {
    return Padding(
      padding: const EdgeInsets.all(8),
      child: DecoratedBox(
        // height: screenHeight * 0.3,
        // height: 200,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: AppColors.kLightGrayLight),
        ),
        child: Padding(
          padding: const EdgeInsets.all(8),
          // child: Scrollbar(
          //   controller: ScrollController(),
          child: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              physics: const BouncingScrollPhysics(),
              shrinkWrap: true,
              itemCount: items.isEmpty ? 1 : items.length + 1,
              itemBuilder: (context, index) {
                if (index == 0) {
                  // return the header
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 20),
                    child: Row(
                      children: [
                        // SizedBox(
                        //   width: 20,
                        // ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'Server ID',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        // SizedBox(
                        //   width: 100,
                        // ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'Mac Address',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        // SizedBox(
                        //   width: 100,
                        // ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'Model',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        // SizedBox(
                        //   width: 100,
                        // ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'NFT License',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        // SizedBox(
                        //   width: 100,
                        // ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'Purchase Date',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        // SizedBox(
                        //   width: 100,
                        // ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'Device Status',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        // SizedBox(
                        //   width: 100,
                        // ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'Total Earning',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        // SizedBox(
                        //   width: 100,
                        // ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'CPUScore',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        // SizedBox(
                        //   width: 100,
                        // ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'GPUScore',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'RAMScore',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'DISKScore',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'Normalized CpuScore',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'Normalized GpuScore',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'Normalized RamScore',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'Normalized DiskScore',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'Normalized ISPScore',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'Normalized DeviceScore',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'Cloud Score',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }
                index -= 1;

                // return row
                final row = items[index];
                return Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // SizedBox(
                    //   width: 20,
                    // ),
                    // Expanded(
                    //   child: SelectableText(
                    //     "${row.nft!.tokenId}",
                    //     style: AppTextStyles.textStyleRegular16(context),
                    //   ),
                    // ),
                    // SizedBox(
                    //   width: 50,
                    // ),
                    // Expanded(
                    //   child: Padding(
                    //     padding: const EdgeInsets.only(
                    //         right: 30.0, top: 8, bottom: 8),
                    //     child: Container(
                    //       width: 100,
                    //       height: 50,
                    //       child: ClipRRect(
                    //         borderRadius: BorderRadius.all(
                    //           Radius.circular(16),
                    //         ),
                    //         child: CachedNetworkImage(
                    //           width: 100,
                    //           height: 50,
                    //           imageUrl: row.nft!.imageUrl!,
                    //           fit: BoxFit.fill,
                    //         ),
                    //       ),
                    //     ),
                    //   ),
                    // ),
                    // // SelectableText(
                    // //   row.nft!.imageUrl!,
                    // //   style: AppTextStyles.textStyleRegular16(context),
                    // //   // style: AppTextStyles.textStyleBold14(context).copyWith(
                    // //   //     color: Provider.of<DarkThemeProvider>(context).darkTheme
                    // //   //         ? AppColors.kDarkPrimarySwatch
                    // //   //         : AppColors.kLightPrimarySwatch),
                    // // )
                    // // SizedBox(
                    // //   width: 50,
                    // // ),
                    Expanded(
                      // width: screenWidth * 0.29,
                      // height: (56 / 800) * screenHeight,
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: AutoSizeText(
                          row.serverID!,
                          style: AppTextStyles.textStyleRegular16(context),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          // stepGranularity: 12,
                          // presetFontSizes: [16, 14, 12],
                        ),
                      ),
                    ),
                    // Expanded(
                    //   child: SelectableText(
                    //     textAlign: TextAlign.center,
                    //     row.serverID!,
                    //     style: AppTextStyles.textStyleRegular16(context),
                    //   ),
                    // ),
                    // SizedBox(
                    //   width: 50,
                    // ),
                    Expanded(
                      child: SelectableText(
                        textAlign: TextAlign.center,
                        row.macAddress!,
                        // HelperFunctions.convertToDate(row.createdAt!),
                        style: AppTextStyles.textStyleRegular16(context),
                      ),
                    ),
                    // SizedBox(
                    //   width: 100,
                    // ),
                    Expanded(
                      child: SelectableText(
                        textAlign: TextAlign.center,
                        row.model!,
                        // "${HelperFunctions.formatAmountTwoDecimals(row.totalEarning!)}",
                        style: AppTextStyles.textStyleRegular16(context),
                      ),
                    ),
                    // SizedBox(
                    //   width: 100,
                    // ),
                    Expanded(
                      child: SelectableText(
                        textAlign: TextAlign.center,
                        row.licenseId!,
                        // "\$${HelperFunctions.formatAmountTwoDecimals(row.totalEarningUSD!)}",
                        style: AppTextStyles.textStyleRegular16(context),
                      ),
                    ),
                    // SizedBox(
                    //   width: 100,
                    // ),
                    Expanded(
                      child: SelectableText(
                        textAlign: TextAlign.center,
                        HelperFunctions.convertToDate(row.purchaseDate!),
                        // HelperFunctions.formatAmountTwoDecimals(
                        //     row.totalEarningDailyUsagePay!),
                        style: AppTextStyles.textStyleRegular16(context),
                      ),
                    ),
                    // SizedBox(
                    //   width: 100,
                    // ),
                    Expanded(
                      child: SelectableText(
                        textAlign: TextAlign.center,
                        row.deviceStatus! == true ? 'Online' : 'Offline',
                        // HelperFunctions.formatAmountTwoDecimals(
                        //     row.totalEarningConnectionPay!),
                        style:
                            AppTextStyles.textStyleRegular16(context).copyWith(
                          color: row.deviceStatus == true
                              ? AppColors.kGreen
                              : AppColors.kRed,
                        ),
                      ),
                    ),
                    // SizedBox(
                    //   width: 100,
                    // ),
                    Expanded(
                      child: SelectableText(
                        textAlign: TextAlign.center,
                        HelperFunctions.formatAmountTwoDecimals(
                          row.totalEarning!,
                        ),
                        style: AppTextStyles.textStyleRegular16(context),
                      ),
                    ),
                    Expanded(
                      child: SelectableText(
                        textAlign: TextAlign.center,
                        row.cpuScore!.toString(),
                        style: AppTextStyles.textStyleRegular16(context),
                      ),
                    ),
                    Expanded(
                      child: SelectableText(
                        textAlign: TextAlign.center,
                        row.gpuScore!.toString(),
                        style: AppTextStyles.textStyleRegular16(context),
                      ),
                    ),
                    Expanded(
                      child: SelectableText(
                        textAlign: TextAlign.center,
                        row.ramScore!.toString(),
                        style: AppTextStyles.textStyleRegular16(context),
                      ),
                    ),
                    Expanded(
                      child: SelectableText(
                        textAlign: TextAlign.center,
                        row.diskScore!.toString(),
                        style: AppTextStyles.textStyleRegular16(context),
                      ),
                    ),
                    Expanded(
                      child: SelectableText(
                        textAlign: TextAlign.center,
                        row.normalizedCpuScore!.toString(),
                        style: AppTextStyles.textStyleRegular16(context),
                      ),
                    ),
                    Expanded(
                      child: SelectableText(
                        textAlign: TextAlign.center,
                        row.normalizedGpuScore!.toString(),
                        style: AppTextStyles.textStyleRegular16(context),
                      ),
                    ),
                    Expanded(
                      child: SelectableText(
                        textAlign: TextAlign.center,
                        row.normalizedRamScore!.toString(),
                        style: AppTextStyles.textStyleRegular16(context),
                      ),
                    ),
                    Expanded(
                      child: SelectableText(
                        textAlign: TextAlign.center,
                        row.normalizedDiskScore!.toString(),
                        style: AppTextStyles.textStyleRegular16(context),
                      ),
                    ),
                    Expanded(
                      child: SelectableText(
                        textAlign: TextAlign.center,
                        row.normalizedISPScore!.toString(),
                        style: AppTextStyles.textStyleRegular16(context),
                      ),
                    ),
                    Expanded(
                      child: SelectableText(
                        textAlign: TextAlign.center,
                        row.normalizedDeviceScore!.toString(),
                        style: AppTextStyles.textStyleRegular16(context),
                      ),
                    ),
                    Expanded(
                      child: SelectableText(
                        textAlign: TextAlign.center,
                        row.cloudScore!.toString(),
                        style: AppTextStyles.textStyleRegular16(context),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget DailyPayout(List<DailyEarnings> items) {
    return Padding(
      padding: const EdgeInsets.all(8),
      child: DecoratedBox(
        // height: screenHeight * 0.3,
        // height: 200,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: AppColors.kLightGrayLight),
        ),
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: Scrollbar(
            controller: ScrollController(),
            child: ListView.builder(
              // physics: const AlwaysScrollableScrollPhysics(), // new

              shrinkWrap: true,
              itemCount: items.isEmpty ? 1 : items.length + 1,
              itemBuilder: (context, index) {
                if (index == 0) {
                  // return the header
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 20),
                    child: Row(
                      children: [
                        const SizedBox(
                          width: 50,
                        ),
                        SelectableText(
                          'Earnings',
                          style:
                              AppTextStyles.textStyleBold14(context).copyWith(
                            color: Provider.of<DarkThemeProvider>(
                              context,
                            ).darkTheme
                                ? AppColors.kDarkPrimarySwatch
                                : AppColors.kLightPrimarySwatch,
                          ),
                        ),
                        const SizedBox(
                          width: 100,
                        ),
                        SelectableText(
                          'Date',
                          style:
                              AppTextStyles.textStyleBold14(context).copyWith(
                            color: Provider.of<DarkThemeProvider>(
                              context,
                            ).darkTheme
                                ? AppColors.kDarkPrimarySwatch
                                : AppColors.kLightPrimarySwatch,
                          ),
                        ),
                      ],
                    ),
                  );
                }
                index -= 1;

                // return row
                final row = items[index];
                return Row(
                  children: [
                    const SizedBox(
                      width: 50,
                    ),
                    SelectableText(
                      '\$${HelperFunctions.formatAmountTwoDecimals(row.nxqEarningsInUsd!)}',
                      style: AppTextStyles.textStyleRegular16(context),
                    ),
                    const SizedBox(
                      width: 100,
                    ),
                    // Padding(
                    //   padding: const EdgeInsets.all(8.0),
                    //   child: ClipRRect(
                    //     borderRadius: BorderRadius.all(
                    //       Radius.circular(16),
                    //     ),
                    //     child: CachedNetworkImage(
                    //       width: 100,
                    //       height: 50,
                    //       imageUrl:
                    //           HelperFunctions.convertToDate(row.date!),
                    //       fit: BoxFit.fill,
                    //     ),
                    //   ),
                    // )
                    SelectableText(
                      HelperFunctions.convertToDate(row.date!),
                      style: AppTextStyles.textStyleRegular16(context),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}

Row retunItemWidet(String heading, String row, BuildContext context) {
  return Row(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SelectableText(
            // "\$${HelperFunctions.formatAmountTwoDecimals(row.nxqEarningsInUsd!)}",
            heading,
            style: AppTextStyles.textStyleBold14(context).copyWith(
              color: Provider.of<DarkThemeProvider>(context).darkTheme
                  ? AppColors.kDarkPrimarySwatch
                  : AppColors.kLightPrimarySwatch,
            ),
            // style: AppTextStyles.textStyleBold16(context)
            //     .copyWith(color: Colors.grey),
          ),
          const SizedBox(
            height: 10,
            width: 100,
          ),
          SelectableText(
            row,
            // HelperFunctions.convertToDate(row.date!),
            style: AppTextStyles.textStyleBold14(context),
          ),
        ],
      ),
      const SizedBox(
        width: 50,
      ),
    ],
  );
}

Widget NftWidget(List<NFTs> items) {
  // return ListView.builder(
  //   itemCount: items.isEmpty ? 1 : items.length + 1,
  //   itemBuilder: (BuildContext context, int index) {
  //     if (index == 0) {
  //       // return the header
  //       // return new Column(...);
  //       return SelectableText("Header");
  //     }
  //     index -= 1;

  //     // return row
  //     var row = items[index];
  //     // return new InkWell(... with row ...);
  //     return SelectableText("Row");
  //   },
  // );
  // return CustomScrollView(
  //   shrinkWrap: true,

  //   slivers: [
  //     // SliverAppBar(
  //     //   title: SelectableText("Token ID"),
  //     // ),
  //     SliverList(
  //         delegate: SliverChildListDelegate(List.generate(items.length, (idx) {
  //       return Padding(
  //         padding: const EdgeInsets.only(left: 8.0, right: 8.0),
  //         child: Card(
  //           child: ListTile(
  //             leading: Icon(null),
  //             title: SelectableText(items[idx].nft!.tokenId),
  //             onTap: null,
  //           ),
  //         ),
  //       );
  //     })))
  //   ],
  // );

  return Padding(
    padding: const EdgeInsets.all(8),
    child: DecoratedBox(
      // height: screenHeight * 0.3,
      // height: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: AppColors.kLightGrayLight),
      ),
      child: Padding(
        padding: const EdgeInsets.all(8),
        // child: Scrollbar(
        //   controller: ScrollController(),
        child: SizedBox(
          // height: MediaQuery.of(context).size.height,
          width: 2000,

          child: ListView.builder(
            // physics: const AlwaysScrollableScrollPhysics(), // new

            shrinkWrap: true,
            itemCount: items.isEmpty ? 1 : items.length + 1,
            itemBuilder: (context, index) {
              if (index == 0) {
                // return the header
                return Padding(
                  padding: const EdgeInsets.only(bottom: 20),
                  child: Row(
                    children: [
                      // SizedBox(
                      //   width: 20,
                      // ),
                      Expanded(
                        child: SelectableText(
                          textAlign: TextAlign.center,
                          'Token ID',
                          style:
                              AppTextStyles.textStyleBold14(context).copyWith(
                            color: Provider.of<DarkThemeProvider>(
                              context,
                            ).darkTheme
                                ? AppColors.kDarkPrimarySwatch
                                : AppColors.kLightPrimarySwatch,
                          ),
                        ),
                      ),
                      // SizedBox(
                      //   width: 100,
                      // ),
                      Expanded(
                        child: SelectableText(
                          textAlign: TextAlign.center,
                          'Image',
                          style:
                              AppTextStyles.textStyleBold14(context).copyWith(
                            color: Provider.of<DarkThemeProvider>(
                              context,
                            ).darkTheme
                                ? AppColors.kDarkPrimarySwatch
                                : AppColors.kLightPrimarySwatch,
                          ),
                        ),
                      ),
                      // SizedBox(
                      //   width: 100,
                      // ),
                      Expanded(
                        child: SelectableText(
                          textAlign: TextAlign.center,
                          'Issuance ID',
                          style:
                              AppTextStyles.textStyleBold14(context).copyWith(
                            color: Provider.of<DarkThemeProvider>(
                              context,
                            ).darkTheme
                                ? AppColors.kDarkPrimarySwatch
                                : AppColors.kLightPrimarySwatch,
                          ),
                        ),
                      ),
                      // SizedBox(
                      //   width: 100,
                      // ),
                      Expanded(
                        child: SelectableText(
                          textAlign: TextAlign.center,
                          'Date Created',
                          style:
                              AppTextStyles.textStyleBold14(context).copyWith(
                            color: Provider.of<DarkThemeProvider>(
                              context,
                            ).darkTheme
                                ? AppColors.kDarkPrimarySwatch
                                : AppColors.kLightPrimarySwatch,
                          ),
                        ),
                      ),
                      // SizedBox(
                      //   width: 100,
                      // ),
                      Expanded(
                        child: SelectableText(
                          textAlign: TextAlign.center,
                          'Total Earnings',
                          style:
                              AppTextStyles.textStyleBold14(context).copyWith(
                            color: Provider.of<DarkThemeProvider>(
                              context,
                            ).darkTheme
                                ? AppColors.kDarkPrimarySwatch
                                : AppColors.kLightPrimarySwatch,
                          ),
                        ),
                      ),
                      // SizedBox(
                      //   width: 50,
                      // ),
                      Expanded(
                        child: SelectableText(
                          textAlign: TextAlign.center,
                          'Total Earning in USD',
                          style:
                              AppTextStyles.textStyleBold14(context).copyWith(
                            color: Provider.of<DarkThemeProvider>(
                              context,
                            ).darkTheme
                                ? AppColors.kDarkPrimarySwatch
                                : AppColors.kLightPrimarySwatch,
                          ),
                        ),
                      ),
                      // SizedBox(
                      //   width: 100,
                      // ),
                      Expanded(
                        child: SelectableText(
                          textAlign: TextAlign.center,
                          'Daily Usage Pay',
                          style:
                              AppTextStyles.textStyleBold14(context).copyWith(
                            color: Provider.of<DarkThemeProvider>(
                              context,
                            ).darkTheme
                                ? AppColors.kDarkPrimarySwatch
                                : AppColors.kLightPrimarySwatch,
                          ),
                        ),
                      ),
                      // SizedBox(
                      //   width: 50,
                      // ),
                      Expanded(
                        child: SelectableText(
                          textAlign: TextAlign.center,
                          'Connection Pay',
                          style:
                              AppTextStyles.textStyleBold14(context).copyWith(
                            color: Provider.of<DarkThemeProvider>(
                              context,
                            ).darkTheme
                                ? AppColors.kDarkPrimarySwatch
                                : AppColors.kLightPrimarySwatch,
                          ),
                        ),
                      ),
                      // SizedBox(
                      //   width: 50,
                      // ),
                      Expanded(
                        child: SelectableText(
                          textAlign: TextAlign.center,
                          'Server ID',
                          style:
                              AppTextStyles.textStyleBold14(context).copyWith(
                            color: Provider.of<DarkThemeProvider>(
                              context,
                            ).darkTheme
                                ? AppColors.kDarkPrimarySwatch
                                : AppColors.kLightPrimarySwatch,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }
              index -= 1;

              // return row
              final row = items[index];
              return Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // SizedBox(
                  //   width: 20,
                  // ),
                  Expanded(
                    child: SelectableText(
                      textAlign: TextAlign.center,
                      '${row.nft!.tokenId}',
                      style: AppTextStyles.textStyleRegular16(context),
                    ),
                  ),
                  // SizedBox(
                  //   width: 50,
                  // ),
                  Expanded(
                    child: Center(
                      child: Padding(
                        padding: const EdgeInsets.only(
                          right: 8,
                          top: 8,
                          bottom: 8,
                        ),
                        child: SizedBox(
                          width: 100,
                          height: 50,
                          child: ClipRRect(
                            borderRadius: const BorderRadius.all(
                              Radius.circular(16),
                            ),
                            child: CachedNetworkImage(
                              width: 100,
                              height: 50,
                              imageUrl: row.nft!.imageUrl!,
                              fit: BoxFit.fill,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  // // SelectableText(
                  // //   row.nft!.imageUrl!,
                  // //   style: AppTextStyles.textStyleRegular16(context),
                  // //   // style: AppTextStyles.textStyleBold14(context).copyWith(
                  // //   //     color: Provider.of<DarkThemeProvider>(context).darkTheme
                  // //   //         ? AppColors.kDarkPrimarySwatch
                  // //   //         : AppColors.kLightPrimarySwatch),
                  // // )
                  // // SizedBox(
                  // //   width: 50,
                  // // ),
                  Expanded(
                    child: SelectableText(
                      textAlign: TextAlign.center,
                      row.issuanceId!,
                      style: AppTextStyles.textStyleRegular16(context),
                    ),
                  ),
                  // SizedBox(
                  //   width: 50,
                  // ),
                  Expanded(
                    child: SelectableText(
                      textAlign: TextAlign.center,
                      HelperFunctions.convertToDate(row.createdAt!),
                      style: AppTextStyles.textStyleRegular16(context),
                    ),
                  ),
                  // SizedBox(
                  //   width: 100,
                  // ),
                  Expanded(
                    child: SelectableText(
                      textAlign: TextAlign.center,
                      // row.totalEarning!.toString(),
                      HelperFunctions.formatAmountTwoDecimals(
                        row.totalEarning!,
                      ),
                      style: AppTextStyles.textStyleRegular16(context),
                    ),
                  ),
                  // SizedBox(
                  //   width: 100,
                  // ),
                  Expanded(
                    child: SelectableText(
                      textAlign: TextAlign.center,
                      '\$${HelperFunctions.formatAmountTwoDecimals(row.totalEarningUSD!)}',
                      style: AppTextStyles.textStyleRegular16(context),
                    ),
                  ),
                  // SizedBox(
                  //   width: 100,
                  // ),
                  Expanded(
                    child: SelectableText(
                      textAlign: TextAlign.center,
                      HelperFunctions.formatAmountTwoDecimals(
                        row.totalEarningDailyUsagePay!,
                      ),
                      style: AppTextStyles.textStyleRegular16(context),
                    ),
                  ),
                  // SizedBox(
                  //   width: 100,
                  // ),
                  Expanded(
                    child: SelectableText(
                      textAlign: TextAlign.center,
                      HelperFunctions.formatAmountTwoDecimals(
                        row.totalEarningConnectionPay!,
                      ),
                      style: AppTextStyles.textStyleRegular16(context),
                    ),
                  ),
                  // SizedBox(
                  //   width: 100,
                  // ),
                  Expanded(
                    child: SelectableText(
                      textAlign: TextAlign.center,
                      row.serverID!,
                      style: AppTextStyles.textStyleRegular16(context),
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    ),
  );
}
