import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:admin_portal/configs/configs_export.dart';
import 'package:admin_portal/models/order_list_model_new.dart';
import 'package:admin_portal/provider/provider_export.dart';
import 'package:admin_portal/utils/helper_functions.dart';
import 'package:admin_portal/utils/utils_export.dart';
import 'package:admin_portal/widgets/loading_overlay.dart';
import 'package:admin_portal/widgets/widgets_export.dart';
import 'package:animate_do/animate_do.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:iconsax/iconsax.dart';
import 'package:provider/provider.dart';

class OrderDetailsPage extends StatefulWidget {
  const OrderDetailsPage({
    super.key,
    required this.id,
    required this.email,
  });
  final String id;
  final String email;

  @override
  State<OrderDetailsPage> createState() => _OrderDetailsPageState();
}

class _OrderDetailsPageState extends State<OrderDetailsPage>
    with TickerProviderStateMixin {
  late TabController _controller;
  List<Map<String, dynamic>> data = [
    {
      '_id': '6602c4be86ce88c1d7fc1581',
      'user_name': 'Your Device',
      'user_email': 'SRV-3KLYGI',
      'role': '1.685676',
    },
    {
      '_id': '6602c4be86ce88c1d7fc1581',
      'user_name': 'Your Device',
      'user_email': 'SRV-3KLYGI',
      'role': '2.685676',
    },
    {
      '_id': '6602c4be86ce88c1d7fc15823',
      'user_name': 'Your Device',
      'user_email': 'SRV-3KLYGI',
      'role': '0.685676',
    },
  ];
  String dropDownValue = 'Provisioning';

  @override
  void initState() {
    super.initState();
    log('Oerdetails Email: ${widget.email} : OrderId: ${widget.id}');
    // _controller = TabController(length: 3, vsync: this, initialIndex: 0);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      Provider.of<ManageMerchantProvider>(context, listen: false)
          .getOrderDataDetails(1, 1000, widget.id);
    });
    // WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
    //   Provider.of<ManageMerchantProvider>(context, listen: false)
    //       // .getMerchantDetails(1, 10);
    //       .getOrderDataDetails(1, 1000, widget.email);
    //   // Provider.of<MerchantDetailsProvider>(context, listen: false)
    //   //     .getOrderDataDetails(widget.id);
    // });
  }

  @override
  void dispose() {
    // _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: buildAppBar(context, AppStrings.merchantPage),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SelectableText(
            AppStrings.merchantDetailsMessage,
            style: AppTextStyles.textStyleBold14(context),
          ),
          const SizedBox(
            height: 30,
          ),
          // Row(
          //   children: [
          //     Container(
          //       width: 100,
          //       height: 100,
          //       decoration: BoxDecoration(
          //         borderRadius: BorderRadius.circular(20),
          //         border: Border.all(color: AppColors.kLightGray),
          //       ),
          //     ),
          //     SizedBox(
          //       width: 20,
          //     ),
          //     Consumer<MerchantDetailsProvider>(
          //         builder: (context, provider, child) {
          //       return Column(
          //         crossAxisAlignment: CrossAxisAlignment.start,
          //         children: [
          //           Row(
          //             children: [
          //               Text(provider.merchantDetails!.userName,
          //                   style: AppTextStyles.textStyleBold26(context)),
          //               SizedBox(
          //                 width: 10,
          //               ),
          //               Text(provider.merchantDetails!.status.name,
          //                   style: AppTextStyles.textStyleBold14(context)),
          //               SizedBox(
          //                 width: 4,
          //               ),
          //               Switch.adaptive(
          //                   value: provider.merchantDetails!.status.name ==
          //                           "ACTIVE"
          //                       ? true
          //                       : false,
          //                   activeColor: AppColors.kGreen,
          //                   inactiveThumbColor: AppColors.kBlack,
          //                   thumbColor:
          //                       MaterialStateProperty.all(AppColors.kWhite),
          //                   onChanged: (value) {
          //                     provider
          //                         .toggleMerchant(provider.merchantDetails!.id);
          //                   }),
          //             ],
          //           ),
          //           Text(provider.merchantDetails!.siteName,
          //               style: AppTextStyles.textStyleBold16(context).copyWith(
          //                   color: Provider.of<DarkThemeProvider>(context,
          //                               listen: false)
          //                           .darkTheme
          //                       ? AppColors.kDarkPrimarySwatch
          //                       : AppColors.kLightPrimarySwatch,
          //                   decoration: TextDecoration.underline,
          //                   decorationColor: AppColors.kLightPrimarySwatch)),
          //           Row(
          //             children: [
          //               IconButton(
          //                 onPressed: () {},
          //                 icon: provider.merchantDetails!.hidden
          //                     ? const Icon(Icons.visibility_off_outlined)
          //                     : const Icon(Icons.visibility_outlined),
          //               ),
          //               Text(
          //                 "Hide Merchant",
          //                 style: AppTextStyles.textStyleBold14(context),
          //               ),
          //             ],
          //           )
          //         ],
          //       );
          //     }),
          //     Spacer(),
          //     Container(
          //       height: 100,
          //       padding: EdgeInsets.only(left: 20, right: 20),
          //       decoration: BoxDecoration(
          //         borderRadius: BorderRadius.circular(20),
          //         border: Border.all(color: AppColors.kLightGray),
          //       ),
          //       child: Column(children: [
          //         Text(
          //           "Customers",
          //           style: AppTextStyles.textStyleBold16(context).copyWith(
          //             color:
          //                 Provider.of<DarkThemeProvider>(context, listen: false)
          //                         .darkTheme
          //                     ? AppColors.kDarkPrimarySwatch
          //                     : AppColors.kLightPrimarySwatch,
          //           ),
          //         ),
          //         Row(
          //           children: [
          //             customerCount("12", "Active", AppColors.kGreen),
          //             VerticalDivider(
          //               color: AppColors.kRed,
          //               thickness: 1,
          //             ),
          //             customerCount("5", "Inactive", AppColors.kRed),
          //             VerticalDivider(
          //               color: AppColors.kLightGray,
          //               thickness: 1,
          //             ),
          //             customerCount("2", "Invited", AppColors.kYellow),
          //           ],
          //         )
          //       ]),
          //     ),
          //     SizedBox(
          //       width: 20,
          //     ),
          //   ],
          // ),
          // SizedBox(
          //   height: 100,
          // ),
          // Divider(
          //   color: AppColors.kLightGray,
          //   thickness: 1,
          // ),
          // Expanded(
          //   child: Container(
          //       decoration: BoxDecoration(
          //           borderRadius: BorderRadius.circular(20),
          //           border:
          //               Border.all(color: AppColors.kLightGrayLight, width: 1)),
          //       child: Scrollbar(
          //           controller: ScrollController(),
          //           child: ListView(shrinkWrap: true, children: [
          //             // TabBar(
          //             //     controller: _controller,
          //             //     labelStyle: AppTextStyles.textStyleBold14(context),
          //             //     unselectedLabelStyle:
          //             //         AppTextStyles.textStyleBold14(context),
          //             //     tabs: [
          //             //       Tab(
          //             //         text: AppStrings.allUsers,
          //             //       ),
          //             //       Tab(
          //             //         text: AppStrings.activeUsers,
          //             //       ),
          //             //       Tab(
          //             //         text: AppStrings.inactiveUsers,
          //             //       ),
          //             //     ]),
          //             Consumer<ManageMerchantProvider>(
          //                 builder: (context, provider, child) {
          //               return Padding(
          //                 padding: const EdgeInsets.only(left: 16, right: 16),
          //                 child: PaginatedDataTable(
          //                     dataRowHeight: 200,
          //                     columns: [
          //                       DataColumn(
          //                         label: Text("Order ID",
          //                             style: AppTextStyles.textStyleBold12(
          //                                 context)),
          //                       ),
          //                       DataColumn(
          //                         label: Text("Comment",
          //                             style: AppTextStyles.textStyleBold12(
          //                                 context)),
          //                       ),
          //                       DataColumn(
          //                         label: Text("ShippingAddress",
          //                             style: AppTextStyles.textStyleBold12(
          //                                 context)),
          //                       ),
          //                       DataColumn(
          //                         label: Text("BillingAddress",
          //                             style: AppTextStyles.textStyleBold12(
          //                                 context)),
          //                       ),
          //                       DataColumn(
          //                         label: Text("customerPaid",
          //                             style: AppTextStyles.textStyleBold12(
          //                                 context)),
          //                       ),
          //                       DataColumn(
          //                         label: Text("Purchased Items",
          //                             style: AppTextStyles.textStyleBold12(
          //                                 context)),
          //                       ),
          //                       DataColumn(
          //                         label: Text("Payment Details",
          //                             style: AppTextStyles.textStyleBold12(
          //                                 context)),
          //                       ),
          //                       DataColumn(
          //                         label: Text("Servers",
          //                             style: AppTextStyles.textStyleBold12(
          //                                 context)),
          //                       ),
          //                     ],
          //                     source: OrderSub1Data(
          //                         provider.getOrderDetails, context)),
          //                 // source: MerchantData(
          //                 //     data
          //                 //         .map((e) => MerchantUsers.fromJson(e))
          //                 //         .toList(),
          //                 //     context),
          //               );
          //             })
          //           ]))),
          // )

          Expanded(
            child: Container(
              child: Consumer<ManageMerchantProvider>(
                builder: (context, provider, child) {
                  if (provider.isLoading) {
                    return const Center(child: CupertinoActivityIndicator());
                  }
                  return ListView(
                    shrinkWrap: true,
                    // scrollDirection: Axis.vertical,
                    children: [
                      FadeInUp(
                        duration: const Duration(milliseconds: 500),
                        child: Align(
                          alignment: Alignment.centerLeft,
                          child: Row(
                            children: [
                              SelectableText(
                                // "Order Details - ${provider.getOrderDetails.isNotEmpty ? provider.getOrderDetails[0].customerInfo!.fullName! : ''} ${provider.getOrderDetails[0].servers!.isNotEmpty ? '(${returnHostnames(provider.getOrderDetails[0].servers!)})' : ''}",
                                "Order Details - Assigned Hostnames ${provider.getOrderDetails.isNotEmpty && provider.getOrderDetails[0].servers!.isNotEmpty ? '(${returnHostnames(provider.getOrderDetails[0].servers!)})' : ''}",
                                style: AppTextStyles.textStyleBold16(context),
                              ),
                              provider.getOrderDetails.isNotEmpty &&
                                      provider.getOrderDetails[0].servers!
                                          .isNotEmpty
                                  ? returnHostnamesChecked(
                                            provider
                                                .getOrderDetails[0].servers!,
                                          ) ==
                                          false
                                      ? Icon(
                                          Icons.close,
                                          color: AppColors.kRed,
                                        )
                                      : Icon(
                                          Icons.done,
                                          color: AppColors.kGreen,
                                        )
                                  : Container(),
                            ],
                          ),
                        ),
                      ),
                      provider.getOrderDetails.isNotEmpty
                          ? FadeInUp(
                              duration: const Duration(milliseconds: 500),
                              child: OrdersWidget(provider.getOrderDetails))
                          : noDataWidget(),
                      FadeInUp(
                        duration: const Duration(milliseconds: 500),
                        delay: const Duration(milliseconds: 100),
                        child: Align(
                          alignment: Alignment.centerLeft,
                          child: SelectableText(
                            'Servers',
                            style: AppTextStyles.textStyleBold16(context),
                          ),
                        ),
                      ),
                      provider.getOrderDetails.isNotEmpty &&
                              provider.getOrderDetails[0].servers!.isNotEmpty
                          ? servers(
                              provider.getOrderDetails[0].servers!,
                              provider.getOrderDetails.isNotEmpty
                                  ? provider.getOrderDetails[0].customerInfo!
                                      .fullName!
                                  : '',
                            )
                          : noDataWidget(),
                      const SizedBox(
                        height: 30,
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget customerCount(String count, String status, Color color) {
    return Column(
      children: [
        SelectableText(
          count,
          style: AppTextStyles.textStyleBold22(context).copyWith(color: color),
        ),
        SelectableText(status, style: AppTextStyles.textStyleBold16(context)),
      ],
    );
  }

  Widget noDataWidget() {
    return Padding(
      padding: const EdgeInsets.all(8),
      child: DecoratedBox(
        // height: screenHeight * 0.3,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: AppColors.kLightGrayLight),
        ),
        child: Padding(
          padding: const EdgeInsets.all(50),
          child: SelectableText(
            'No Data',
            style: AppTextStyles.textStyleBold14(context).copyWith(
              color: Provider.of<DarkThemeProvider>(context).darkTheme
                  ? AppColors.kDarkPrimarySwatch
                  : AppColors.kLightPrimarySwatch,
            ),
          ),
        ),
      ),
    );
  }

  Widget OrdersWidget(List<CustomersNew> list) {
    return Padding(
      padding: const EdgeInsets.all(8),
      child: DecoratedBox(
        // height: screenHeight * 0.35,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: AppColors.kLightGrayLight),
        ),
        child: Column(
          children: [
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(
                    height: 20,
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // SizedBox(
                      //   width: 20,
                      // ),
                      retunItemWidet('Order Id', list[0].orderId!, context),

                      retunItemWidet(
                        'Status',
                        list[0].status!,
                        context,
                        list[0],
                      ),
                      retunItemWidet(
                        'Assign Hostname',
                        list[0].status!,
                        context,
                        list[0],
                      ),
                      retunItemWidet(
                        'Quantity',
                        list[0].purchasedItems!.isNotEmpty
                            ? '${list[0].purchasedItems![0].count!}'
                            : '',
                        context,
                      ),
                      retunItemWidet(
                        'Product Name',
                        list[0].purchasedItems!.isNotEmpty
                            ? list[0].purchasedItems![0].productName!
                            : '',
                        context,
                      ),
                      retunItemWidet(
                        'Sub Total Cost',
                        list[0].purchasedItems!.isNotEmpty
                            ? list[0].totals!.subtotal!.string!
                            : '',
                        context,
                      ),
                      retunItemWidet(
                        'Shipping Cost',
                        list[0].purchasedItems!.isNotEmpty
                            ? list[0].totals!.extras![0].price!.string!
                            : '',
                        context,
                      ),
                      // retunItemWidet(
                      //     "Total Cost",
                      //     list[0].purchasedItems!.isNotEmpty
                      //         ? list[0].totals!.total!.string!
                      //         : "",
                      //     context),
                      // retunItemWidet(
                      //     "Payment Type", list[0].paymentProcessor!, context),
                    ],
                  ),
                  const SizedBox(
                    height: 35,
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // SizedBox(
                      //   width: 20,
                      // ),
                      retunItemWidet(
                        'Total Cost',
                        list[0].purchasedItems!.isNotEmpty
                            ? list[0].totals!.total!.string!
                            : '',
                        context,
                      ),
                      retunItemWidet(
                        'Payment Type',
                        list[0].paymentProcessor!,
                        context,
                      ),
                      retunItemWidet(
                        'Shipping Address',
                        '${list[0].shippingAddress!.addressee!} \n${list[0].shippingAddress!.line1!} \n${list[0].shippingAddress!.city!}, ${list[0].shippingAddress!.state}, ${list[0].shippingAddress!.country!} ${list[0].shippingAddress!.postalCode!}',
                        context,
                      ),
                      retunItemWidet(
                        'Billing Address',
                        '${list[0].billingAddress!.addressee!} \n${list[0].billingAddress!.line1!} \n${list[0].billingAddress!.city!}, ${list[0].billingAddress!.state}, ${list[0].billingAddress!.country!} ${list[0].billingAddress!.postalCode!}',
                        context,
                      ),
                      // SelectableText(
                      //     list[0].purchasedItems!.isNotEmpty
                      //         ? "Shipping Address: \n${list[0].shippingAddress!.addressee!} \n${list[0].shippingAddress!.line1!} \n${list[0].shippingAddress!.city!}, ${list[0].shippingAddress!.state}, ${list[0].shippingAddress!.country!} ${list[0].shippingAddress!.postalCode!}"
                      //         : "",
                      //     style: AppTextStyles.textStyleBold14(context)),
                      // SizedBox(
                      //   width: 100,
                      // ),
                      // SelectableText(
                      //     list[0].purchasedItems!.isNotEmpty
                      //         ? "Billing Address: \n${list[0].billingAddress!.addressee!} \n${list[0].billingAddress!.line1!} \n${list[0].billingAddress!.city!}, ${list[0].billingAddress!.state}, ${list[0].billingAddress!.country!} ${list[0].billingAddress!.postalCode!}"
                      //         : "",
                      //     style: AppTextStyles.textStyleBold14(context)),
                    ],
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget servers(List<Servers> items, String name) {
    return Padding(
      padding: const EdgeInsets.all(8),
      child: DecoratedBox(
        // height: screenHeight * 0.3,
        // height: 200,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: AppColors.kLightGrayLight),
        ),
        child: Padding(
          padding: const EdgeInsets.all(8),
          // child: Scrollbar(
          //   controller: ScrollController(),
          child: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              physics: const BouncingScrollPhysics(),
              shrinkWrap: true,
              itemCount: items.isEmpty ? 1 : items.length + 1,
              itemBuilder: (context, index) {
                if (index == 0) {
                  // return the header
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 20),
                    child: Row(
                      children: [
                        // SizedBox(
                        //   width: 20,
                        // ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            '#',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'Full Name',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'Model',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'Server ID',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        // SizedBox(
                        //   width: 100,
                        // ),

                        // SizedBox(
                        //   width: 100,
                        // ),

                        // SizedBox(
                        //   width: 100,
                        // ),

                        // SizedBox(
                        //   width: 100,
                        // ),

                        // SizedBox(
                        //   width: 100,
                        // ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'Host Name',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'Hostname Assigned',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'Device Status',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'Support Status',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        // SizedBox(
                        //   width: 100,
                        // ),
                      ],
                    ),
                  );
                }
                index -= 1;

                // return row
                final row = items[index];
                return Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // SizedBox(
                    //   width: 20,
                    // ),
                    // Expanded(
                    //   child: SelectableText(
                    //     "${row.nft!.tokenId}",
                    //     style: AppTextStyles.textStyleRegular16(context),
                    //   ),
                    // ),
                    // SizedBox(
                    //   width: 50,
                    // ),
                    // Expanded(
                    //   child: Padding(
                    //     padding: const EdgeInsets.only(
                    //         right: 30.0, top: 8, bottom: 8),
                    //     child: Container(
                    //       width: 100,
                    //       height: 50,
                    //       child: ClipRRect(
                    //         borderRadius: BorderRadius.all(
                    //           Radius.circular(16),
                    //         ),
                    //         child: CachedNetworkImage(
                    //           width: 100,
                    //           height: 50,
                    //           imageUrl: row.nft!.imageUrl!,
                    //           fit: BoxFit.fill,
                    //         ),
                    //       ),
                    //     ),
                    //   ),
                    // ),
                    // // SelectableText(
                    // //   row.nft!.imageUrl!,
                    // //   style: AppTextStyles.textStyleRegular16(context),
                    // //   // style: AppTextStyles.textStyleBold14(context).copyWith(
                    // //   //     color: Provider.of<DarkThemeProvider>(context).darkTheme
                    // //   //         ? AppColors.kDarkPrimarySwatch
                    // //   //         : AppColors.kLightPrimarySwatch),
                    // // )
                    // // SizedBox(
                    // //   width: 50,
                    // // ),
                    Expanded(
                      // width: screenWidth * 0.29,
                      // height: (56 / 800) * screenHeight,
                      child: Align(
                        child: AutoSizeText(
                          '${index + 1}',
                          style: AppTextStyles.textStyleRegular16(context),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          // stepGranularity: 12,
                          // presetFontSizes: [16, 14, 12],
                        ),
                      ),
                    ),
                    Expanded(
                      // width: screenWidth * 0.29,
                      // height: (56 / 800) * screenHeight,
                      child: Align(
                        child: AutoSizeText(
                          name,
                          style: AppTextStyles.textStyleRegular16(context),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          // stepGranularity: 12,
                          // presetFontSizes: [16, 14, 12],
                        ),
                      ),
                    ),
                    // Expanded(
                    //   child: SelectableText(
                    //     textAlign: TextAlign.center,
                    //     row.serverID!,
                    //     style: AppTextStyles.textStyleRegular16(context),
                    //   ),
                    // ),
                    // SizedBox(
                    //   width: 50,
                    // ),
                    Expanded(
                      child: SelectableText(
                        textAlign: TextAlign.center,
                        row.model!,
                        // HelperFunctions.convertToDate(row.createdAt!),
                        style: AppTextStyles.textStyleRegular16(context),
                      ),
                    ),
                    // SizedBox(
                    //   width: 100,
                    // ),
                    Expanded(
                      child: SelectableText(
                        textAlign: TextAlign.center,
                        row.serverID!,
                        // "${HelperFunctions.formatAmountTwoDecimals(row.totalEarning!)}",
                        style: AppTextStyles.textStyleRegular16(context),
                      ),
                    ),
                    // SizedBox(
                    //   width: 100,
                    // ),

                    // SizedBox(
                    //   width: 100,
                    // ),
                    Expanded(
                      child: SelectableText(
                        textAlign: TextAlign.center,
                        row.hostName!,
                        // HelperFunctions.formatAmountTwoDecimals(
                        //     row.totalEarningDailyUsagePay!),
                        style: AppTextStyles.textStyleRegular16(context),
                      ),
                    ),
                    // SizedBox(
                    //   width: 100,
                    // ),
                    Expanded(
                      child: row.hostName!.isEmpty
                          ? Icon(
                              Icons.close,
                              color: AppColors.kRed,
                            )
                          : Icon(
                              Icons.done,
                              color: AppColors.kGreen,
                            ),
                    ),
                    row.hostName!.isEmpty
                        ? Expanded(
                            child: SelectableText(
                              textAlign: TextAlign.center,
                              'N/A',
                              // row.hostName!,
                              // HelperFunctions.formatAmountTwoDecimals(
                              //     row.totalEarningDailyUsagePay!),
                              style: AppTextStyles.textStyleRegular16(context),
                            ),
                          )
                        : Expanded(
                            child: SelectableText(
                              textAlign: TextAlign.center,
                              row.isOnline! == true ? 'Online' : 'Offline',
                              // HelperFunctions.formatAmountTwoDecimals(
                              //     row.totalEarningConnectionPay!),
                              style: AppTextStyles.textStyleRegular16(context)
                                  .copyWith(
                                color: row.isOnline == true
                                    ? AppColors.kGreen
                                    : AppColors.kRed,
                              ),
                            ),
                          ),
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SelectableText(
                            textAlign: TextAlign.center,
                            _formatSupportStatus(row.supportStatus),
                            style: AppTextStyles.textStyleRegular14(context),
                            maxLines: 2,
                          ),

                          ///This needs to be uncommented once support status changes are deployed into Production - Do Not Remove code below
                          // IconButton(
                          //   onPressed: () {
                          //     _showSupportStatusDialog(context, row.serverID!,
                          //         row.supportStatus ?? '');
                          //   },
                          //   icon: Icon(
                          //     Iconsax.edit,
                          //     size: 24,
                          //     color: Provider.of<DarkThemeProvider>(context)
                          //             .darkTheme
                          //         ? AppColors.kDarkPrimarySwatch
                          //         : AppColors.kLightPrimarySwatch,
                          //   ),
                          // ),
                        ],
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Row retunItemWidet(
    String heading,
    String row,
    BuildContext context, [
    CustomersNew? list,
  ]) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SelectableText(
              // "\$${HelperFunctions.formatAmountTwoDecimals(row.nxqEarningsInUsd!)}",
              heading,
              style: AppTextStyles.textStyleBold14(context).copyWith(
                color: Provider.of<DarkThemeProvider>(context).darkTheme
                    ? AppColors.kDarkPrimarySwatch
                    : AppColors.kLightPrimarySwatch,
              ),
              // style: AppTextStyles.textStyleBold16(context)
              //     .copyWith(color: Colors.grey),
            ),
            // const SizedBox(
            //   height: 10,
            //   width: 100,
            // ),

            heading == 'Status'
                ? Row(
                    children: [
                      SelectableText(
                        list!.status!.isNotEmpty
                            ? HelperFunctions.returnStatus(list.status!)
                            : '',
                        style: AppTextStyles.textStyleBold14(context).copyWith(
                          color:
                              Provider.of<DarkThemeProvider>(context).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                        ),
                        // style: AppTextStyles.textStyleBlueBold(context).copyWith(
                        // color: getStatusColor(data[index].orders![0].status!),
                      ),
                      list.orderId!.isNotEmpty
                          ? IconButton(
                              onPressed: () {
                                // log("Country Code: ${data[index].phone!.countryCode!}  ::: number: ${data[index].phone!.number!}");

                                // Provider.of<MyTeamProvider>(context, listen: false)
                                //     .deleteUserDialog(context, data[index].id);
                                // phoneController.text = data[index].phone!.number!;
                                Provider.of<MyTeamProvider>(
                                  context,
                                  listen: false,
                                ).editOrderDialog(
                                  context,
                                  list.servers!.isNotEmpty
                                      ? list.servers![0].userId!
                                      : '',
                                  // "",
                                  list.orderId!,
                                  '',
                                  '',
                                  '',
                                  '',
                                  '',
                                  TextEditingController(),
                                  FocusNode(),
                                  list.servers!,
                                  list.hostNameForDropdown!,
                                  list.status!.isEmpty
                                      ? dropDownValue
                                      : (list.status! ==
                                                  AppStrings.Provisioning ||
                                              list.status! ==
                                                  AppStrings.Configuring ||
                                              list.status! ==
                                                  AppStrings.QA_Testing ||
                                              list.status! ==
                                                  AppStrings.QATesting ||
                                              list.status! ==
                                                  AppStrings.PackAndShip ||
                                              list.status! ==
                                                  AppStrings.PackShip ||
                                              list.status! == AppStrings.Shipped
                                          // ||
                                          // list.status! ==
                                          //     AppStrings.Received
                                          )
                                          ? list.status!
                                          : list.status! == AppStrings.Received
                                              ? AppStrings.Shipped
                                              : dropDownValue,
                                  true,
                                );
                              },
                              icon: Icon(
                                Iconsax.edit,
                                color:
                                    // Colors.blue
                                    Provider.of<DarkThemeProvider>(context)
                                            .darkTheme
                                        ? AppColors.kDarkPrimarySwatch
                                        : AppColors.kLightPrimarySwatch,
                              ),
                            )
                          : Container(),
                    ],
                  )
                : heading == 'Assign Hostname'
                    ? Row(
                        children: [
                          SelectableText(
                            '          ',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(context)
                                      .darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                            // style: AppTextStyles.textStyleBlueBold(context).copyWith(
                            // color: getStatusColor(data[index].orders![0].status!),
                          ),
                          // list!.orderId!.isNotEmpty
                          //     ?
                          IconButton(
                            onPressed: () {
                              // log("Country Code: ${data[index].phone!.countryCode!}  ::: number: ${data[index].phone!.number!}");

                              // Provider.of<MyTeamProvider>(context, listen: false)
                              //     .deleteUserDialog(context, data[index].id);
                              // phoneController.text = data[index].phone!.number!;
                              Provider.of<MyTeamProvider>(
                                context,
                                listen: false,
                              ).editHostNameDialog(
                                context,
                                list!.servers!.isNotEmpty
                                    ? list.servers![0].userId!
                                    : '',
                                // "",
                                list.orderId!,
                                '',
                                '',
                                '',
                                '',
                                '',
                                TextEditingController(),
                                FocusNode(),
                                list.servers!,
                                list.hostNameForDropdown!,
                                list.status!.isEmpty
                                    ? dropDownValue
                                    : (list.status! ==
                                                AppStrings.Provisioning ||
                                            list.status! ==
                                                AppStrings.Configuring ||
                                            list.status! ==
                                                AppStrings.QA_Testing ||
                                            list.status! ==
                                                AppStrings.QATesting ||
                                            list.status! ==
                                                AppStrings.PackAndShip ||
                                            list.status! ==
                                                AppStrings.PackShip ||
                                            list.status! == AppStrings.Shipped
                                        // ||
                                        // list.status! ==
                                        //     AppStrings.Received
                                        )
                                        ? list.status!
                                        : list.status! == AppStrings.Received
                                            ? AppStrings.Shipped
                                            : dropDownValue,
                                true,
                              );
                            },
                            icon: Icon(
                              Iconsax.edit,
                              color:
                                  // Colors.blue
                                  Provider.of<DarkThemeProvider>(context)
                                          .darkTheme
                                      ? AppColors.kDarkPrimarySwatch
                                      : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                          // : Container()
                        ],
                      )
                    : SelectableText(
                        // textAlign: TextAlign.center,
                        row,
                        // HelperFunctions.convertToDate(row.date!),
                        style: AppTextStyles.textStyleBold14(context),
                      ),
          ],
        ),
        const SizedBox(
          width: 50,
        ),
      ],
    );
  }

  String returnHostnames(List<Servers> servers) {
    var count = 0;
    var ishostnameSelected = '$count/${servers.length}';
    for (var i = 0; i < servers.length; i++) {
      // log('Server ID: ${servers[i].serverID}: Hostname:${servers[i].hostName}');
      if (servers[i].hostName!.isNotEmpty) {
        count = count + 1;
        // ishostnameSelected = false;
        // log('return hostnames: pos:$i ID: ${servers[i].serverID} value; $ishostnameSelected');
        // return ishostnameSelected;
      }
    }
    ishostnameSelected = '$count/${servers.length}';
    return ishostnameSelected;
  }

  bool returnHostnamesChecked(List<Servers> servers) {
    var ishostnameSelected = true;
    for (var i = 0; i < servers.length; i++) {
      // log('Server ID: ${servers[i].serverID}: Hostname:${servers[i].hostName}');
      if (servers[i].hostName!.isEmpty) {
        ishostnameSelected = false;
        // log('return hostnames: pos:$i ID: ${servers[i].serverID} value; $ishostnameSelected');
        return ishostnameSelected;
      }
    }
    return ishostnameSelected;
  }

  String _formatSupportStatus(String? status) {
    if (status == null || status.isEmpty) {
      return 'Not Set';
    }
    switch (status) {
      case 'ISSUE_ACKNOWLEDGED':
        return 'Issue Acknowledged';
      case 'ISSUE_RESOLVED':
        return 'Issue Resolved';
      default:
        return status;
    }
  }

  void _showSupportStatusDialog(
      BuildContext context, String serverID, String currentStatus) {
    var selectedStatus = currentStatus.isEmpty || currentStatus == 'Not Set'
        ? 'ISSUE_ACKNOWLEDGED'
        : currentStatus;
    var isLoading = false;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Stack(
              children: [
                AlertDialog(
                  title: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Update Support Status',
                        style: AppTextStyles.textStyleBold26(context),
                      ),
                      IconButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),
                  content: SizedBox(
                    width: 350,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Server ID: $serverID',
                          style: AppTextStyles.textStyleBold14(context),
                        ),
                        const SizedBox(height: 20),
                        Text(
                          'Select Support Status:',
                          style: AppTextStyles.textStyleBold14(context),
                        ),
                        const SizedBox(height: 10),
                        StatefulBuilder(
                          builder: (context, setState) {
                            return Column(
                              children: [
                                RadioListTile<String>(
                                  title: const Text('Issue Acknowledged'),
                                  value: 'ISSUE_ACKNOWLEDGED',
                                  groupValue: selectedStatus,
                                  onChanged: (value) {
                                    setState(() {
                                      selectedStatus = value!;
                                    });
                                  },
                                ),
                                RadioListTile<String>(
                                  title: const Text('Issue Resolved'),
                                  value: 'ISSUE_RESOLVED',
                                  groupValue: selectedStatus,
                                  onChanged: (value) {
                                    setState(() {
                                      selectedStatus = value!;
                                    });
                                  },
                                ),
                              ],
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                  actions: [
                    TextButton(
                      onPressed: isLoading
                          ? null
                          : () {
                              Navigator.pop(context);
                            },
                      child: Text(
                        'Cancel',
                        style: AppTextStyles.textStyleBold14(context).copyWith(
                          color: AppColors.kRed,
                        ),
                      ),
                    ),
                    ElevatedButton(
                      onPressed: isLoading
                          ? null
                          : () async {
                              setState(() {
                                isLoading = true;
                              });
                              try {
                                await Provider.of<ManageMerchantProvider>(
                                        context,
                                        listen: false)
                                    .updateSupportStatus(
                                        selectedStatus, serverID);

                                //Update Status locally
                                updateStatusLocally(
                                  serverID: serverID,
                                  selectedStatus: selectedStatus,
                                );
                                Navigator.pop(context);
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: const Text(
                                        'Support status updated successfully'),
                                    backgroundColor: AppColors.kGreen,
                                  ),
                                );
                              } catch (error) {
                                setState(() {
                                  isLoading = false;
                                });

                                var errorMessage =
                                    'Failed to update support status';

                                // Handle specific network connectivity issues
                                if (error is SocketException) {
                                  errorMessage =
                                      'No internet connection. Please check your network and try again.';
                                } else if (error is TimeoutException) {
                                  errorMessage =
                                      'Request timed out. Please check your internet connection and try again.';
                                } else if (error is HttpException) {
                                  errorMessage =
                                      'HTTP error occurred. Please try again.';
                                } else if (error is http.ClientException) {
                                  errorMessage =
                                      'Network error. Please check your internet connection and try again.';
                                } else if (error is Map<String, dynamic>) {
                                  errorMessage = error['message'] ??
                                      'Failed to update support status';
                                } else {
                                  // For any other error, show a generic message without technical details
                                  errorMessage =
                                      'Failed to update support status. Please check your connection and try again.';
                                }

                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(errorMessage),
                                    backgroundColor: AppColors.kRed,
                                  ),
                                );
                              }
                            },
                      child: isLoading
                          ? SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                    AppColors.kWhite),
                              ),
                            )
                          : Text(
                              'Update Status',
                              style: AppTextStyles.textStyleBold14(context)
                                  .copyWith(
                                color: AppColors.kWhite,
                              ),
                            ),
                    ),
                  ],
                ),
                if (isLoading) LoadingOverlay(isLoading: isLoading),
              ],
            );
          },
        );
      },
    );
  }

  void updateStatusLocally(
      {required String serverID, required String selectedStatus}) {
    // Update local model after successful API call
    final provider = context.read<ManageMerchantProvider>();

    final orders = provider.getOrderDetails;
    if (orders.isEmpty) return;

    final servers = orders.first.servers;
    if (servers == null || servers.isEmpty) return;

    final idx = servers.indexWhere((s) => s.serverID == serverID);
    if (idx == -1) return;

    servers[idx].supportStatus = selectedStatus;
    setState(() {});
  }
}
