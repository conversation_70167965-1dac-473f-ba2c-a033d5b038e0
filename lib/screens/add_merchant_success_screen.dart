import 'package:admin_portal/configs/configs_export.dart';
import 'package:admin_portal/provider/provider_export.dart';
import 'package:admin_portal/utils/utils_export.dart';
import 'package:admin_portal/widgets/widgets_export.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class AddMerchantSuccessScreen extends StatelessWidget {
  const AddMerchantSuccessScreen({
    super.key,
    required this.name,
    required this.email,
  });
  final String name;
  final String email;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      // appBar: buildAppBar(context, AppStrings.addNewMerchant),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Image(
                image: AssetImage(ImageConstants.addMerchantSuccessImage),
              ),
              Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    AppStrings.addNewMerchantInviteSuccessMessage,
                    style: AppTextStyles.textStyleDashboardTitle(context),
                  ),
                  RichText(
                    text: TextSpan(
                      text: 'to $name with email',
                      style: AppTextStyles.textStyleBold14(context),
                      children: <TextSpan>[
                        TextSpan(
                          text: ' $email',
                          style: AppTextStyles.textStyleBlueBold(context),
                        ),
                      ],
                    ),
                  ),
                  Text(
                    AppStrings.addNewMerchantSuccessInformMessage,
                    style: AppTextStyles.textStyleDashboardMessage(context),
                  ),
                ],
              ),
            ],
          ),
          CustomListTile(
            title: AppStrings.addMerchant,
            tileColor:
                Provider.of<DarkThemeProvider>(context, listen: false).darkTheme
                    ? AppColors.kDarkPrimarySwatch
                    : AppColors.kLightPrimarySwatch,
            isDarkTheme: Provider.of<DarkThemeProvider>(context, listen: false)
                .darkTheme,
            leadingButtonColor: AppColors.kWhite,
            onTap: () {
              Provider.of<AddMerchantProvider>(context, listen: false)
                  .addMerchantBack();
            },
          ),
        ],
      ),
    );
  }
}
