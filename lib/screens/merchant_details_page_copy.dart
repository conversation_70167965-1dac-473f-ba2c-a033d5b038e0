import 'dart:developer';

import 'package:admin_portal/configs/configs_export.dart';
import 'package:admin_portal/models/models_export.dart';
import 'package:admin_portal/provider/provider_export.dart';
import 'package:admin_portal/utils/utils_export.dart';
import 'package:admin_portal/widgets/widgets_export.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class MerchantDetailsPage extends StatefulWidget {
  const MerchantDetailsPage({super.key, required this.id, required this.email});
  final String id, email;

  @override
  State<MerchantDetailsPage> createState() => _MerchantDetailsPageState();
}

class _MerchantDetailsPageState extends State<MerchantDetailsPage>
    with TickerProviderStateMixin {
  late TabController _controller;
  List<Map<String, dynamic>> data = [
    {
      '_id': '6602c4be86ce88c1d7fc1581',
      'user_name': 'Your Device',
      'user_email': 'SRV-3KLYGI',
      'role': '1.685676',
    },
    {
      '_id': '6602c4be86ce88c1d7fc1581',
      'user_name': 'Your Device',
      'user_email': 'SRV-3KLYGI',
      'role': '2.685676',
    },
    {
      '_id': '6602c4be86ce88c1d7fc15823',
      'user_name': 'Your Device',
      'user_email': 'SRV-3KLYGI',
      'role': '0.685676',
    },
  ];

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _controller = TabController(length: 3, vsync: this);
    // WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
    //   Provider.of<MerchantDetailsProvider>(context, listen: false)
    //       .getMerchantDetails(widget.id);
    // });
    log('Support Details Email: ${widget.email}');
    // _controller = TabController(length: 3, vsync: this, initialIndex: 0);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      Provider.of<ManageMerchantProvider>(context, listen: false)
          .getSupportDetailsData(1, 1000, widget.id);
    });
  }

  @override
  void dispose() {
    // TODO: implement dispose
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: buildAppBar(context, AppStrings.merchantPage),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppStrings.merchantDetailsMessage,
            style: AppTextStyles.textStyleBold14(context),
          ),
          const SizedBox(
            height: 20,
          ),
          // Row(
          //   children: [
          //     Container(
          //       width: 100,
          //       height: 100,
          //       decoration: BoxDecoration(
          //         borderRadius: BorderRadius.circular(20),
          //         border: Border.all(color: AppColors.kLightGray),
          //       ),
          //     ),
          //     SizedBox(
          //       width: 20,
          //     ),
          //     Consumer<MerchantDetailsProvider>(
          //         builder: (context, provider, child) {
          //       return Column(
          //         crossAxisAlignment: CrossAxisAlignment.start,
          //         children: [
          //           Row(
          //             children: [
          //               Text(provider.merchantDetails!.userName,
          //                   style: AppTextStyles.textStyleBold26(context)),
          //               SizedBox(
          //                 width: 10,
          //               ),
          //               Text(provider.merchantDetails!.status.name,
          //                   style: AppTextStyles.textStyleBold14(context)),
          //               SizedBox(
          //                 width: 4,
          //               ),
          //               Switch.adaptive(
          //                   value: provider.merchantDetails!.status.name ==
          //                           "ACTIVE"
          //                       ? true
          //                       : false,
          //                   activeColor: AppColors.kGreen,
          //                   inactiveThumbColor: AppColors.kBlack,
          //                   thumbColor:
          //                       MaterialStateProperty.all(AppColors.kWhite),
          //                   onChanged: (value) {
          //                     provider
          //                         .toggleMerchant(provider.merchantDetails!.id);
          //                   }),
          //             ],
          //           ),
          //           Text(provider.merchantDetails!.siteName,
          //               style: AppTextStyles.textStyleBold16(context).copyWith(
          //                   color: Provider.of<DarkThemeProvider>(context,
          //                               listen: false)
          //                           .darkTheme
          //                       ? AppColors.kDarkPrimarySwatch
          //                       : AppColors.kLightPrimarySwatch,
          //                   decoration: TextDecoration.underline,
          //                   decorationColor: AppColors.kLightPrimarySwatch)),
          //           Row(
          //             children: [
          //               IconButton(
          //                 onPressed: () {},
          //                 icon: provider.merchantDetails!.hidden
          //                     ? const Icon(Icons.visibility_off_outlined)
          //                     : const Icon(Icons.visibility_outlined),
          //               ),
          //               Text(
          //                 "Hide Merchant",
          //                 style: AppTextStyles.textStyleBold14(context),
          //               ),
          //             ],
          //           )
          //         ],
          //       );
          //     }),
          //     Spacer(),
          //     Container(
          //       height: 100,
          //       padding: EdgeInsets.only(left: 20, right: 20),
          //       decoration: BoxDecoration(
          //         borderRadius: BorderRadius.circular(20),
          //         border: Border.all(color: AppColors.kLightGray),
          //       ),
          //       child: Column(children: [
          //         Text(
          //           "Customers",
          //           style: AppTextStyles.textStyleBold16(context).copyWith(
          //             color:
          //                 Provider.of<DarkThemeProvider>(context, listen: false)
          //                         .darkTheme
          //                     ? AppColors.kDarkPrimarySwatch
          //                     : AppColors.kLightPrimarySwatch,
          //           ),
          //         ),
          //         Row(
          //           children: [
          //             customerCount("12", "Active", AppColors.kGreen),
          //             VerticalDivider(
          //               color: AppColors.kRed,
          //               thickness: 1,
          //             ),
          //             customerCount("5", "Inactive", AppColors.kRed),
          //             VerticalDivider(
          //               color: AppColors.kLightGray,
          //               thickness: 1,
          //             ),
          //             customerCount("2", "Invited", AppColors.kYellow),
          //           ],
          //         )
          //       ]),
          //     ),
          //     SizedBox(
          //       width: 20,
          //     ),
          //   ],
          // ),
          const SizedBox(
            height: 100,
          ),
          Divider(
            color: AppColors.kLightGray,
            thickness: 1,
          ),
          Expanded(
            child: DecoratedBox(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: AppColors.kLightGrayLight),
              ),
              child: Scrollbar(
                controller: ScrollController(),
                child: ListView(
                  shrinkWrap: true,
                  children: [
                    // TabBar(
                    //     controller: _controller,
                    //     labelStyle: AppTextStyles.textStyleBold14(context),
                    //     unselectedLabelStyle:
                    //         AppTextStyles.textStyleBold14(context),
                    //     tabs: [
                    //       Tab(
                    //         text: AppStrings.allUsers,
                    //       ),
                    //       Tab(
                    //         text: AppStrings.activeUsers,
                    //       ),
                    //       Tab(
                    //         text: AppStrings.inactiveUsers,
                    //       ),
                    //     ]),
                    Consumer<ManageMerchantProvider>(
                      builder: (context, provider, child) {
                        return Padding(
                          padding: const EdgeInsets.only(left: 16, right: 16),
                          child: PaginatedDataTable(
                            dataRowHeight: 300,
                            columns: [
                              DataColumn(
                                label: Text(
                                  AppStrings.userId,
                                  style: AppTextStyles.textStyleBold12(
                                    context,
                                  ),
                                ),
                              ),
                              DataColumn(
                                label: Text(
                                  'NFTS',
                                  style: AppTextStyles.textStyleBold12(
                                    context,
                                  ),
                                ),
                              ),
                              DataColumn(
                                label: Text(
                                  'SERVERS',
                                  style: AppTextStyles.textStyleBold12(
                                    context,
                                  ),
                                ),
                              ),
                              DataColumn(
                                label: Text(
                                  'ORDERS',
                                  style: AppTextStyles.textStyleBold12(
                                    context,
                                  ),
                                ),
                              ),
                              DataColumn(
                                label: Text(
                                  'ADDRESS',
                                  style: AppTextStyles.textStyleBold12(
                                    context,
                                  ),
                                ),
                              ),
                            ],
                            source: MerchantData(
                              provider.getSupportDetails,
                              context,
                            ),
                            // source: MerchantData(
                            //     data
                            //         .map((e) => MerchantUsers.fromJson(e))
                            //         .toList(),
                            //     context),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget customerCount(String count, String status, Color color) {
    return Column(
      children: [
        Text(
          count,
          style: AppTextStyles.textStyleBold22(context).copyWith(color: color),
        ),
        Text(status, style: AppTextStyles.textStyleBold16(context)),
      ],
    );
  }
}
