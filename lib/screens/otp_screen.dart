import 'package:admin_portal/configs/configs_export.dart';
import 'package:admin_portal/provider/provider_export.dart';
import 'package:admin_portal/widgets/widgets_export.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class OtpScreen extends StatefulWidget {
  const OtpScreen({super.key, required this.userId});
  final String userId;
  @override
  State<OtpScreen> createState() => _OtpScreenState();
}

class _OtpScreenState extends State<OtpScreen> {
  @override
  void initState() {
    super.initState();
    // Start the timer for resend functionality
    Future.delayed(const Duration(microseconds: 2), () {
      Provider.of<AuthProvider>(context, listen: false).startTimer();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
        onPressed: () {
          Navigator.of(context).pop();
        },
        ),
        title: const Text('OTP Verification'),
        centerTitle: true,
      ),
      body: Container(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Enter OTP',
                  style: AppTextStyles.textStyleDashboardTitle(context).copyWith(
                    color: AppColors.kLightPrimarySwatch,
                  ),
                ),
                const SizedBox(height: 20),
                Text(
                  'Enter the 4-digit code sent to your phone number',
                  style: AppTextStyles.textStyleDashboardMessage(context),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 40),
                Consumer<AuthProvider>(
                  builder: (context, auth, child) {
                    return Column(
                      children: [
                        CustomOtpField(
                          autoFocus: true,
                          onCompleted: (value) {
                            auth.verifyOtp(context, widget.userId, value);
                          },
                        ),
                        const SizedBox(height: 20),
                        // Resend button
                        TextButton(
                          onPressed: auth.isResendButtonDisabled
                              ? null
                              : () {
                                  // TODO: Implement resend OTP functionality
                                },
                          child: Text(
                            auth.isResendButtonDisabled
                                ? auth.seconds < 10
                                    ? 'Resend in 00:0${auth.seconds}'
                                    : 'Resend in 00:${auth.seconds}'
                                : 'Resend OTP',
                            style: AppTextStyles.textStyleDashboardMessage(context)
                                .copyWith(
                              color: auth.isResendButtonDisabled
                                  ? Colors.grey
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        const SizedBox(height: 20),
                        // Error message
                        Visibility(
                          visible: auth.isApiError,
                          child: Text(
                            auth.apiErrorMessage,
                            style: AppTextStyles.textStyleDashboardMessage(context)
                                .copyWith(color: Colors.red),
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
