import 'package:admin_portal/configs/configs_export.dart';
import 'package:admin_portal/provider/provider_export.dart';
import 'package:admin_portal/utils/utils_export.dart';
import 'package:admin_portal/widgets/widgets_export.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class MerchantOnbardingSuccessScreen extends StatelessWidget {
  const MerchantOnbardingSuccessScreen({super.key, required this.siteName});
  final String siteName;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: Row(
        children: [
          Drawer(
            backgroundColor: Theme.of(context).colorScheme.surface,
            child: Container(
              color: Theme.of(context).colorScheme.surface,
              child: Column(
                children: [
                  Image.asset(
                    ImageConstants.payNationTag,
                  ),
                ],
              ),
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                buildAppBar(context, AppStrings.merchantOnboardingMessage),
                const Spacer(),
                Row(
                  children: [
                    const Image(
                      image: AssetImage(ImageConstants.addMerchantSuccessImage),
                    ),
                    const SizedBox(
                      width: 20,
                    ),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppStrings.merchantOnboardSuccessMessage,
                          style: AppTextStyles.textStyleDashboardTitle(context)
                              .copyWith(color: AppColors.kLightPrimarySwatch),
                        ),
                        Text(
                          AppStrings.merchantOnboardSuccessInformMessage,
                          style: AppTextStyles.textStyleDashboardTitle(context)
                              .copyWith(color: AppColors.kLightPrimarySwatch),
                        ),
                        RichText(
                          text: TextSpan(
                            text: 'Your unique site can be accessed via',
                            style: AppTextStyles.textStyleBold14(context),
                            children: <TextSpan>[
                              TextSpan(
                                text: ' $siteName',
                                style: AppTextStyles.textStyleBlueBold(context),
                              ),
                            ],
                          ),
                        ),
                        Text(
                          AppStrings.merchantOnboardSuccessInformMessage2,
                          style:
                              AppTextStyles.textStyleDashboardMessage(context),
                        ),
                      ],
                    ),
                  ],
                ),
                const Spacer(),
                CustomListTile(
                  title: AppStrings.takeMeThere,
                  tileColor:
                      Provider.of<DarkThemeProvider>(context, listen: false)
                              .darkTheme
                          ? AppColors.kDarkPrimarySwatch
                          : AppColors.kLightPrimarySwatch,
                  isDarkTheme:
                      Provider.of<DarkThemeProvider>(context, listen: false)
                          .darkTheme,
                  leadingButtonColor: AppColors.kWhite,
                  showIcon: false,
                  onTap: () {},
                ),
                const Spacer(),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
