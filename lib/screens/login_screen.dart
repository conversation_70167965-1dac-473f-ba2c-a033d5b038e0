import 'package:admin_portal/configs/configs_export.dart';
import 'package:admin_portal/provider/provider_export.dart';
import 'package:admin_portal/utils/utils_export.dart';
import 'package:admin_portal/widgets/widgets_export.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final formKeyLogin = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    // Clear any existing data
    Provider.of<AuthProvider>(context, listen: false).clearControllers();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: Container(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Logo and title
                Column(
                  children: [
                    Image.asset(
                      ImageConstants.payNationTag,
                      color: AppColors.kLightPrimarySwatch,
                    ),
                    const SizedBox(height: 25),
                    Text(
                      AppStrings.loginText,
                      style: AppTextStyles.textStyleDashboardTitle(context)
                          .copyWith(
                        color: AppColors.kLightPrimarySwatch,
                      ),
                    ),
                    Text(
                      AppStrings.loginTextUsage,
                      style: AppTextStyles.textStyleDashboardTitle(context)
                          .copyWith(
                        color: AppColors.kLightPrimarySwatch,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 40),
                // Login form
                SizedBox(
                  width: MediaQuery.of(context).size.width * 0.5,
                  child: Consumer<AuthProvider>(
                    builder: (context, auth, child) {
                      return Form(
                        key: formKeyLogin,
                        autovalidateMode: AutovalidateMode.disabled,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Phone number input
                            Text(
                              'Phone Number',
                              style: AppTextStyles.textStyleDashboardMessage(
                                context,
                              ).copyWith(
                                color: AppColors.kLightPrimarySwatch,
                              ),
                            ),
                            const SizedBox(height: 10),
                            CustomMobileInput(
                              key: const Key('mobile login Input'),
                              phoneController: auth.loginPhoneController,
                              countryCodeController:
                                  auth.loginCountryCodeController,
                              countryCodeStringController:
                                  auth.countryCodeStringController,
                              onChanged: (value) {
                                auth.countryCodeStringController.text = value;
                                if (auth.isLoginError) {
                                  auth.clearAllErrors();
                                }
                              },
                              enabled: true,
                            ),
                            const SizedBox(height: 20),
                            // Password input
                            Text(
                              'Password',
                              style: AppTextStyles.textStyleDashboardMessage(
                                context,
                              ).copyWith(
                                color: AppColors.kLightPrimarySwatch,
                              ),
                            ),
                            const SizedBox(height: 10),
                            CustomTextFormField(
                              hintText: 'Enter your password',
                              controller: auth.loginPasswordController,
                              enableObscure: auth.isLoginObscure,
                              suffixIcon: IconButton(
                                onPressed: () {
                                  auth.changeLoginObscure();
                                },
                                icon: Icon(
                                  auth.isLoginObscure
                                      ? Icons.visibility_off
                                      : Icons.visibility,
                                  color: AppColors.kBlack,
                                ),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Password is required';
                                }
                                return null;
                              },
                              onChange: (val) {
                                if (auth.isLoginError) {
                                  auth.clearAllErrors();
                                }
                              },
                            ),
                            const SizedBox(height: 20),
                            // Error message
                            Visibility(
                              visible: auth.isLoginError,
                              child: Padding(
                                padding: const EdgeInsets.only(bottom: 16),
                                child: Text(
                                  auth.loginErrorMessage,
                                  style:
                                      AppTextStyles.textStyleDashboardMessage(
                                    context,
                                  ).copyWith(color: Colors.red),
                                ),
                              ),
                            ),
                            // Login button
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton(
                                style: ButtonStyle(
                                  foregroundColor:
                                      WidgetStateProperty.all<Color>(
                                    Colors.white,
                                  ),
                                  backgroundColor:
                                      WidgetStateProperty.all<Color>(
                                    Provider.of<DarkThemeProvider>(context)
                                            .darkTheme
                                        ? AppColors.kDarkPrimarySwatch
                                        : AppColors.kLightPrimarySwatch,
                                  ),
                                  shape: WidgetStateProperty.all<
                                      RoundedRectangleBorder>(
                                    RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(20),
                                      side: const BorderSide(
                                        color: Colors.blueAccent,
                                      ),
                                    ),
                                  ),
                                ),
                                onPressed: auth.disableLoginUntillResponse
                                    ? null
                                    : () {
                                        // Only validate when user clicks login
                                        if (formKeyLogin.currentState!
                                            .validate()) {
                                          auth.loginUser(context);
                                        } else {
                                          // Show validation errors without triggering autoValidate
                                          auth.showValidationErrors();
                                        }
                                      },
                                child: auth.disableLoginUntillResponse
                                    ? const SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                            Colors.white,
                                          ),
                                        ),
                                      )
                                    : const Text(
                                        'Login',
                                        style: TextStyle(fontSize: 18),
                                      ),
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  bool validateLogins(String username, String password) {
    if (username == AppStrings.username &&
        password == AppStrings.usernamePassword) {
      return true;
    } else if (username == AppStrings.username2 &&
        password == AppStrings.usernamePassword) {
      return true;
    } else if (username == AppStrings.username3 &&
        password == AppStrings.usernamePassword) {
      return true;
    } else if (username == AppStrings.username4 &&
        password == AppStrings.usernamePassword) {
      return true;
    } else if (username == AppStrings.username5 &&
        password == AppStrings.username5Password) {
      return true;
    } else if (username == AppStrings.username6 &&
        password == AppStrings.username6Password) {
      return true;
    } else if (username == AppStrings.username7 &&
        password == AppStrings.username7Password) {
      return true;
    } else if (username == AppStrings.username8 &&
        password == AppStrings.username8Password) {
      return true;
    } else if (username == AppStrings.username9 &&
        password == AppStrings.username9Password) {
      return true;
    } else if (username == AppStrings.username10 &&
        password == AppStrings.username10Password) {
      return true;
    }
    return false;
  }
}
