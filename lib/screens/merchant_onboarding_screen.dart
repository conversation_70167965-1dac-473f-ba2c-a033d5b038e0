import 'package:admin_portal/configs/configs_export.dart';
import 'package:admin_portal/provider/provider_export.dart';
import 'package:admin_portal/routes/route_names.dart';
import 'package:admin_portal/utils/utils_export.dart';
import 'package:admin_portal/widgets/image_preview.dart';
import 'package:admin_portal/widgets/widgets_export.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:reactive_forms/reactive_forms.dart';
import 'package:responsive_framework/responsive_framework.dart';

class MerchantOnboardingScreen extends StatefulWidget {
  const MerchantOnboardingScreen({super.key, required this.id, this.token});
  final String id;
  final String? token;
  @override
  State<MerchantOnboardingScreen> createState() =>
      _MerchantOnboardingScreenState();
}

class _MerchantOnboardingScreenState extends State<MerchantOnboardingScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: ChangeNotifierProvider<MerchantOnboardingProvider>(
        create: (context) => MerchantOnboardingProvider(),
        child: Row(
          children: [
            Drawer(
              backgroundColor: Theme.of(context).colorScheme.surface,
              child: Container(
                color: Theme.of(context).colorScheme.surface,
                child: Column(
                  children: [
                    Image.asset(
                      ImageConstants.payNationTag,
                    ),
                  ],
                ),
              ),
            ),
            Expanded(
              child: Column(
                children: [
                  buildAppBar(context, AppStrings.merchantOnboardingMessage),
                  Row(
                    children: [
                      Expanded(
                        child: Container(
                          padding: EdgeInsets.only(
                            left: 10,
                            right: ResponsiveValue(
                                  context,
                                  conditionalValues: [
                                    Condition.equals(
                                      name: MOBILE,
                                      value: 20.toDouble(),
                                    ),
                                    Condition.equals(
                                      name: TABLET,
                                      value: 80.toDouble(),
                                    ),
                                    Condition.equals(
                                      name: DESKTOP,
                                      value: 120.toDouble(),
                                    ),
                                  ],
                                ).value ??
                                80,
                          ),
                          child: Consumer<MerchantOnboardingProvider>(
                            builder: (context, provider, child) {
                              return Form(
                                key: provider.formKey,
                                child: Column(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // Text(AppStrings.dashBoardMessage,
                                    //     style: AppTextStyles
                                    //         .textStyleDashboardMessage(context)),

                                    const SizedBox(
                                      height: 20,
                                    ),
                                    const MandatoryFieldName(
                                      message: AppStrings.companyName,
                                      isBold: true,
                                    ),
                                    const SizedBox(
                                      height: 10,
                                    ),
                                    CustomTextFormField(
                                      controller: provider.controller,
                                      hintText: AppStrings.companyName,
                                      validator: (value) {
                                        if (value!.isEmpty) {
                                          return AppStrings
                                              .companyNameEmptyMessage;
                                        }
                                        return null;
                                      },
                                    ),
                                    const SizedBox(
                                      height: 20,
                                    ),
                                    const MandatoryFieldName(
                                      message: AppStrings.customSiteName,
                                      isBold: true,
                                    ),
                                    const SizedBox(
                                      height: 10,
                                    ),
                                    // CustomTextFormField(
                                    //   controller: provider.companySiteController,
                                    //   hintText: AppStrings.customSiteName,
                                    //   validator: (value) {
                                    //     if (value!.isEmpty) {
                                    //       return AppStrings.customSiteNameEmptyMessage;
                                    //     }
                                    //     return null;
                                    //   },
                                    // ),
                                    ReactiveForm(
                                      formGroup:
                                          provider.getFormGorup(widget.token!),
                                      child: ReactiveTextField<String>(
                                        key: const Key('site'),
                                        formControlName: 'site',
                                        decoration: InputDecoration(
                                          hintStyle: AppTextStyles
                                              .textStyleFormFieldHintText(
                                            context,
                                          ),
                                          hintText: AppStrings.customSiteName,
                                        ),
                                        onChanged: (value) {
                                          provider.companySiteController.text =
                                              value.value!;
                                        },
                                      ),
                                    ),

                                    const SizedBox(
                                      height: 20,
                                    ),
                                    Text(
                                      AppStrings.availableCompanySites,
                                      style: AppTextStyles.textStyleRegular14(
                                        context,
                                      ),
                                    ),
                                    const SizedBox(
                                      height: 10,
                                    ),
                                    //TODO::Select site widget

                                    const MandatoryFieldName(
                                      message: AppStrings.supportEmail,
                                      isBold: true,
                                    ),
                                    const SizedBox(
                                      height: 10,
                                    ),
                                    CustomTextFormField(
                                      controller:
                                          provider.supportEmailController,
                                      hintText: AppStrings.supportEmail,
                                      validator: (value) {
                                        if (value!.isEmpty) {
                                          return AppStrings
                                              .supportEmailEmptyMessage;
                                        }
                                        return null;
                                      },
                                    ),
                                    const SizedBox(
                                      height: 20,
                                    ),
                                    CommonElevatedButton(
                                      text: AppStrings
                                          .submitAndCreateMerchantText,
                                      borderRadius: 10,
                                      onTap: () {
                                        print(
                                          provider.formKey.currentState!
                                              .validate(),
                                        );
                                        print(
                                          provider
                                              .getFormGorup(widget.token!)
                                              .valid,
                                        );
                                        if (provider.formKey.currentState!
                                            .validate()) {
                                          // if (provider.transparentLogo == null ||
                                          //     provider.transparentLogo!.isEmpty) {
                                          //   showErrorDialog(context,
                                          //       "Please upload transparent logo");
                                          // } else {
                                          provider.updateTenantCompany(
                                            provider.controller.text,
                                            provider.companySiteController.text,
                                            provider
                                                .supportEmailController.text,
                                            widget.id,
                                            widget.token!,
                                          );
                                          //provider.uploadImages(widget.id, widget.token!);
                                          context.goNamed(
                                            context.namedLocation(
                                              AppRouteNames
                                                  .merchantOnboardSuccess,
                                            ),
                                            extra: {
                                              'site': provider
                                                  .companySiteController.text,
                                            },
                                          );
                                          // provider.uploadImages(
                                          //   widget.id,
                                          // );
                                          //}
                                        }
                                      },
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                      Expanded(
                        child: Consumer<MerchantOnboardingProvider>(
                          builder: (context, provider, child) {
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const SizedBox(
                                  height: 20,
                                ),
                                const MandatoryFieldName(
                                  message: AppStrings.imageUploadMessage,
                                  isBold: false,
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                CommonFileUploadButton(
                                  onPressed: () =>
                                      provider.pickTransparentLogoFile(),
                                  title: AppStrings.uploadLogoText,
                                  isUploaded:
                                      provider.isTransparentLogoUploaded,
                                  fileName: provider.transparentLogoFileName,
                                ),
                                const SizedBox(
                                  height: 20,
                                ),
                                CommonFileUploadButton(
                                  onPressed: () => provider.pickDarkLogoFile(),
                                  title: AppStrings.uploadDarkLogoText,
                                  isUploaded: provider.isDarkLogoUploaded,
                                  fileName: provider.darkLogoFileName,
                                ),
                                const SizedBox(
                                  height: 20,
                                ),
                                Text(
                                  AppStrings.logoPreviewText,
                                  style:
                                      AppTextStyles.textStyleRegular14(context)
                                          .copyWith(
                                    color: AppColors.kLightGrayDark,
                                  ),
                                ),
                                const SizedBox(
                                  height: 20,
                                ),
                                if (provider.transparentLogo == null)
                                  const ImagePreviewPlaceholder(),
                                if (provider.transparentLogo != null)
                                  UploadedImagePreview(
                                    memoryTransparentLogo:
                                        provider.transparentLogo,
                                    memoryDarkLogo: provider.darkLogo,
                                    onTransparentLogoPressed: () {
                                      provider.clearTransparentLogoImage();
                                    },
                                    onDarkLogoPressed: () {
                                      provider.clearDarkLogoImage();
                                    },
                                  ),
                              ],
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
