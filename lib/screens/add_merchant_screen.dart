import 'package:admin_portal/configs/configs_export.dart';
import 'package:admin_portal/provider/provider_export.dart';
import 'package:admin_portal/screens/screens_export.dart';

import 'package:admin_portal/utils/utils_export.dart';
import 'package:admin_portal/widgets/widgets_export.dart';
import 'package:flutter/material.dart';
import 'package:intl_phone_field/country_picker_dialog.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:provider/provider.dart';
import 'package:reactive_forms/reactive_forms.dart';

class AddMerchantScreen extends StatefulWidget {
  const AddMerchantScreen({super.key});

  @override
  State<AddMerchantScreen> createState() => _AddMerchantScreenState();
}

class _AddMerchantScreenState extends State<AddMerchantScreen> {
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<AddMerchantProvider>(
      create: (context) => AddMerchantProvider(),
      child: Scaffold(
        backgroundColor: Theme.of(context).colorScheme.surface,
        appBar: buildAppBar(context, AppStrings.addNewMerchant),
        body: Consumer<AddMerchantProvider>(
          builder: (context, provider, child) {
            if (provider.isAddMerchantSuccess) {
              return AddMerchantSuccessScreen(
                name: provider.firstNameController.text +
                    provider.lastNameController.text,
                email: provider.emailController.text,
              );
            }
            return Form(
              key: provider.formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    AppStrings.addNewMerchantMessage,
                    style: AppTextStyles.textStyleDashboardMessage(context),
                  ),

                  CustomTextFormField(
                    hintText: AppStrings.enterFirstName,
                    controller: provider.firstNameController,
                    focusNode: provider.firstNameFocusNode,
                    onFieldSubmitted: (_) {
                      provider.firstNameFocusNode.unfocus();
                      provider.lastNameFocusNode.requestFocus();
                    },
                    textInputAction: TextInputAction.next,
                    validator: AppValidators.validateName,
                    label: AppStrings.firstNameLabel,
                    width: 360,
                  ),
                  CustomTextFormField(
                    hintText: AppStrings.enterLastName,
                    controller: provider.lastNameController,
                    focusNode: provider.lastNameFocusNode,
                    onFieldSubmitted: (_) {
                      provider.lastNameFocusNode.unfocus();
                      provider.emailFocusNode.requestFocus();
                    },
                    textInputAction: TextInputAction.next,
                    validator: AppValidators.validateName,
                    label: AppStrings.lastNameLabel,
                    width: 360,
                  ),
                  // CustomTextFormField(
                  //   hintText: AppStrings.emailHintText,
                  //   controller: provider.emailController,
                  //   focusNode: provider.emailFocusNode,
                  //   onFieldSubmitted: (_) {
                  //     provider.emailFocusNode.unfocus();
                  //     provider.confirmEmailFocusNode.requestFocus();
                  //   },
                  //   textInputAction: TextInputAction.next,
                  //   validator: AppValidators.validateEmail,
                  //   label: AppStrings.emailLabel,
                  //   labelColor: AppColors.kBlack,
                  //   width: 360,
                  // ),

                  SizedBox(
                    width: 360,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppStrings.emailLabel,
                          style:
                              AppTextStyles.textStyleDashboardMessage(context)
                                  .copyWith(color: AppColors.kBlack),
                        ),
                        ReactiveForm(
                          formGroup: provider.reactiveForm,
                          child: ReactiveTextField<String>(
                            key: const Key('email'),
                            formControlName: 'email',
                            controller: provider.emailController,
                            validationMessages: {
                              ValidationMessage.pattern: (_) {
                                return AppStrings.emailErrorMessage;
                              },
                              ValidationMessage.required: (_) {
                                return AppStrings.emailErrorMessage;
                              },
                              ValidationMessage.minLength: (_) {
                                return AppStrings.emailErrorMessage;
                              },
                              ValidationMessage.maxLength: (_) {
                                return AppStrings.emailErrorMessage;
                              },
                            },
                            onSubmitted: (_) {
                              provider.emailFocusNode.unfocus();
                              provider.confirmEmailFocusNode.requestFocus();
                            },
                            focusNode: provider.emailFocusNode,
                            decoration: InputDecoration(
                              hintStyle:
                                  AppTextStyles.textStyleFormFieldHintText(
                                context,
                              ),
                              hintText: AppStrings.emailHintText,
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: BorderSide(
                                  color: AppColors.kBlack,
                                ),
                              ),
                              errorBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: BorderSide(color: AppColors.kRed),
                              ),
                              hoverColor: AppColors.kBlack,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  CustomTextFormField(
                    hintText: AppStrings.emailHintText,
                    controller: provider.confirmEmailController,
                    focusNode: provider.confirmEmailFocusNode,
                    onFieldSubmitted: (_) {
                      provider.confirmEmailFocusNode.unfocus();
                      provider.phoneFocusNode.requestFocus();
                    },
                    textInputAction: TextInputAction.next,
                    validator: (value) {
                      if (provider.emailController.text != value) {
                        return 'Emails do not match';
                      } else {
                        return null;
                      }
                    },
                    label: AppStrings.confirmEmailLabel,
                    width: 360,
                  ),

                  SizedBox(
                    width: 360,
                    child: Form(
                      key: provider.phoneFormKey,
                      child: IntlPhoneField(
                        controller: provider.phoneController,
                        focusNode: provider.phoneFocusNode,
                        textInputAction: TextInputAction.done,
                        dropdownIconPosition: IconPosition.trailing,
                        dropdownIcon: const Icon(Icons.expand_more_outlined),
                        showCountryFlag: false,
                        validator: (value) {
                          if (value!.completeNumber.length < 10) {
                            return 'Invalid Phone Number';
                          } else if (value.number.isEmpty) {
                            return 'Phone Number cannot be empty';
                          } else {
                            return null;
                          }
                        },
                        decoration: const InputDecoration(
                          labelText: 'Phone Number',
                          border: OutlineInputBorder(),
                        ),
                        initialCountryCode: 'IN',
                        onChanged: (phone) {
                          print(phone.completeNumber);
                          print(phone.countryCode);
                          provider.countryCodeController.text =
                              phone.countryCode;
                        },
                        pickerDialogStyle: PickerDialogStyle(
                          backgroundColor: AppColors.kWhite,
                        ),
                      ),
                    ),
                  ),

                  //TODO: Add a input field for phone number and country code.

                  CommonElevatedButton(
                    text: AppStrings.submitAndCreateMerchantText,
                    borderRadius: 10,
                    onTap: () {
                      //TODO: Add a function to submit the form and create a new merchant and show success message.
                      //provider.addMerchantSuccess();
                      if (provider.formKey.currentState!.validate() &&
                          provider.reactiveForm.valid &&
                          provider.phoneFormKey.currentState!.validate()) {
                        provider.addMerchant();
                      }
                    },
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
