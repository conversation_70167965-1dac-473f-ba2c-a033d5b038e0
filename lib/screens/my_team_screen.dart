import 'package:admin_portal/configs/configs_export.dart';
import 'package:admin_portal/models/models_export.dart';
import 'package:admin_portal/provider/provider_export.dart';
import 'package:admin_portal/utils/utils_export.dart';
import 'package:admin_portal/widgets/widgets_export.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class MyTeamScreen extends StatefulWidget {
  const MyTeamScreen({super.key});

  @override
  State<MyTeamScreen> createState() => _MyTeamScreenState();
}

class _MyTeamScreenState extends State<MyTeamScreen>
    with TickerProviderStateMixin {
  late TabController _controller;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _controller = TabController(length: 3, vsync: this);
    final provider = Provider.of<MyTeamProvider>(context, listen: false);
    provider.getAdminUsers();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: buildAppBar(context, AppStrings.myTeam),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppStrings.myTeamMessage,
            style: AppTextStyles.textStyleDashboardMessage(context),
          ),
          const SizedBox(height: 20),
          Text(
            AppStrings.searchNameMessage,
            style: AppTextStyles.textStyleDashboardMessage(context),
          ),
          const SizedBox(height: 20),
          Consumer<MyTeamProvider>(
            builder: (context, provider, child) {
              return Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CustomTextFormField(
                    prefixIcon: Icon(
                      Icons.search,
                      color: AppColors.kRed,
                    ),
                    hintText: AppStrings.searchTextMessage,
                    controller: TextEditingController(),
                    validator: AppValidators.validateEmail,
                    width: 300,
                  ),
                  const Spacer(),
                  CustomListTile(
                    tileColor:
                        Provider.of<DarkThemeProvider>(context, listen: false)
                                .darkTheme
                            ? AppColors.kBlack
                            : AppColors.kWhite,
                    isDarkTheme:
                        Provider.of<DarkThemeProvider>(context, listen: false)
                            .darkTheme,
                    title: AppStrings.myTeamCreateUser,
                    onTap: () {
                      provider.createUserDialog(
                        context,
                        provider.firstNameController,
                        provider.firstNameFocusNode,
                        provider.lastNameController,
                        provider.lastNameFocusNode,
                        provider.emailController,
                        provider.emailFocusNode,
                        provider.phoneController,
                        provider.phoneFocusNode,
                        provider.dropDownValue,
                      );
                      // provider.showSuccessfulCreateUser(context);
                    },
                  ),
                ],
              );
            },
          ),
          const SizedBox(
            height: 16,
          ),
          Expanded(
            child: DecoratedBox(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: AppColors.kLightGrayLight),
              ),
              child: Scrollbar(
                controller: ScrollController(),
                child: ListView(
                  shrinkWrap: true,
                  children: [
                    TabBar(
                      controller: _controller,
                      labelStyle: AppTextStyles.textStyleBold14(context),
                      unselectedLabelStyle:
                          AppTextStyles.textStyleBold14(context),
                      tabs: const [
                        Tab(
                          text: AppStrings.myTeamAllUsers,
                        ),
                        Tab(
                          text: AppStrings.myTeamActiveUsers,
                        ),
                        Tab(
                          text: AppStrings.myTeamInactiveUsers,
                        ),
                      ],
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 16, right: 16),
                      child: Consumer<MyTeamProvider>(
                        builder: (context, provider, child) {
                          return PaginatedDataTable(
                            columns: [
                              DataColumn(
                                label: Text(
                                  AppStrings.userId,
                                  style: AppTextStyles.textStyleBold12(
                                    context,
                                  ),
                                ),
                              ),
                              DataColumn(
                                label: Text(
                                  AppStrings.userName,
                                  style: AppTextStyles.textStyleBold12(
                                    context,
                                  ),
                                ),
                              ),
                              DataColumn(
                                label: Text(
                                  AppStrings.userEmail,
                                  style: AppTextStyles.textStyleBold12(
                                    context,
                                  ),
                                ),
                              ),
                              DataColumn(
                                label: Text(
                                  AppStrings.userRole,
                                  style: AppTextStyles.textStyleBold12(
                                    context,
                                  ),
                                ),
                              ),
                              DataColumn(
                                label: Text(
                                  AppStrings.manageMerchantData,
                                  style: AppTextStyles.textStyleBold12(
                                    context,
                                  ),
                                ),
                              ),
                            ],
                            source: MyTeamUsersData(
                              provider.adminUsers,
                              context,
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
