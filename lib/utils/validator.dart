import 'package:admin_portal/utils/utils_export.dart';
import 'package:reactive_forms/reactive_forms.dart';

class AppValidators {
  static String? validateEmail(String? value) {
    const pattern = r"(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'"
        r'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-'
        r'\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*'
        r'[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:(2(5[0-5]|[0-4]'
        r'[0-9])|1[0-9][0-9]|[1-9]?[0-9]))\.){3}(?:(2(5[0-5]|[0-4][0-9])|1[0-9]'
        r'[0-9]|[1-9]?[0-9])|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\'
        r'x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])';
    final regex = RegExp(pattern);

    return value!.isNotEmpty && !regex.hasMatch(value)
        ? AppStrings.emailErrorMessage
        : null;
  }

  static Map<String, dynamic>? validateEmailReactive(
    AbstractControl<dynamic> control,
  ) {
    const pattern = r"(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'"
        r'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-'
        r'\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*'
        r'[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:(2(5[0-5]|[0-4]'
        r'[0-9])|1[0-9][0-9]|[1-9]?[0-9]))\.){3}(?:(2(5[0-5]|[0-4][0-9])|1[0-9]'
        r'[0-9]|[1-9]?[0-9])|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\'
        r'x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])';
    final regex = RegExp(pattern);

    return control.value!.isNotEmpty && !regex.hasMatch(control.value)
        ? {'requiredTrue': 'Email is invalid'}
        : null;
  }

  static const pattern = r"(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'"
      r'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-'
      r'\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*'
      r'[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:(2(5[0-5]|[0-4]'
      r'[0-9])|1[0-9][0-9]|[1-9]?[0-9]))\.){3}(?:(2(5[0-5]|[0-4][0-9])|1[0-9]'
      r'[0-9]|[1-9]?[0-9])|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\'
      r'x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])';

  static String? validateName(String? value) {
    // const pattern = r'^[a-zA-Z ]*$';
    // final regex = RegExp(pattern);

    // return value!.isNotEmpty && !regex.hasMatch(value)
    //     ? AppStrings.nameErrorMessage
    //     : value!.isEmpty
    //         ? AppStrings.nameEmptyErrorMessage
    //         : null;
    if (value!.isEmpty) {
      return AppStrings.nameErrorMessage;
    }
    return null;
  }

  //Empty validator with error message
  static String? validateEmptyPassword(String? value) {
    if (value == null) {
      return 'Invalid password';
    }
    if (value.isEmpty) {
      return 'Invalid password';
    }
    // validation for logic length less than 8
    // if (value.length < 9) {
    //   return 'Password must be at least 9 characters';
    // }
    // if (value != "kNjbpUgvf%19874") {
    //   return 'You have entered wrong password';
    // }
    return null;
  }

  static String? validateEmptyemail(String? value) {
    if (value == null) {
      return 'Invalid username';
    }
    if (value.isEmpty) {
      return 'Invalid username';
    }
    // validation for logic length less than 8
    // if (value.length < 8) {
    //   return 'Password must be at least 8 characters';
    // }
    // if (value != "<EMAIL>") {
    if (value == AppStrings.username ||
        value == AppStrings.username2 ||
        value == AppStrings.username3 ||
        value == AppStrings.username4 ||
        value == AppStrings.username5 ||
        value == AppStrings.username6 ||
        value == AppStrings.username7 ||
        value == AppStrings.username8 ||
        value == AppStrings.username9 ||
        value == AppStrings.username10) {
      // return 'You have entered wrong username';
    } else {
      return 'You have entered wrong username';
    }
    return null;
  }
}
