class AppStrings {
  AppStrings._();

  static const String welcomeText = 'Welcome!';
  static const String dashBoardMessage =
      'Manage the merchant instances from here';
  static const String searchTextMessage =
      'Search by Mobile number, Email, or Name';
  static const String searchNameMessage = 'Search by Name';
  static const String addMerchant = 'ADD MERCHANT';
  static const String allMerchants = 'ALL MERCHANTS';
  static const String activeMerchants = 'ACTIVE MERCHANTS';
  static const String stoppedMerchants = 'STOPPED MERCHANTS';
  static const String hiddenMerchants = 'HIDDEN MERCHANTS';
  static const String addNewMerchant = 'Add New Merchant';
  static const String addNewMerchantMessage =
      'Fill in the basic information to Create a new merchant in the system and invite them to the portal. ';
  // static const String merchantPage = "Merchant Page";
  static const String merchantPage = 'Details Page';
  //ManageMerchant table column names
  // static const String merchantId = "MERCHANT ID";
  // static const String merchantName = "MERCHANT NAME";
  // static const String merchantAddedDate = "DATE ADDED";
  // static const String merchantStatus = "STATUS";
  // static const String hideMerchant = "HIDE MERCHANT";
  // static const String manageMerchantTable = "MANAGE MERCHANT";
  static const String merchantId = 'ORDER ID';
  static const String merchantName = 'FULL NAME';
  static const String merchantAddedDate = 'PURCHASED DATE';
  static const String assignHostname = 'HOSTNAME';
  static const String assignOnline = 'ONLINE';
  static const String merchantStatus = 'ORDER STATUS';
  static const String assignHostnames = 'ASSIGN HOSTNAMES';
  static const String hideMerchant = 'EMAIL ADDRESS';
  static const String manageMerchantTable = 'MANAGE ACCOUNT';
  static const String address = 'ADDRESS';
  static const String prodectDetails = 'PRODUCT DETAILS';
  static const String payment = 'PAYMENT';

  static const String supportFirstName = 'FIRST NAME';
  static const String supportLastName = 'LAST NAME';
  static const String supportMobileNumber = 'MOBILE NUMBER';
  static const String supportEmails = 'EMAIL';
  static const String supportCryptoAddress = 'CRYPTO WALLET ADDRESS';
  static const String supportLinkedNFT = "LINKED NFT's";
  static const String supportLinkedServers = 'LINKED SERVERS';
  static const String supportOrders = 'ORDERS';
  static const String supportEdit = 'MANAGE ACCOUNT';
  // static const String support = "";
  // static const String support = "";

  //drawer section button titles
  // static const String manageMerchant = "Manage Merchant";
  static const String manageMerchant = 'Order Dashboard';
  static const String supportDashboard = 'Support Dashboard';
  static const String addMerchantDrawer = 'Add Merchant';
  static const String myTeam = 'My Team';
  static const String accountSettings = 'Account Settings';

  static const String loginText = 'Dashboards for NexQloud';
  static const String submitText = 'SUBMIT';
  static const String submitAndCreateMerchantText = 'Submit & Create Merchant';
  static const String loginEmailText = 'Login Username';
  static const String otpText = 'OTP';

  //Validation Error messages
  static const String emailErrorMessage = 'Invalid Email';
  static const String nameErrorMessage = 'Invalid Name';
  static const String nameEmptyErrorMessage = 'Name cannot be empty';

  //textField Hint messages
  static const String emailHintText = 'Enter Email';
  static const String confirmEmailHintText = 'Enter Email';
  static const String enterFirstName = 'Enter FirstName';
  static const String enterLastName = 'Enter LastName';

  //textField Labels
  static const String firstNameLabel = 'First Name';
  static const String lastNameLabel = 'Last Name';
  static const String emailLabel = 'Email';
  static const String confirmEmailLabel = 'Confirm Email';

  //Add Merchant Success Screen
  static const String addNewMerchantInviteSuccessMessage =
      'Your Merchant Dashboard invite has been sent successfully!';
  static const String addNewMerchantSuccessInformMessage =
      'Please inform them to check their email and use the link in the mail to start onboarding.';

  //Merchant Details Page
  // static const String merchantDetailsMessage =
  //     "Worry not! All your Invoices & Payments are fetched here.";
  static const String merchantDetailsMessage =
      'All your required details are fetched here.';
  static const String allUsers = 'ALL USERS';
  static const String activeUsers = 'ACTIVE USERS';
  static const String inactiveUsers = 'INACTIVE USERS';
  static const String userId = 'USER ID';
  static const String userName = 'USER NAME';
  static const String userEmail = 'USER EMAIL';
  static const String userRole = 'USER ROLE';
  static const String manageMerchantData = 'MANAGE MERCHANT';
  static const String createUser = 'CREATE USER';
  //Merchant Onboarding Screen
  static const String merchantOnboardingMessage =
      'Merchant Onboarding - Company info';
  static const String uploadLogoText = 'Upload Logo';
  static const String uploadDarkLogoText = 'Upload Dark Logo';
  static const String logoPreviewText = 'Logo Preview';
  static const String previewCap = 'PREVIEW';
  static const String imageUploadMessage =
      'upload “.png” transparent background upload Black logo.';
  static const String companyName = 'Company Name';
  static const String companyNameEmptyMessage = 'Company Name cannot be empty';
  static const String customSiteName = 'Custom Site Name';
  static const String customSiteNameEmptyMessage =
      'Custom Site Name cannot be empty';
  static const String availableCompanySites = 'Available Site Names';
  static const String supportEmail = 'Support Email';
  static const String supportEmailEmptyMessage =
      'Support Email cannot be empty';

  //Merchant onboard success screen
  static const String merchantOnboardSuccessMessage = 'Congratulations!';
  static const String merchantOnboardSuccessInformMessage =
      'Merchant Dashboard has been successfully created.';
  static const String merchantOnboardSuccessInformMessage2 =
      'Please inform your team to check their email and use the link in the mail to start using the portal';
  static const String takeMeThere = 'TAKE ME THERE';

  //MyTeam Screen
  static const String myTeamMessage = 'Team Managing Made Easy!';
  static const String myTeamAllUsers = 'ALL USERS';
  static const String myTeamActiveUsers = 'ACTIVE USERS';
  static const String myTeamInactiveUsers = 'INACTIVE USERS';
  static const String myTeamCreateUser = 'CREATE USER';
  static const String myTeamAddAnotherUser = 'ADD ANOTHER MERCHANT';

  //username
  static const String username = 'mhannah';
  static const String username2 = 'htiyyagura';
  static const String username3 = 'rvegesna';
  static const String username4 = 'bbarnett';
  static const String username5 = '<EMAIL>';
  static const String username6 = '<EMAIL>';
  static const String username7 = '<EMAIL>';
  static const String username8 = 'wtimothy';
  static const String username9 = 'taguillard';
  static const String username10 = 'gnadella';

  //passwords
  static const String usernamePassword = 'kNjbpUgvf%19874';
  static const String username5Password = 'D1JA6W2jL8QTEtR';
  static const String username6Password = '8GWa2bES5pjlCkW';
  static const String username7Password = 'HAwrKCfgJgkWkur';
  static const String username8Password = 'wtimothyKfCfgJgk';
  static const String username9Password = 'taguillardES5pjlC';
  static const String username10Password = 'gnadellaf%198';

  //Order Status
  static String Provisioning = 'Provisioning';
  static String Configuring = 'Configuring';
  static String QATesting = 'QA Testing';
  static String QA_Testing = 'QA_Testing';
  static String PackAndShip = 'PackAndShip';
  static String PackShip = 'Pack & Ship';
  static String Shipped = 'Shipped';
  static String Received = 'Received';

  static String dropdownDefalutName = 'Choose One';
}
