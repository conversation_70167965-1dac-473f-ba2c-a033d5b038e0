/// Validator that validates the user's email is unique, sending a request to
/// the Server.

import 'package:admin_portal/api/api.dart';
import 'package:reactive_forms/reactive_forms.dart';

class UniqueSiteAsyncValidator extends AsyncValidator<dynamic> {
  UniqueSiteAsyncValidator({required this.token});
  final String token;
  @override
  Future<Map<String, dynamic>?> validate(
    AbstractControl<dynamic> control,
  ) async {
    // final error = {'Site not available': false};

    final isUniqueEmail = await _getIsUniqueSite(control.value.toString());
    if (!isUniqueEmail['available']) {
      control.markAsTouched();
      return {isUniqueEmail['message']: false};
    }

    return null;
  }

  /// Simulates a time consuming operation (i.e. a Server request)
  Future<Map<String, dynamic>> _getIsUniqueSite(String site) async {
    // simple array that simulates emails stored in the Server DB.

    try {
      final response = await api.checkTenantSiteNameExists(site, token);
      print(response);
      return {
        'available': response['available'],
        'message': response['message'] ?? '',
      };
    } catch (e) {
      print(e);
      return {
        'available': false,
        'message': 'Something went wrong. Please try again later.',
      };
    }
  }
}
