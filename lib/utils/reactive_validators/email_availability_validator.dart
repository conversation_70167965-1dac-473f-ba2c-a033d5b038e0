import 'package:admin_portal/api/api.dart';
import 'package:reactive_forms/reactive_forms.dart';

class UniqueEmailAsyncValidator extends AsyncValidator<dynamic> {
  @override
  Future<Map<String, dynamic>?> validate(
    AbstractControl<dynamic> control,
  ) async {
    // final error = {'Site not available': false};

    final isUniqueEmail = await _getIsUniqueSite(control.value.toString());
    if (!isUniqueEmail['available']) {
      control.markAsTouched();
      return {isUniqueEmail['message']: false};
    }

    return null;
  }

  /// Simulates a time consuming operation (i.e. a Server request)
  Future<Map<String, dynamic>> _getIsUniqueSite(String site) async {
    // simple array that simulates emails stored in the Server DB.

    try {
      final response = await api.checkTenantEmail(site);
      print(response);
      return {
        'available': response['available'],
        'message': response['message'] ?? '',
      };
    } catch (e) {
      if (e is Map<String, dynamic>) {
        print('Map printed');
        return {
          'available': false,
          'message': e['message'] ?? 'Something went wrong',
        };
      } else {
        print('String printed');
        return {
          'available': false,
          'message': e ?? 'Something went wrong',
        };
      }
    }
  }
}
