import 'package:admin_portal/utils/app_strings.dart';
import 'package:intl/intl.dart';

class HelperFunctions {
  static String convertToDate(String value) {
    final inputDate = DateTime.parse(
      DateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(value).toString(),
    );
    final outputFormat = DateFormat('MM-dd-yyyy hh:mm a');
    final outputDate = outputFormat.format(inputDate);
    return outputDate;
  }

  static String returnStatus(String value) {
    if (value == AppStrings.QA_Testing) {
      return 'QA Testing';
    } else if (value == AppStrings.PackAndShip) {
      return 'Pack & Ship';
    } else if (value == AppStrings.Received) {
      return AppStrings.Shipped;
    }

    return value;
  }

  static String formatAmountTwoDecimals(double amount) {
    final formattedAmount = amount.toStringAsFixed(2); // Keep 2 decimal places
    final parts = formattedAmount.split('.');
    final wholePart = parts[0];
    final decimalPart = parts.length > 1 ? '.${parts[1]}' : '';

    var result = '';
    var count = 0;
    for (var i = wholePart.length - 1; i >= 0; i--) {
      result = wholePart[i] + result;
      count++;
      if (count % 3 == 0 && i > 0) {
        result = ',$result';
      }
    }
    return result + decimalPart;
  }
}
