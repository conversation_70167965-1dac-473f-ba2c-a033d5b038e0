import 'package:admin_portal/routes/route_names.dart';
import 'package:admin_portal/screens/order_details_page.dart';
import 'package:admin_portal/screens/screens_export.dart';
import 'package:admin_portal/screens/support_screen.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

final _rootNavigatorKey = GlobalKey<NavigatorState>();
final _shellNavigatorKey = GlobalKey<NavigatorState>();
final _manageMerchantKey = GlobalKey<NavigatorState>();
final _supportKey = GlobalKey<NavigatorState>();
final _addMerchantKey = GlobalKey<NavigatorState>();
final _myTeamKey = GlobalKey<NavigatorState>();

class AppRoutes {
  AppRoutes._();
  static final router = GoRouter(
    navigatorKey: _rootNavigatorKey,
    initialLocation: '/',
    routes: [
      GoRoute(
        // parentNavigatorKey: _rootNavigatorKey,
        path: AppRouteNames.merchantOnboardSuccess,
        name: AppRouteNames.merchantOnboardSuccess,
        builder: (context, state) {
          final siteName = state.extra as Map<String, dynamic>;
          return MerchantOnbardingSuccessScreen(
            siteName: siteName['site']?.toString() ?? '',
          );
        },
      ),
      GoRoute(
        // parentNavigatorKey: _rootNavigatorKey,
        path: '/${AppRouteNames.merchantOnboard}/:id',
        // name: AppRouteNames.merchantOnboard,
        builder: (context, state) {
          print('state.pathParameters ${state.pathParameters}');
          final id = state.pathParameters['id'];
          final token = state.uri.queryParameters['token'];
          print('onboard token $token');
          return MerchantOnboardingScreen(
            id: id ?? '',
            token: token,
          );
        },
      ),
      GoRoute(
        path: AppRouteNames.splash,
        builder: (context, state) => const SplashScreen(),
        routes: [
          GoRoute(
            //parentNavigatorKey: _rootNavigatorKey,
            path: AppRouteNames.login,
            name: AppRouteNames.login,
            builder: (context, state) => const LoginScreen(),
          ),
          GoRoute(
            // parentNavigatorKey: _rootNavigatorKey,
            path: AppRouteNames.otpScreen,
            name: AppRouteNames.otpScreen,
            builder: (context, state) {
              final params = state.extra as Map<String, dynamic>;
              return OtpScreen(
                userId: params['userId']?.toString() ?? '',
              );
            },
          ),

          // GoRoute(
          //   // parentNavigatorKey: _rootNavigatorKey,
          //   path: AppRouteNames.merchantOnboardSuccess,
          //   name: AppRouteNames.merchantOnboardSuccess,
          //   builder: (context, state) =>
          //       const MerchantOnbardingSuccessScreen(),
          // ),
          StatefulShellRoute.indexedStack(
            builder: (context, state, child) => DashBoardScreen(
              key: state.pageKey,
              child: child,
            ),
            branches: <StatefulShellBranch>[
              //Manage Merchant Shell Branch
              StatefulShellBranch(
                navigatorKey: _manageMerchantKey,
                routes: [
                  GoRoute(
                    //parentNavigatorKey: _shellNavigatorKey,
                    path: AppRouteNames.manageMerchant,
                    name: AppRouteNames.manageMerchant,
                    builder: (context, state) => const ManageMerchantScreen(),
                    routes: [
                      GoRoute(
                        // parentNavigatorKey: _shellNavigatorKey,
                        path: '${AppRouteNames.orderDetails}/:orderId',
                        name: AppRouteNames.orderDetails,
                        builder: (context, state) {
                          final Map<String, dynamic> params =
                              state.pathParameters;

                          // Safely handle `state.extra`
                          final queryParameters = state.extra != null &&
                                  state.extra is Map<String, dynamic>
                              ? state.extra! as Map<String, dynamic>
                              : null;

                          print(
                            'params :: $params : queryParameters $queryParameters',
                          );

                          return OrderDetailsPage(
                            key: UniqueKey(),
                            id: params['orderId'] ?? '',
                            email: queryParameters?['email'] ??
                                '', // Safe access with fallback
                          );
                        },
                      ),
                    ],
                  ),
                ],
              ),
              StatefulShellBranch(
                navigatorKey: _supportKey,
                routes: [
                  GoRoute(
                    //parentNavigatorKey: _shellNavigatorKey,
                    path: AppRouteNames.supportScreen,
                    name: AppRouteNames.supportScreen,
                    builder: (context, state) => const SupportScreen(),
                    routes: [
                      GoRoute(
                        // parentNavigatorKey: _shellNavigatorKey,
                        path: '${AppRouteNames.merchantDetails}/:merchantId',
                        name: AppRouteNames.merchantDetails,
                        builder: (context, state) {
                          final Map<String, dynamic> params =
                              state.pathParameters;

                          // Safely handle state.extra and ensure it's a Map
                          final Map<String, dynamic>? queryParameters =
                              state.extra != null &&
                                      state.extra is Map<String, dynamic>
                                  ? state.extra as Map<String, dynamic>
                                  : null;

                          print(
                              'params :: $params : queryParameters $queryParameters');

                          return MerchantDetailsPage(
                            key: UniqueKey(),
                            id: params['merchantId'] ?? '',
                            email: queryParameters?['email'] ??
                                '', // Null-safe access
                          );
                        },
                      ),
                    ],
                  ),
                ],
              ),
              //AddMerchant Shell Branch
              // StatefulShellBranch(navigatorKey: _addMerchantKey, routes: [
              //   GoRoute(
              //       // parentNavigatorKey: _shellNavigatorKey,
              //       path: AppRouteNames.addmerchant,
              //       name: AppRouteNames.addmerchant,
              //       builder: (context, state) => const AddMerchantScreen()),
              // ]),
              // //myTeam Branch
              // StatefulShellBranch(navigatorKey: _myTeamKey, routes: [
              //   GoRoute(
              //       // parentNavigatorKey: _shellNavigatorKey,
              //       path: AppRouteNames.myTeam,
              //       name: AppRouteNames.myTeam,
              //       builder: (context, state) => const MyTeamScreen()),
              // ]),
            ],
          ),
        ],
      ),
      // GoRoute(path: AppRouteNames.dashboard, builder: (context, state) => const DashBoardScreen()),
    ],
  );
}
