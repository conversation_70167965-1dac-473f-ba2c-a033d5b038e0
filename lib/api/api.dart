import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:admin_portal/services/services_export.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';

class Api {
  // Dev
  // final baseUri2 = "https://devapi.nexqloud.org";
  // Prod
  final baseUri2 = const String.fromEnvironment(
    'BASE_URI2',
    // defaultValue: 'http://192.168.1.79:3000',
    defaultValue: 'https://devapi.dks.nexqloud.net',
  );
  // final baseUri = 'http://192.168.1.79:3000';
  final baseUri = 'https://chaos.paynation.io';
  //final baseUri = "http://10.37.26.114:3001";

  // Helper method to get token from secure storage
  Future<String> _getAuthToken() async {
    final token = await SecureStorageService.getToken();
    return token ?? '';
  }

  Future<Map<String, dynamic>> sendOtp(String email) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUri/admin/send-otp'),
        body: {'email': email},
      );
      final responseJson = json.decode(response.body);
      return responseJson;
    } catch (e) {
      print(e);
      return {};
    }
  }

  //API for login with phone number and password
  Future<Map<String, dynamic>> login(
    String countryCode,
    String mobileNumber,
    String password,
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUri2/user/login'),
        body: jsonEncode({
          'phone': {
            'countryCode': countryCode,
            'number': mobileNumber,
          },
          'password': password,
        }),
        headers: {
          'accept': '*/*',
          'Content-Type': 'application/json',
        },
      );

      final responseJson = json.decode(response.body);
      if (kDebugMode) {
        print('Login response: $responseJson');
        print('Status code: ${response.statusCode}');
      }

      // Handle different HTTP status codes
      if (response.statusCode == 200 || response.statusCode == 201) {
        return {
          'statusCode': response.statusCode,
          'success': true,
          'data': responseJson,
          'userId': responseJson['userId'],
          'message': responseJson['message'] ?? 'OTP Sent Successfully',
        };
      } else {
        return {
          'statusCode': response.statusCode,
          'success': false,
          'message':
              responseJson['message'] ?? 'Login failed. Please try again.',
          'error': responseJson['error'] ?? 'Unknown error occurred',
        };
      }
    } catch (e) {
      print('Login error: $e');
      return {
        'statusCode': 500,
        'success': false,
        'message': 'Network error. Please check your connection and try again.',
        'error': e.toString(),
      };
    }
  }

  //API for verify mobile OTP
  Future<Map<String, dynamic>> verifyMobileOtp(
    String userId,
    String otp,
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUri2/user/verify-mobile-otp/dashboard'),
        body: jsonEncode({
          'userId': userId,
          'otp': otp,
        }),
        headers: {
          'accept': '*/*',
          'Content-Type': 'application/json',
        },
      );

      final responseJson = json.decode(response.body);
      if (kDebugMode) {
        print('OTP verification response: $responseJson');
        print('Status code: ${response.statusCode}');
      }

      // Handle different HTTP status codes
      if (response.statusCode == 200 || response.statusCode == 201) {
        return {
          'statusCode': response.statusCode,
          'success': true,
          'data': responseJson,
          'user': responseJson['user'],
          'message': responseJson['message'] ?? 'OTP verified successfully',
        };
      } else {
        return {
          'statusCode': response.statusCode,
          'success': false,
          'message': responseJson['message'] ??
              'OTP verification failed. Please try again.',
          'error': responseJson['error'] ?? 'Invalid OTP',
        };
      }
    } catch (e) {
      print('OTP verification error: $e');
      return {
        'statusCode': 500,
        'success': false,
        'message': 'Network error. Please check your connection and try again.',
        'error': e.toString(),
      };
    }
  }

  //Api for get-users
  Future<Map<String, dynamic>> getUsers() async {
    try {
      final response = await http.post(
        Uri.parse('$baseUri/admin/get-users'),
        body: jsonEncode({'page': 1, 'limit': 10}),
        headers: {
          'accept': '*/*',
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseJson = json.decode(response.body);
        print(responseJson);
        return responseJson;
      } else {
        return Future.error({
          'statusCode': response.statusCode,
          'message': jsonDecode(response.body)['message'],
        });
      }
    } catch (e) {
      print(e);
      return {};
    }
  }

  //API for create user
  Future<Map<String, dynamic>> createUser(
    String userName,
    String email,
    String countryCode,
    String number,
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUri/admin/create'),
        body: jsonEncode({
          'name': userName,
          'email': email,
          'phone': {'countryCode': countryCode, 'number': number},
        }),
        headers: {
          'accept': '*/*',
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseJson = json.decode(response.body);
        return responseJson;
      } else {
        return Future.error({
          'statusCode': response.statusCode,
          'message': jsonDecode(response.body)['message'],
        });
      }
    } catch (e) {
      print(e);
      return {};
    }
  }

  Future<Map<String, dynamic>> sentEmail(
    Map<String, dynamic> data,
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUri2/customer/sendEmail'),
        body: jsonEncode(data),
        headers: {
          'accept': '*/*',
          'Content-Type': 'application/json',
          // "Authorization": "Bearer ${PreferenceUtils().getString('token')}",
        },
      );
      log('sentEmail reponse: ${response.body}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseJson = json.decode(response.body);
        return responseJson;
      } else {
        return Future.error({
          'statusCode': response.statusCode,
          'message': jsonDecode(response.body)['message'],
        });
      }
    } catch (e) {
      print(e);
      return {};
    }
  }

  //API for edit user
  Future<Map<String, dynamic>> editOrderStatus(
    Map<String, dynamic> data,
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUri2/customer/updateOrderStatusForStore'),
        body: jsonEncode(data),
        headers: {
          'accept': '*/*',
          'Content-Type': 'application/json',
          // "Authorization": "Bearer ${PreferenceUtils().getString('token')}",
        },
      );
      log('Updated reponse: ${response.body}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseJson = json.decode(response.body);
        return responseJson;
      } else {
        return Future.error({
          'statusCode': response.statusCode,
          'message': jsonDecode(response.body)['message'],
        });
      }
    } catch (e) {
      print(e);
      return {};
    }
  }

  //API for edit support
  Future<Map<String, dynamic>> editSupportDetails(
    Map<String, dynamic> data,
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUri2/customer/updateOrderStatusForSupport'),
        body: jsonEncode(data),
        headers: {
          'accept': '*/*',
          'Content-Type': 'application/json',
          // "Authorization": "Bearer ${PreferenceUtils().getString('token')}",
        },
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseJson = json.decode(response.body);
        return responseJson;
      } else {
        return Future.error({
          'statusCode': response.statusCode,
          'message': jsonDecode(response.body)['message'],
        });
      }
    } catch (e) {
      print(e);
      return {};
    }
  }

//API for get user
  Future<Map<String, dynamic>> getOrderList(
    String page,
    String limit,
    String sort,
    String order,
    String search,
    DateTime? selectedDate,
    List<String> status,
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUri2/customer/getUserInfoForStore'),
        body: jsonEncode({
          'page': page,
          'limit': limit,
          'sort': sort,
          'order': order,
          'search': search,
          'selectedDate': selectedDate?.toIso8601String(),
          'status': status,
        }),
        headers: {
          'accept': '*/*',
          'Content-Type': 'application/json',
          // "Authorization": "Bearer ${PreferenceUtils().getString('token')}",
        },
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseJson = json.decode(response.body);
        return responseJson;
      } else {
        return Future.error({
          'statusCode': response.statusCode,
          'message': jsonDecode(response.body)['message'],
        });
      }
    } catch (e) {
      print('Error in getOrderList: $e');
      return {};
    }
  }

  Future<Map<String, dynamic>> getOrderDetails(
    String page,
    String limit,
    String orderId,
    // String comment,
  ) async {
    try {
      final sort = jsonEncode(
        {'firstName': -1},
      );
      final response = await http.post(
        Uri.parse('$baseUri2/customer/getUserInfoForStore'),
        body: jsonEncode(
          {'orderId': orderId, 'page': page, 'limit': limit, 'sort': sort},
        ),
        headers: {
          'accept': '*/*',
          'Content-Type': 'application/json',
          // "Authorization": "Bearer ${PreferenceUtils().getString('token')}",
        },
      );
      log('getOrderList reponse: ${response.body}');
      print('getOrderList reponse: ${response.body}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseJson = json.decode(response.body);
        print('getOrderList responseJson: ${response.body}');
        return responseJson;
      } else {
        return Future.error({
          'statusCode': response.statusCode,
          'message': jsonDecode(response.body)['message'],
        });
      }
    } catch (e) {
      print(e);
      // return [];
      return {};
    }
  }

  Future<Map<String, dynamic>> getSupportList(
    String page,
    String limit,
    String sort,
    String order,
    String search,
  ) async {
    try {
      final url = Uri.parse('$baseUri2/customer/getUserInfoForSupport/v2');

      final requestBody = jsonEncode({
        'page': page,
        'limit': limit,
        'sort': sort,
        'search': search,
        'order': order,
      });

      final headers = {
        'accept': '*/*',
        'Content-Type': 'application/json',
        'api-key': '.aBZxGyKLvRtOP@GH*456',
        // 'Authorization': "Bearer ${PreferenceUtils().getString('token')}",
      };

      final response =
          await http.post(url, body: requestBody, headers: headers);

      if (response.statusCode == 200 || response.statusCode == 201) {
        return json.decode(response.body);
      } else {
        return Future.error({
          'statusCode': response.statusCode,
          'message': jsonDecode(response.body)['message'],
        });
      }
    } catch (e) {
      print('response error:$e');
      // return [];
      return {};
    }
  }

  Future<Map<String, dynamic>> getSupportDetails(
    String page,
    String limit,
    String userId,
  ) async {
    try {
      final sort = jsonEncode(
        {'firstName': -1},
      );
      final response = await http.post(
        Uri.parse('$baseUri2/customer/getUserInfoForSupport/v2'),
        body: jsonEncode({
          // "email": "<EMAIL>",
          // "email": mail,
          'userId': userId,
          'page': page,
          'limit': limit,
          'sort': sort,
        }),
        headers: {
          'accept': '*/*',
          'Content-Type': 'application/json',
          'api-key': '.aBZxGyKLvRtOP@GH*456',
          // "Authorization": "Bearer ${PreferenceUtils().getString('token')}",
        },
      );
      // log("getOrderList reponse: ${response.body}");
      // print('getSupportDetails reponse: ${response.body}');
      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseJson = json.decode(response.body);
        // print("getOrderList responseJson: ${response.body}");
        return responseJson;
      } else {
        return Future.error({
          'statusCode': response.statusCode,
          'message': jsonDecode(response.body)['message'],
        });
      }
    } catch (e) {
      print('response error:$e');
      // return [];
      return {};
    }
  }

  //API for edit user
  Future<Map<String, dynamic>> editUser(
    String userId,
    String userName,
    String countryCode,
    String number,
    String role,
  ) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUri/admin/user/edit/$userId'),
        body: jsonEncode({
          'name': userName,
          'phone': {'countryCode': countryCode, 'number': number},
          'role': [role],
        }),
        headers: {
          'accept': '*/*',
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseJson = json.decode(response.body);
        return responseJson;
      } else {
        return Future.error({
          'statusCode': response.statusCode,
          'message': jsonDecode(response.body)['message'],
        });
      }
    } catch (e) {
      print(e);
      return {};
    }
  }

  //API for delete user
  Future<Map<String, dynamic>> deleteUser(String userId) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUri/admin/user/delete/$userId'),
        headers: {
          'accept': '*/*',
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseJson = json.decode(response.body);
        return responseJson;
      } else {
        return Future.error({
          'statusCode': response.statusCode,
          'message': jsonDecode(response.body)['message'],
        });
      }
    } catch (e) {
      print(e);
      return {};
    }
  }

  //API for checking tenant email exits
  //curl -X 'POST' \
//   'https://chaos.paynation.io/admin/check-email' \
//   -H 'accept: */*' \
//   -H 'Content-Type: application/json' \
//   -d '{
//   "email": "<EMAIL>"
// }'

  Future<Map<String, dynamic>> checkTenantEmail(String email) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUri/admin/check-email'),
        body: {'email': email},
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseJson = json.decode(response.body);
        return {
          'available': true,
          'message': responseJson['message'] ?? '',
        };
      } else {
        return Future.error({
          'available': false,
          'message': jsonDecode(response.body)['message'],
        });
      }
    } catch (e) {
      print(e);
      return {};
    }
  }

  //Api for tenant onboard

  Future<Map<String, dynamic>> tenantOnboard(
    String email,
    String userName,
    String countryCode,
    String number,
  ) async {
//print all data
    print(
      jsonEncode({
        'email': email,
        'user_name': userName,
        'phone': {'countryCode': countryCode, 'number': number},
        'hidden': false,
        'dateAdded':
            "${DateFormat('yyyy-MM-ddTHH:mm:ss.SSSZ').format(DateTime.now())}Z",
      }),
    );
    final response = await http.post(
      Uri.parse('$baseUri/admin/tenant/onboard'),
      body: jsonEncode({
        'email': email,
        'user_name': userName,
        'phone': {'countryCode': countryCode, 'number': number},
        'dateAdded':
            "${DateFormat('yyyy-MM-ddTHH:mm:ss.SSSZ').format(DateTime.now())}Z",
      }),
      headers: {
        'accept': 'application/json',
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ${await _getAuthToken()}',
      },
    );
    if (response.statusCode == 200 || response.statusCode == 201) {
      //final responseJson = json.decode(response.body);
      return {'id': response.body};
    } else {
      return Future.error({
        'statusCode': response.statusCode,
        'message': jsonDecode(response.body)['message'],
      });
    }
  }

//API for hidden Merchant
// curl -X 'PATCH' \
//   'https://chaos.paynation.io/admin/tenant/toggle/frf' \
//   -H 'accept: */*' \
//   -H 'Authorization: Bearer vrevevbetbv'

  Future<Map<String, dynamic>> toggleHiddenTenant(String tenantId) async {
    try {
      final response = await http.patch(
        Uri.parse('$baseUri/admin/tenant/toggle/hiddenMerchant/$tenantId'),
        headers: {
          'accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
      );
      if (response.statusCode == 200) {
        final responseJson = json.decode(response.body);
        return responseJson;
      } else {
        return Future.error({
          'statusCode': response.statusCode,
          'message': jsonDecode(response.body)['message'],
        });
      }
    } catch (e) {
      print(e);
      return {};
    }
  }

  //API for activating or deactivating tenant
  // curl -X 'PATCH' \
  // 'https://chaos.paynation.io/admin/tenant/toggle/65afe348a2f54172c91fd34a' \
  // -H 'accept: */*' \
  // -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2NTllM2I0YWVjNTk2ODdiNzRiMTY2YjUiLCJyb2xlIjpbInN1cGVyLWFkbWluIl0sInVzZXJUeXBlIjoiYWRtaW4iLCJpYXQiOjE3MDY1OTc4NjcsImV4cCI6MTcwNjU5OTY2N30.XV6ALeuWArb9OjFLF7CZOfjf-4ra-Ym_9w_uUsSTNYY'

  Future<Map<String, dynamic>> toggleTenant(String companyId) async {
    try {
      final response = await http.patch(
        Uri.parse('$baseUri/admin/tenant/toggle/$companyId'),
        headers: {
          'accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
      );
      if (response.statusCode == 200) {
        final responseJson = json.decode(response.body);
        return responseJson;
      } else {
        return Future.error({
          'statusCode': response.statusCode,
          'message': jsonDecode(response.body)['message'],
        });
      }
    } catch (e) {
      print(e);
      return {};
    }
  }
  //API for getting details by company Id

  // curl -X 'GET' \
  // 'https://chaos.paynation.io/admin/tenant-company/summary/65afab9ece5a869a0fcabf5c' \
  // -H 'accept: */*' \
  // -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2NTllM2I0YWVjNTk2ODdiNzRiMTY2YjUiLCJyb2xlIjpbInN1cGVyLWFkbWluIl0sInVzZXJUeXBlIjoiYWRtaW4iLCJpYXQiOjE3MDYwMTY1NDgsImV4cCI6MTcwNjAxODM0OH0.e_lWSeN1fzddGdZZJQJNfTDFsgUFHTxdAHjdcpwU9wo'

  Future<Map<String, dynamic>> getTenantCompanyDetails(String companyId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUri/admin/tenant-company/summary/$companyId'),
        headers: {
          'accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
      );
      if (response.statusCode == 200) {
        final responseJson = json.decode(response.body);
        return responseJson;
      } else {
        return Future.error({
          'statusCode': response.statusCode,
          'message': jsonDecode(response.body)['message'],
        });
      }
    } catch (e) {
      print(e);
      return {};
    }
  }

  //Api for tenant deactivate tenant company with companyId

  Future<Map<String, dynamic>> deactivateTenantCompany(String companyId) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUri/admin/tenant/deactivate/tenantCompany/$companyId'),
        body: {'companyId': companyId},
      );
      final responseJson = json.decode(response.body);
      return responseJson;
    } catch (e) {
      print(e);
      return {};
    }
  }

  //Api for tenant deactivate tenant user with userId
  Future<Map<String, dynamic>> deactivateTenantUser(String userId) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUri/admin/tenant/deactivate/$userId'),
        body: {'userId': userId},
      );
      final responseJson = json.decode(response.body);
      return responseJson;
    } catch (e) {
      print(e);
      return {};
    }
  }

  //Api for get tenent companies
  Future<Map<String, dynamic>> getTenantCompanies(
    String page,
    String limit, [
    String? status,
    bool? hidden,
  ]) async {
    print('token${await _getAuthToken()}');
    // try {
    final response = await http.get(
      Uri.parse(
        "$baseUri/admin/tenant-companies?status=${status ?? ""}&page=$page&limit=$limit${hidden != null ? "&hidden=$hidden" : ""}",
      ),
      headers: {
        'Authorization': 'Bearer ${await _getAuthToken()}',
      },
    );
    if (response.statusCode == 200) {
      final responseJson = json.decode(response.body);
      return responseJson;
    } else {
      return Future.error({
        'statusCode': response.statusCode,
        'message': jsonDecode(response.body)['message'],
      });
    }
    // } catch (e) {
    //   print(e);
    //   return {};
    // }
  }

  Future<Map<String, dynamic>> updateTenantCompany(
    String companyName,
    String customSiteName,
    String supportEmail,
    //String darkLogo,
    String companyId,
    String token, {
    String lightLogo = '',
  }) async {
    try {
      final response = await http.patch(
        Uri.parse('$baseUri/admin/update-tenant-company/$companyId'),
        body: jsonEncode({
          'CompanyName': companyName,
          'CustomSiteName': customSiteName,
          'SupportEmail': supportEmail,
          //TODO: send the key of light logo and darl logo from upload api.
          'darkLogo': '',
          'lightLogo': '',
        }),
        headers: {
          'accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );
      if (response.statusCode == 200) {
        final responseJson = json.decode(response.body);
        return responseJson;
      } else {
        return Future.error({
          'statusCode': response.statusCode,
          'message': jsonDecode(response.body)['message'],
        });
      }
    } catch (e) {
      print(e);
      return {};
    }
  }

  Future<Map<String, dynamic>> checkTenantSiteNameExists(
    String customSiteName,
    String token,
  ) async {
    try {
      final response = await http.get(
        Uri.parse(
          '$baseUri/admin/check-tenant-site-name-exists/$customSiteName',
        ),
        headers: {
          'accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );
      if (response.statusCode == 200) {
        final responseJson = json.decode(response.body);
        return responseJson;
      } else {
        return Future.error({
          'statusCode': response.statusCode,
          'message': jsonDecode(response.body)['message'],
        });
      }
    } catch (e) {
      print(e);
      return {};
    }
  }

  Future<Map<String, dynamic>> uploadImages(
    String token,
    String companyId,
    String compulsoryImage,
    Uint8List compulsoryImageFileName, {
    String? optionalImage,
    String? optionalImageFileName,
  }) async {
    final request =
        http.MultipartRequest('POST', Uri.parse('$baseUri/filehandler/upload'));
    //request.fields.addAll({'filePath': companyId, "file": compulsoryImage});
    request.fields.addAll({
      'filePath': companyId,
    });
    request.files
        .add(http.MultipartFile.fromBytes('file', compulsoryImageFileName));
    if (optionalImage != null) {
      request.files
          .add(http.MultipartFile.fromString('optionalImage', optionalImage));
    }

    try {
      final response = await request.send();

      if (response.statusCode == 200) {
        return {'message': 'Images uploaded successfully'};
      } else {
        return Future.error({
          'statusCode': response.statusCode,
          'message': 'Error occured while uploading images',
        });
      }
    } catch (e) {
      print(e);
      return {};
    }
  }

  Future<Map<String, dynamic>> updateSupportStatus(
    String status,
    String serverID,
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUri2/customer/updateSupportStatus'),
        body: jsonEncode({
          'status': status,
          'serverID': serverID,
        }),
        headers: {
          'accept': '*/*',
          'Content-Type': 'application/json',
        },
      ).timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          throw TimeoutException(
              'Request timed out', const Duration(seconds: 30));
        },
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseJson = json.decode(response.body);
        return responseJson;
      } else {
        return Future.error({
          'statusCode': response.statusCode,
          'message': jsonDecode(response.body)['message'],
        });
      }
    } catch (e) {
      print(e);
      // Rethrow the exception instead of returning empty map
      rethrow;
    }
  }

  // API for logout
  Future<Map<String, dynamic>> logout() async {
    try {
      final response = await http.post(
        Uri.parse('$baseUri/dashboard/logout'),
        headers: {
          'accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
      );
      
      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseJson = json.decode(response.body);
        return responseJson;
      } else {
        return Future.error({
          'statusCode': response.statusCode,
          'message': jsonDecode(response.body)['message'],
        });
      }
    } catch (e) {
      print('Logout error: $e');
      return {
        'success': false,
        'message': 'Logout failed',
        'error': e.toString(),
      };
    }
  }
}

final api = Api();
