import 'package:admin_portal/configs/configs_export.dart';
import 'package:admin_portal/configs/themes/app_colors.dart';
import 'package:flutter/material.dart';

class AppTheme {
  static ThemeData themeData(bool isDarkTheme, BuildContext context) {
    return ThemeData(
      primaryColor: isDarkTheme
          ? AppColors.kDarkPrimarySwatch
          : AppColors.kLightPrimarySwatch,
      textTheme: Theme.of(context).textTheme.apply(
            bodyColor: isDarkTheme ? AppColors.kWhite : AppColors.kBlack,
            displayColor: isDarkTheme ? AppColors.kWhite : AppColors.kBlack,
          ),
      dividerColor:
          isDarkTheme ? AppColors.kDarkGrayNight : AppColors.kLightGray,
      iconTheme: IconThemeData(
        color: isDarkTheme ? AppColors.kWhite : AppColors.kBlack,
      ),
      iconButtonTheme: IconButtonThemeData(
        style: ButtonStyle(
          iconColor: WidgetStateProperty.all<Color>(
            isDarkTheme ? AppColors.kWhite : AppColors.kBlack,
          ),
        ),
      ),
      colorScheme: isDarkTheme
          ? ColorScheme.dark(surface: AppColors.kBlack)
          : ColorScheme.light(surface: AppColors.kWhite),
      buttonTheme: ButtonThemeData(
        buttonColor: isDarkTheme
            ? AppColors.kDarkPrimarySwatch
            : AppColors.kLightPrimarySwatch,
        disabledColor: AppColors.kLightGray,
        hoverColor: isDarkTheme ? AppColors.kWhite : AppColors.kBlack,
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: isDarkTheme ? AppColors.kWhite : AppColors.kBlack,
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(
            color:
                isDarkTheme ? AppColors.kDarkGrayDarkNight : AppColors.kBlack,
          ),
        ),
        disabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: AppColors.kLightGray),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(
            color: isDarkTheme ? AppColors.kDarkGrayNight : AppColors.kBlack,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(
            color: isDarkTheme ? AppColors.kDarkGrayNight : AppColors.kBlack,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: AppColors.kRed),
        ),
        //filled: true,
        fillColor: isDarkTheme ? AppColors.kBlack : AppColors.kWhite,
      ),
      cardColor: AppColors.kWhite,
      dialogBackgroundColor: isDarkTheme ? AppColors.kBlack : AppColors.kWhite,
      cardTheme: CardThemeData(
        color: isDarkTheme ? AppColors.kBlack : AppColors.kWhite,
        surfaceTintColor: isDarkTheme ? AppColors.kBlack : AppColors.kWhite,
      ),
      listTileTheme: ListTileThemeData(
        selectedTileColor: isDarkTheme
            ? AppColors.kDarkPrimarySwatch
            : AppColors.kLightPrimarySwatch,
        selectedColor: isDarkTheme ? AppColors.kWhite : AppColors.kBlack,
        textColor: isDarkTheme ? AppColors.kWhite : AppColors.kBlack,
      ),
      dialogTheme: DialogThemeData(
        backgroundColor: isDarkTheme ? AppColors.kBlack : AppColors.kWhite,
        surfaceTintColor: isDarkTheme ? AppColors.kBlack : AppColors.kWhite,
      ),
      dropdownMenuTheme: DropdownMenuThemeData(
        inputDecorationTheme: InputDecorationTheme(
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(color: AppColors.kLightPrimarySwatch),
          ),
        ),
      ),
      dividerTheme: DividerThemeData(
        color: isDarkTheme ? AppColors.kDarkGrayNight : AppColors.kLightGray,
      ),
      dataTableTheme: DataTableThemeData(
        headingRowColor: WidgetStateColor.resolveWith((states) {
          if (isDarkTheme) {
            return AppColors.kDarkGrayLight;
          } else {
            return AppColors.kLightGrayLight;
          }
        }),
        // dataRowColor: MaterialStateColor.resolveWith((states) {
        //   if (isDarkTheme) {
        //     return AppColors.kDarkGrayDarkNight;
        //   } else {
        //     return AppColors.kWhite;
        //   }
        // }),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: isDarkTheme
              ? AppColors.kDarkPrimarySwatch
              : AppColors.kLightPrimarySwatch,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      ),
      tabBarTheme: TabBarThemeData(
        indicatorColor: isDarkTheme
            ? AppColors.kDarkPrimarySwatch
            : AppColors.kLightPrimarySwatch,
        labelColor: isDarkTheme
            ? AppColors.kDarkPrimarySwatch
            : AppColors.kLightPrimarySwatch,
      ),
      useMaterial3: true,
    );
  }
}
