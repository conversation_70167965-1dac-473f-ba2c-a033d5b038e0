import 'package:admin_portal/configs/configs_export.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:responsive_framework/responsive_framework.dart';

class AppTextStyles {
  AppTextStyles._();

  static TextStyle textStyleDashboardTitle(BuildContext context) {
    return GoogleFonts.lato(
      fontSize: ResponsiveValue(
        context,
        defaultValue: 14.0,
        conditionalValues: [
          Condition.equals(name: MOBILE, value: 24.toDouble()),
          Condition.equals(name: TABLET, value: 24.toDouble()),
          Condition.equals(name: DESKTOP, value: 26.toDouble()),
        ],
      ).value,
      fontWeight: FontWeight.bold,
    );
  }

  static TextStyle textStyleDashboardMessage(BuildContext context) {
    return GoogleFonts.lato(
      fontSize: ResponsiveValue(
        context,
        defaultValue: 14.0,
        conditionalValues: [
          Condition.equals(name: MO<PERSON><PERSON>, value: 14.toDouble()),
          Condition.equals(name: TABLET, value: 14.toDouble()),
          Condition.equals(name: DESKTOP, value: 14.toDouble()),
        ],
      ).value,
      fontWeight: FontWeight.bold,
    );
  }

  static TextStyle textStyleFormFieldHintText(BuildContext context) {
    return GoogleFonts.lato(
      fontSize: ResponsiveValue(
        context,
        defaultValue: 14.0,
        conditionalValues: [
          Condition.equals(name: MOBILE, value: 14.toDouble()),
          Condition.equals(name: TABLET, value: 14.toDouble()),
          Condition.equals(name: DESKTOP, value: 14.toDouble()),
        ],
      ).value,
      color: AppColors.kLightGray,
      fontWeight: FontWeight.w400,
    );
  }

  static TextStyle textStyleBlueBold(BuildContext context) {
    return GoogleFonts.lato(
      fontSize: ResponsiveValue(
        context,
        defaultValue: 14.0,
        conditionalValues: [
          Condition.equals(name: MOBILE, value: 14.toDouble()),
          Condition.equals(name: TABLET, value: 14.toDouble()),
          Condition.equals(name: DESKTOP, value: 14.toDouble()),
        ],
      ).value,
      color: AppColors.kLightPrimarySwatch,
      fontWeight: FontWeight.bold,
    );
  }

  static TextStyle textStyleBold26(BuildContext context) {
    return GoogleFonts.lato(
      fontSize: ResponsiveValue(
        context,
        defaultValue: 14.0,
        conditionalValues: [
          Condition.equals(name: MOBILE, value: 14.toDouble()),
          Condition.equals(name: TABLET, value: 14.toDouble()),
          Condition.equals(name: DESKTOP, value: 26.toDouble()),
        ],
      ).value,
      fontWeight: FontWeight.bold,
    );
  }

  static TextStyle textStyleBold22(BuildContext context) {
    return GoogleFonts.lato(
      fontSize: ResponsiveValue(
        context,
        defaultValue: 14.0,
        conditionalValues: [
          Condition.equals(name: MOBILE, value: 14.toDouble()),
          Condition.equals(name: TABLET, value: 14.toDouble()),
          Condition.equals(name: DESKTOP, value: 22.toDouble()),
        ],
      ).value,
      fontWeight: FontWeight.bold,
    );
  }

  static TextStyle textStyleBold18(BuildContext context) {
    return GoogleFonts.lato(
      fontSize: ResponsiveValue(
        context,
        defaultValue: 14.0,
        conditionalValues: [
          Condition.equals(name: MOBILE, value: 14.toDouble()),
          Condition.equals(name: TABLET, value: 14.toDouble()),
          Condition.equals(name: DESKTOP, value: 18.toDouble()),
        ],
      ).value,
      fontWeight: FontWeight.bold,
    );
  }

  static TextStyle textStyleBold16(BuildContext context) {
    return GoogleFonts.lato(
      fontSize: ResponsiveValue(
        context,
        defaultValue: 14.0,
        conditionalValues: [
          Condition.equals(name: MOBILE, value: 14.toDouble()),
          Condition.equals(name: TABLET, value: 14.toDouble()),
          Condition.equals(name: DESKTOP, value: 16.toDouble()),
        ],
      ).value,
      fontWeight: FontWeight.bold,
    );
  }

  static TextStyle textStyleBold14(BuildContext context) {
    return GoogleFonts.lato(
      fontSize: ResponsiveValue(
        context,
        defaultValue: 14.0,
        conditionalValues: [
          Condition.equals(name: MOBILE, value: 14.toDouble()),
          Condition.equals(name: TABLET, value: 14.toDouble()),
          Condition.equals(name: DESKTOP, value: 14.toDouble()),
        ],
      ).value,
      fontWeight: FontWeight.bold,
    );
  }

  //latoBold12
  static TextStyle textStyleBold12(BuildContext context) {
    return GoogleFonts.lato(
      fontSize: ResponsiveValue(
        context,
        defaultValue: 14.0,
        conditionalValues: [
          Condition.equals(name: MOBILE, value: 12.toDouble()),
          Condition.equals(name: TABLET, value: 12.toDouble()),
          Condition.equals(name: DESKTOP, value: 12.toDouble()),
        ],
      ).value,
      fontWeight: FontWeight.bold,
    );
  }

  //Regular Styles

  static TextStyle textStyleRegular14(BuildContext context) {
    return GoogleFonts.lato(
      fontSize: ResponsiveValue(
        context,
        defaultValue: 14.0,
        conditionalValues: [
          Condition.equals(name: MOBILE, value: 14.toDouble()),
          Condition.equals(name: TABLET, value: 14.toDouble()),
          Condition.equals(name: DESKTOP, value: 14.toDouble()),
        ],
      ).value,
      fontWeight: FontWeight.w400,
    );
  }

  static TextStyle textStyleRegular16(BuildContext context) {
    return GoogleFonts.lato(
      fontSize: ResponsiveValue(
        context,
        defaultValue: 14.0,
        conditionalValues: [
          Condition.equals(name: MOBILE, value: 14.toDouble()),
          Condition.equals(name: TABLET, value: 14.toDouble()),
          Condition.equals(name: DESKTOP, value: 16.toDouble()),
        ],
      ).value,
      fontWeight: FontWeight.w400,
    );
  }
}
