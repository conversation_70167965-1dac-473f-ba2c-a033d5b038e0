import 'dart:convert';
import 'dart:developer';

import 'package:admin_portal/api/api.dart';
import 'package:admin_portal/models/models_export.dart';
import 'package:admin_portal/models/order_list_model.dart';
import 'package:admin_portal/models/order_list_model_new.dart';
import 'package:admin_portal/models/paginated_result.dart';
import 'package:admin_portal/models/support_datamodel.dart';
import 'package:admin_portal/models/support_list_model.dart';
import 'package:admin_portal/utils/app_strings.dart';
import 'package:admin_portal/utils/constants/common_const.dart';
import 'package:admin_portal/utils/constants/global_config.dart';
import 'package:admin_portal/utils/helper_functions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';

class ManageMerchantProvider extends ChangeNotifier {
  List<CustomersNew> tempListOfDetails = [];
  List<Customers> tempSupportListOfDetails = [];
  List<MerchantDetails> merchantDetails = [];
  List<HostNameForDropdown> hostDropDownList = [];
  List<CustomersNew> getOrderList = [];
  GetOrderListNew getOrderListDetails = GetOrderListNew();
  GetSupportList getSupportListNew = GetSupportList();
  List<CustomersNew> getOrderDetails = [];
  List<Customers> getSupportList = [];
  List<Customers> getSupportDetails = [];


  int selectedIndex = 0;
  int selectedSupportIndex = 0;
  int get firstRowIndex => selectedIndex;

  void updateFirstRowIndex(int index) {
    selectedIndex = index;
    notifyListeners(); // So the widget rebuilds with updated range
  }
  void updateFirstRowSupportIndex(int index) {
    selectedSupportIndex = index;
    notifyListeners();
  }

  bool allChecked = false;

  bool isLoading = false;
  bool isSearching = false;
  bool isPaginationLoading = false; // Loading state for pagination

  List<CustomersNew> originalList = []; // Holds the unfiltered original list

  DateTime? selectedDate; // Selected date for filtering
  List<String> selectedStatuses = []; // List of selected statuses for filtering
  bool isFilterApplied = false; // To track if a filter is applied
  int filterStateCounter = 0; // Counter to force table rebuild when filters change

  PaginatedResult<Customers> totalPaginatedResult = PaginatedResult.empty();

  bool isFoundNothing = false;
  bool isAscending = true; // For full name sort
  bool isEmailAscending = true; // For email sort
  bool isOrderIdAscending = true; // For order ID sort
  bool isAcceptedOnAscending = true;
  bool isServersAscending = true;
  bool isHostnamesAscending = true;
  bool isStatusAscending = true;
  String lastOrderSort = "acceptedOn";

  bool isSupportListUserIdAscending = true; // For User Id sort
  bool isSupportListFullNameAscending = true; // For First Name sort
  bool isSupportListEmailAscending = true; // For Email sort
  bool isSupportListMobileNumberAscending = true; // For Mobile Number sort
  bool isSupportListCryptoAddressAscending = true; // For Crypto Address sort
  String lastSupportListSort = orderByFirstName;

  int? total = 0;
  int? totalSupportList = 0;
  int? currentSupportPage = 1;
  int? currentPage = 1;

  void getMerchantDetails(
    int page,
    int limit, [
    String? status,
    bool? hidden,
  ]) async {
    try {
      final response =
          await api.getTenantCompanies('$page', '$limit', status ?? '', hidden);
      final merchantDetailsResponse =
          MerchantDetailsResponse.fromJson(response);
      merchantDetails = merchantDetailsResponse.documents;
      notifyListeners();

      // Debug prints
      print(response);
      print(merchantDetails.first.merchantId);
    } catch (error) {
      print(error);
      if (error is Map<String, dynamic>) {
        // Handle error based on status code if needed
      }
    }
  }

  // Unused utility method, left here if it’s part of the overall logic elsewhere
  List<GetOrderList> parseList(String responseBody) {
    final parsed = json.decode(responseBody).cast<Map<String, dynamic>>();
    return parsed
        .map<GetOrderList>((json) => GetOrderList.fromJson(json))
        .toList();
  }

  ManageMerchantData? tableSource;
  SupportData? supportTableSource;

  void getOrderListData(int page, int limit, BuildContext context,
      {String sort = 'DESC', //'ASC',
      String search = ''}) async {
    try {
      // Set pagination loading state
      isPaginationLoading = true;

      currentPage = page;
      notifyListeners(); // Optional if UI shows loading state
      GlobalConfig().currentPageOrderListPagination = page;

      final response = await api.getOrderList(
          '$page',
          '${GlobalConfig().pageLimit}',
          sort,
          lastOrderSort,
          search,
          selectedDate,
          selectedStatuses);
      final parsedResponse = GetOrderListNew.fromJson(response);

      total = parsedResponse.total;
      getOrderListDetails = parsedResponse;
      originalList = parsedResponse.customers ?? [];
      getOrderList = List.from(originalList);
      // Sort lists by acceptedOn
      originalList.sort((a, b) => DateTime.parse(b.acceptedOn!)
          .compareTo(DateTime.parse(a.acceptedOn!)));
      getOrderList.sort((a, b) => DateTime.parse(b.acceptedOn!)
          .compareTo(DateTime.parse(a.acceptedOn!)));

      hostDropDownList = parsedResponse.hostNameForDropdown ?? [];
      if (tableSource == null) {
        tableSource = ManageMerchantData(
          total!,
          getOrderList,
          hostDropDownList,
          context,
        );
      } else {
        tableSource?.updateData(getOrderList, total!);
      }
      currentPage = page;
      updateFirstRowIndex((page - 1) * limit);
      isLoading = false;
      isPaginationLoading = false;
      notifyListeners();
    } catch (error) {
      if (kDebugMode) {
        print(error);
      }
      if (error is Map<String, dynamic>) {
        // Optional: Handle API error
      }
    } finally {
      isLoading = false;
      isPaginationLoading = false;
      notifyListeners();
    }
  }

  void getOrderDataDetails(int page, int limit, String email) async {
    try {
      isLoading = true;
      notifyListeners();

      getOrderDetails.clear();
      final response = await api.getOrderDetails('$page', '$limit', email);
      getOrderDetails.add(CustomersNew.fromJson(response));

      if (kDebugMode) {
        print('getOrderDataDetails : $response');
      }
      isLoading = false;
      notifyListeners();
    } catch (error) {
      if (kDebugMode) {
        print(error);
      }
      if (error is Map<String, dynamic>) {
        // Handle error based on status code if needed
      }
    }
  }

  void getSupportListData(
    int page,
    int limit,
    BuildContext context, {
    String sort = 'DESC',
    String search = '',
    String? status,
    bool? hidden,
  }) async {
    try {
      isPaginationLoading = true;

      currentPage = page;
      notifyListeners();
      GlobalConfig().currentPageSupportListPagination = page;
      final response = await api.getSupportList('$page',
          '${GlobalConfig().pageLimit}', sort, lastSupportListSort, search,);
      getSupportList = GetSupportList.fromJson(response).customers ?? [];
      getSupportListNew = GetSupportList.fromJson(response);
      updateFirstRowSupportIndex((page - 1) * limit);
      totalSupportList= getSupportListNew.total;
      isLoading = false;
      isSearching = false;
      isPaginationLoading = false;
      if (supportTableSource == null) {
        supportTableSource = SupportData(
          totalSupportList!,
          getSupportList,
        );
      } else {
        supportTableSource?.updateData(getSupportList, totalSupportList!);
      }
      if (getSupportList.isEmpty) {
        isFoundNothing = true;
      } else {
        isFoundNothing = false;
      }
      notifyListeners();
    } catch (error) {
      print(error);
      if (error is Map<String, dynamic>) {
        // Handle error based on status code if needed
      }
    } finally {
      isSearching = false;
      isLoading = false;
      notifyListeners();
    }
  }

  void getSupportDetailsData(
    int page,
    int limit,
    String userId, [
    String? status,
    bool? hidden,
  ]) async {
    try {
      isLoading = true;
      final response = await api.getSupportDetails('$page', '$limit', userId);
      getSupportDetails = GetSupportList.fromJson(response).customers ?? [];

      print(response);
      notifyListeners();
    } catch (error) {
      print(error);
      if (error is Map<String, dynamic>) {
        // Handle error based on status code if needed
      }
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }

  void changeSelectedIndex(int index) {
    selectedIndex = index;
    getMerchantDetails(
      1,
      10,
      getMerchantFilter(index),
      index == 3 ? true : null,
    );
  }

  String getMerchantFilter(int index) {
    switch (index) {
      case 0:
        return '';
      case 1:
        return 'active';
      case 2:
        return 'inactive';
      case 3:
        return '';
      default:
        return '';
    }
  }

  void buildSearchList(String userSearchTerm) {
    tempListOfDetails.clear();
    notifyListeners();

    tempListOfDetails = getOrderList
        .where(
          (item) =>
              (item.customerInfo?.fullName ?? '')
                  .toLowerCase()
                  .contains(userSearchTerm.toLowerCase()) ||
              (item.customerInfo?.email ?? '')
                  .toLowerCase()
                  .contains(userSearchTerm.toLowerCase()) ||
              (item.acceptedOn ?? '')
                  .toLowerCase()
                  .contains(userSearchTerm.toLowerCase()) ||
              (item.customerName ?? '')
                  .toLowerCase()
                  .contains(userSearchTerm.toLowerCase()) ||
              (item.orderId ?? '')
                  .toLowerCase()
                  .contains(userSearchTerm.toLowerCase()) ||
              (item.status ?? '')
                  .toLowerCase()
                  .contains(userSearchTerm.toLowerCase()),
        )
        .toList();
    log('Refresh called length ${tempListOfDetails.length} : mainlist ${getOrderList.length} : search key:$userSearchTerm');
    notifyListeners();
  }

  void buildSearchList2(String userSearchTerm) {
    tempListOfDetails.clear();
    notifyListeners();

    for (var i = 0; i < getOrderList.length; i++) {
      var name = (getOrderList[i].customerInfo?.email ?? '') +
          (getOrderList[i].customerName ?? '') +
          (getOrderList[i].orderId?.isNotEmpty == true
              ? getOrderList[i].orderId!
              : '') +
          (getOrderList[i].status?.isNotEmpty == true
              ? getOrderList[i].status!
              : '') +
          (getOrderList[i].acceptedOn?.isNotEmpty == true
              ? getOrderList[i].acceptedOn!
              : '');

      log('search:name $name');

      if (name.toLowerCase().contains(userSearchTerm.toLowerCase())) {
        log('search:name $name');
        tempListOfDetails.add(getOrderList[i]);
      }
    }

    log('Refresh called length ${tempListOfDetails.length} : mainlist ${getOrderList.length} : search key:$userSearchTerm');
    notifyListeners();
  }

  void buildSearchSupportList2(String userSearchTerm) {
    tempSupportListOfDetails.clear();
    notifyListeners();

    tempSupportListOfDetails = getSupportList
        .where(
          (item) =>
              (item.firstName ?? '')
                  .toLowerCase()
                  .contains(userSearchTerm.toLowerCase()) ||
              (item.lastName ?? '')
                  .toLowerCase()
                  .contains(userSearchTerm.toLowerCase()) ||
              (item.orders?.isNotEmpty == true
                      ? item.orders![0].orderId ?? ''
                      : '')
                  .toLowerCase()
                  .contains(userSearchTerm.toLowerCase()) ||
              (item.phone?.countryCode ?? '')
                  .toLowerCase()
                  .contains(userSearchTerm.toLowerCase()) ||
              (item.phone?.number ?? '')
                  .toLowerCase()
                  .contains(userSearchTerm.toLowerCase()),
        )
        .toList();
    notifyListeners();
  }

  Future<void> buildSearchSupportList(String userSearchTerm) async {
    tempSupportListOfDetails.clear();

    if (userSearchTerm.trim().isEmpty) {
      isSearching = false;
      isFoundNothing = false;
      notifyListeners();
      return;
    }

    isSearching = true;
    notifyListeners();

    await compute(_searchCustomers, userSearchTerm);

    log('Refresh called length ${tempSupportListOfDetails.length} '
        ': main list ${getSupportList.length} : search key:$userSearchTerm');

    isSearching = false;
    notifyListeners();
  }

  Future<void> _searchCustomers(String userSearchTerm) async {
    final searchTermLower = userSearchTerm.toLowerCase();
    final filteredList = getSupportList.where((support) {
      final buffer = StringBuffer()
        ..write(support.firstName ?? '')
        ..write(" ${support.lastName ?? ''}")
        ..write(support.orders?.isNotEmpty == true
            ? " ${support.orders![0].orderId ?? ''}"
            : '')
        ..write(" ${support.phone?.countryCode ?? ''}")
        ..write(" ${support.phone?.number ?? ''}")
        ..write(" ${support.email ?? ''}")
        ..write(" ${support.cryptoWallet?.address ?? ''}");

      final result = buffer.toString().toLowerCase().contains(searchTermLower);
      debugPrint('Search result for $searchTermLower in $buffer is $result');
      return result;
    }).toList();

    // Simulate a tiny delay if needed
    await Future.delayed(const Duration(milliseconds: 300));
    tempSupportListOfDetails = filteredList;

    if (userSearchTerm.trim().isNotEmpty && tempSupportListOfDetails.isEmpty) {
      isFoundNothing = true;
    } else {
      isFoundNothing = false;
    }
  }

  void setAllChecked(bool? value) {
    log('checked box valed: $value');
    allChecked = value ?? false;

    for (var i = 0; i < getSupportList.length; i++) {
      getSupportList[i].isCheck = allChecked;
    }
    notifyListeners();
  }

  void setItemChecked(bool? value, int index) {
    log('checked box valed: $value');
    if (tempSupportListOfDetails.isNotEmpty) {
      final selectedIndex = getSupportList.indexWhere(
        (element) => element.userId == tempSupportListOfDetails[index].userId,
      );
      log('selected search index $selectedIndex');
      getSupportList[selectedIndex].isCheck = value ?? false;
      tempSupportListOfDetails[index].isCheck = value ?? false;
    } else {
      getSupportList[index].isCheck = value ?? false;
    }
    notifyListeners();
  }

  /// Full Name Sorting logic
  void buildSortedListFullName() {
    isLoading = true;
    if (tempListOfDetails.isNotEmpty) {
      tempListOfDetails.clear();
    }

    tempListOfDetails = getOrderList.toList()
      ..sort((a, b) {
        final nameA = (a.customerInfo?.fullName ?? '')
            .replaceAll(RegExp(r'[^a-zA-Z\s]'), '')
            .trim()
            .toLowerCase();
        final nameB = (b.customerInfo?.fullName ?? '')
            .replaceAll(RegExp(r'[^a-zA-Z\s]'), '')
            .trim()
            .toLowerCase();
        return nameA.compareTo(nameB);
      });

    if (!isAscending) {
      tempListOfDetails = tempListOfDetails.reversed.toList();
    }
    isAscending = !isAscending;

    // Only shift if no filter is applied
    if (!isFilterApplied && tempListOfDetails.length > 4) {
      final firstFour = tempListOfDetails.sublist(0, isAscending ? 4 : 0);
      tempListOfDetails.removeRange(0, !isAscending ? 4 : 0);
      tempListOfDetails.addAll(firstFour);
    }

    isLoading = false;
    log('List sorted: length ${tempListOfDetails.length}');
    notifyListeners();
  }

  /// Email Sorting logic
  void buildSortedListByEmail() {
    isLoading = true;
    if (tempListOfDetails.isNotEmpty) {
      tempListOfDetails.clear();
    }

    tempListOfDetails = getOrderList.toList()
      ..sort((a, b) {
        final emailA = (a.customerInfo?.email ?? '').trim().toLowerCase();
        final emailB = (b.customerInfo?.email ?? '').trim().toLowerCase();
        return emailA.compareTo(emailB);
      });

    if (!isEmailAscending) {
      tempListOfDetails = tempListOfDetails.reversed.toList();
    }
    isEmailAscending = !isEmailAscending;

    // Only shift if no filter is applied
    if (!isFilterApplied && tempListOfDetails.length > 4) {
      final firstFour = tempListOfDetails.sublist(0, isEmailAscending ? 4 : 0);
      tempListOfDetails.removeRange(0, !isEmailAscending ? 4 : 0);
      tempListOfDetails.addAll(firstFour);
    }

    isLoading = false;
    log('List sorted by email: length ${tempListOfDetails.length}');
    notifyListeners();
  }

  /// Order ID Sorting logic
  void buildSortedListByOrderId() {
    isLoading = true;
    if (tempListOfDetails.isNotEmpty) {
      tempListOfDetails.clear();
    }

    tempListOfDetails = getOrderList.toList()
      ..sort((a, b) {
        final orderIdA = (a.orderId ?? '').trim().toLowerCase();
        final orderIdB = (b.orderId ?? '').trim().toLowerCase();
        return orderIdA.compareTo(orderIdB);
      });

    if (!isOrderIdAscending) {
      tempListOfDetails = tempListOfDetails.reversed.toList();
    }
    isOrderIdAscending = !isOrderIdAscending;

    isLoading = false;
    log('List sorted by order ID: length ${tempListOfDetails.length}');
    notifyListeners();
  }

  /// Purchased Date (acceptedOn) Sorting Logic
  void buildSortedListByAcceptedOn() {
    isLoading = true;
    if (tempListOfDetails.isNotEmpty) {
      tempListOfDetails.clear();
    }

    tempListOfDetails = getOrderList.toList()
      ..sort((a, b) {
        final dateA = HelperFunctions.convertToDate(a.acceptedOn!);
        final dateB = HelperFunctions.convertToDate(b.acceptedOn!);
        return dateA.compareTo(dateB);
      });

    if (!isAcceptedOnAscending) {
      tempListOfDetails = tempListOfDetails.reversed.toList();
    }
    isAcceptedOnAscending = !isAcceptedOnAscending;

    isLoading = false;
    log('List sorted by acceptedOn: length ${tempListOfDetails.length}');
    notifyListeners();
  }

  /// Servers Sorting logic
  void buildSortedListByServers() {
    isLoading = true;
    if (tempListOfDetails.isNotEmpty) {
      tempListOfDetails.clear();
    }

    tempListOfDetails = getOrderList.toList()
      ..sort((a, b) {
        final onlineCountA =
            a.servers?.where((server) => server.isOnline == true).length ?? 0;
        final totalCountA = a.servers?.length ?? 0;

        final onlineCountB =
            b.servers?.where((server) => server.isOnline == true).length ?? 0;
        final totalCountB = b.servers?.length ?? 0;

        var comparison = onlineCountA.compareTo(onlineCountB);
        if (comparison == 0) {
          comparison = totalCountA.compareTo(totalCountB);
        }
        return comparison;
      });

    if (!isServersAscending) {
      tempListOfDetails = tempListOfDetails.reversed.toList();
    }
    isServersAscending = !isServersAscending;

    isLoading = false;
    log('List sorted by servers: length ${tempListOfDetails.length}');
    notifyListeners();
  }

  /// Hostname Sorting logic
  void buildSortedListByHostnames() {
    isLoading = true;
    if (tempListOfDetails.isNotEmpty) {
      tempListOfDetails.clear();
    }

    tempListOfDetails = getOrderList.toList()
      ..sort((a, b) {
        final nonEmptyHostnamesA = a.servers
                ?.where((server) => (server.hostName ?? '').isNotEmpty)
                .length ??
            0;
        final totalCountA = a.servers?.length ?? 0;

        final nonEmptyHostnamesB = b.servers
                ?.where((server) => (server.hostName ?? '').isNotEmpty)
                .length ??
            0;
        final totalCountB = b.servers?.length ?? 0;

        var comparison = nonEmptyHostnamesA.compareTo(nonEmptyHostnamesB);
        if (comparison == 0) {
          comparison = totalCountA.compareTo(totalCountB);
        }
        return comparison;
      });

    if (!isHostnamesAscending) {
      tempListOfDetails = tempListOfDetails.reversed.toList();
    }
    isHostnamesAscending = !isHostnamesAscending;

    isLoading = false;
    log('List sorted by hostnames: length ${tempListOfDetails.length}');
    notifyListeners();
  }

  /// Order Status Sorting logic
  void buildSortedListByStatus() {
    isLoading = true;
    if (tempListOfDetails.isNotEmpty) {
      tempListOfDetails.clear();
    }

    final statusPriority = <String, int>{
      AppStrings.Provisioning: 1,
      AppStrings.Configuring: 2,
      AppStrings.QATesting: 3,
      AppStrings.QA_Testing: 3,
      AppStrings.PackAndShip: 4,
      AppStrings.PackShip: 4,
      AppStrings.Shipped: 5,
      AppStrings.Received: 6,
    };

    tempListOfDetails = getOrderList.toList()
      ..sort((a, b) {
        final priorityA = statusPriority[a.status] ?? 100;
        final priorityB = statusPriority[b.status] ?? 100;
        return priorityA.compareTo(priorityB);
      });

    if (!isStatusAscending) {
      tempListOfDetails = tempListOfDetails.reversed.toList();
    }
    isStatusAscending = !isStatusAscending;

    isLoading = false;
    log('List sorted by status: length ${tempListOfDetails.length}');
    notifyListeners();
  }

  /// Filter related methods
  void addSelectedStatus(String status) {
    if (!selectedStatuses.contains(status)) {
      selectedStatuses.add(status);
      notifyListeners();
    }
  }

  void removeSelectedStatus(String status) {
    if (selectedStatuses.contains(status)) {
      selectedStatuses.remove(status);
      notifyListeners();
    }
  }

  void setSelectedDate(DateTime date) {
    selectedDate = date;
    notifyListeners();
  }

  void applyFilter(BuildContext context) {
    isLoading = true;
    
    // Reset to page 1 and update the first row index when applying filter
    updateFirstRowIndex(0);
    getOrderListData(1, GlobalConfig().pageLimit, context, search: "");
    // getOrderList = List.from(originalList);
    //
    // getOrderList = getOrderList.where((order) {
    //   final statusMatch =
    //       selectedStatuses.isEmpty || selectedStatuses.contains(order.status);
    //   final dateMatch = order.createdAt != null && selectedDate != null
    //       ? DateTime.parse(order.createdAt!).isAfter(selectedDate!)
    //       : true;
    //   return statusMatch && dateMatch;
    // }).toList();

    isFilterApplied = true;
    
    // Increment filter state counter to force table rebuild
    filterStateCounter++;
    
    // if (getOrderList.isEmpty) {
    //   getOrderList = List.from(originalList);
    //   isFilterApplied = false;
    // }

    // tempListOfDetails.clear();
    isLoading = false;
    notifyListeners();
  }

  void clearFilter(BuildContext context) {
    isLoading = true;
    selectedStatuses.clear();
    selectedDate = null;
    
    // Reset to page 1 and update the first row index
    updateFirstRowIndex(0);
    getOrderListData(1, GlobalConfig().pageLimit, context);
    getOrderList = List.from(originalList);

    tempListOfDetails.clear();
    isFilterApplied = false;
    
    // Increment filter state counter to force table rebuild
    filterStateCounter++;

    isLoading = false;
    notifyListeners();
  }

  void filterAssignedHostNames() {
    final assignedHostNames = <String>{};

    for (final order in originalList) {
      if (order.servers != null) {
        for (final server in order.servers!) {
          if (server.hostName != null && server.hostName!.isNotEmpty) {
            assignedHostNames.add(server.hostName!.trim().toLowerCase());
          }
        }
      }
    }

    if (kDebugMode) {
      print('Assigned Host Names: $assignedHostNames');
    }

    hostDropDownList.removeWhere((hostItem) {
      final hostName = hostItem.hostName?.trim().toLowerCase();
      return hostName != null && assignedHostNames.contains(hostName);
    });

    if (kDebugMode) {
      print('Filtered Host Names: ${hostDropDownList.map((e) => e.hostName)}');
    }
    notifyListeners();
  }

  /// Update support status for a specific server
  Future<Map<String, dynamic>> updateSupportStatus(
    String status,
    String serverID,
  ) async {
    try {
      isLoading = true;
      notifyListeners();

      final response = await api.updateSupportStatus(status, serverID);

      isLoading = false;
      notifyListeners();
      return response;
    } catch (error) {
      isLoading = false;
      notifyListeners();
      print('Error updating support status: $error');
      rethrow;
    }
  }
}
