import 'package:admin_portal/api/api.dart';
import 'package:admin_portal/utils/utils_export.dart';
import 'package:flutter/material.dart';
import 'package:reactive_forms/reactive_forms.dart';

class AddMerchantProvider extends ChangeNotifier {
  final formKey = GlobalKey<FormState>();
  final phoneFormKey = GlobalKey<FormState>();
  final reactiveForm = FormGroup({
    'email': FormControl<String>(
      validators: [
        Validators.required,
        Validators.minLength(5),
        Validators.maxLength(50),
        Validators.pattern(AppValidators.pattern),
      ],
      asyncValidators: [
        UniqueEmailAsyncValidator(),
      ],
      asyncValidatorsDebounceTime: 1000,
    ),
  });
  final emailController = TextEditingController();
  final confirmEmailController = TextEditingController();
  final firstNameController = TextEditingController();
  final lastNameController = TextEditingController();
  final phoneController = TextEditingController();
  final countryCodeController = TextEditingController();

  final emailFocusNode = FocusNode();
  final confirmEmailFocusNode = FocusNode();
  final firstNameFocusNode = FocusNode();
  final lastNameFocusNode = FocusNode();
  final phoneFocusNode = FocusNode();

  bool isAddMerchantSuccess = false;

  void checkEmail(String email) async {
    final validEmail = AppValidators.validateEmail(email);
    if (validEmail != null) {
      print(validEmail);
    } else {
      print('Valid Email');
    }
  }

  void addMerchantSuccess() {
    isAddMerchantSuccess = true;
    clearAllControllers();
    notifyListeners();
  }

  void addMerchantBack() {
    isAddMerchantSuccess = false;
    notifyListeners();
  }

  void addMerchant() async {
    try {
      final response = await api.tenantOnboard(
        emailController.text,
        firstNameController.text + lastNameController.text,
        countryCodeController.text,
        phoneController.text,
      );
      addMerchantSuccess();
    } catch (e) {
      print(e);
    }
  }

  void checkName(String name) async {
    final validName = AppValidators.validateName(name);
    if (validName != null) {
      print(validName);
    } else {
      print('Valid Name');
    }
  }

  void clearAllControllers() {
    emailController.clear();
    confirmEmailController.clear();
    firstNameController.clear();
    lastNameController.clear();
    phoneController.clear();
    countryCodeController.clear();
  }
  // void checkPhone(String phone) async {
  //   String? validPhone = AppValidators.validatePhone(phone);
  //   if (validPhone != null) {
  //     print(validPhone);
  //   } else {
  //     print('Valid Phone');
  //   }
  // }

  // void checkAddress(String address) async {
  //   String? validAddress = AppValidators.validateAddress(address);
  //   if (validAddress != null) {
  //     print(validAddress);
  //   } else {
  //     print('Valid Address');
  //   }
  // }

  // void checkCity(String city) async {
  //   String? validCity = AppValidators.validateCity(city);
  //   if (validCity != null) {
}
