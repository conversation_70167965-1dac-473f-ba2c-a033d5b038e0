import 'dart:convert';
import 'dart:developer';

import 'package:admin_portal/api/api.dart';
import 'package:admin_portal/configs/configs_export.dart';
import 'package:admin_portal/models/models_export.dart';
import 'package:admin_portal/models/order_list_model_new.dart';
import 'package:admin_portal/provider/provider_export.dart';
import 'package:admin_portal/utils/utils_export.dart';
import 'package:admin_portal/widgets/hostname_selection_widget.dart';
import 'package:admin_portal/widgets/widgets_export.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:intl_phone_field/country_picker_dialog.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:provider/provider.dart';

class MyTeamProvider extends ChangeNotifier {
  List<AdminUsers> adminUsers = [];
  TextEditingController firstNameController = TextEditingController();
  FocusNode firstNameFocusNode = FocusNode();
  TextEditingController lastNameController = TextEditingController();
  FocusNode lastNameFocusNode = FocusNode();
  TextEditingController emailController = TextEditingController();
  FocusNode emailFocusNode = FocusNode();

  TextEditingController phoneController = TextEditingController();
  TextEditingController countryCodeController = TextEditingController();
  FocusNode phoneFocusNode = FocusNode();
  String dropDownValue = 'ADMIN';
  String hostDropDownValue = AppStrings.dropdownDefalutName;

  String countryCodeString = '';

  List<HostNameForDropdown> countries = [];
  TextEditingController controller = TextEditingController();
  late String filter;

  final FocusNode _focusNode = FocusNode();
  //Api for get users
  Future<void> getAdminUsers() async {
    try {
      final response = await api.getUsers();
      adminUsers = AdminUsersResponseModel.fromJson(response).documents;
      notifyListeners();
    } catch (e) {
      print(e);
    }
  }

  //Api for add users
  Future<void> addAdminUsers(BuildContext context) async {
    try {
      final response = await api.createUser(
        firstNameController.text + lastNameController.text,
        emailController.text,
        phoneController.text,
        countryCodeController.text,
      );
      context.pop();
      showSuccessfulCreateUser(context);
      print(response);

      notifyListeners();
    } catch (e) {
      print(e);
    }
  }

  //Api for update users
  Future<void> updateAdminUsers(
    String userId,
    String firstName,
    String lastName,
    String email,
    String phoneNumber,
    String countryCode,
    BuildContext context,
  ) async {
    log('updatinguser details: $userId, $firstName, $lastName , $email $phoneNumber $countryCode $countryCodeString');
    final data = <String, dynamic>{
      'userId': userId,
      'firstName': firstName,
      'lastName': lastName,
      'email': email,
      'phone': {
        'countryCode': countryCode,
        'number': phoneNumber,
      },
      'countryCodeString': countryCodeString,
      // "servers": [
      //   {"serverID": serverID, "status": status}
      // ],
    };
    try {
      final response =
          // await api.editOrderStatus(userId, userName, countryCode);
          // await api.editOrderStatus(
          //     "6602c4be86ce88c1d7fc1581", "Processing", "UPdated status");
          await api.editSupportDetails(data);
      print(response);
      context.pop();
      showSuccessfulEditUser('User Details Updated', context);
      Provider.of<ManageMerchantProvider>(context, listen: false)
          // .getMerchantDetails(1, 10);
          .getSupportListData(1, 1000,context);
      // notifyListeners();
    } catch (e) {
      print(e);
    }
  }

  Future<void> sentEmailToUsers(
    String subject,
    String body,
    String checkedMailList,
    BuildContext context,
  ) async {
    try {
      final data = <String, dynamic>{
        // "emails": ["<EMAIL>,<EMAIL>"],
        'emails': [checkedMailList],
        'subject': subject,
        'body': body,
      };
      // log("statusss $status $comments");
      final response = await api.sentEmail(data);
      // final response = await api.editOrderStatus(
      //     "6602c4be86ce88c1d7fc1581", status, comments);
      // await api.editOrderStatus(
      //     "6602c4be86ce88c1d7fc1581", "Processing", "UPdated status");
      print(response);
      context.pop();
      showSuccessfulEditUser('Emails sent successfully!', context);

      // notifyListeners();
    } catch (e) {
      print(e);
    }
  }

  Future<void> editOrderStaus(
    String userId,
    String orderID,
    String status,
    String comments,
    String countryCode,
    String phoneNumber,
    String email,
    String countryCodeString,
    String firstName,
    String lastName,
    BuildContext context,
    bool isDetailsPage,
    List<Servers> servers,
  ) async {
    try {
      final data = <String, dynamic>{
        'orderId': orderID,
        'status': status,
        'comment': comments,
        'servers': await returnpojolist(servers),
      };

      log('statusss $status $comments');
      final response = await api.editOrderStatus(data);
      // final response = await api.editOrderStatus(
      //     "6602c4be86ce88c1d7fc1581", status, comments);
      // await api.editOrderStatus(
      //     "6602c4be86ce88c1d7fc1581", "Processing", "UPdated status");
      print(response);
      Navigator.pop(context);
      Navigator.pop(context);
      // context.pop();
      showSuccessfulEditUser('User Details Updated', context);
      isDetailsPage
          ? Provider.of<ManageMerchantProvider>(context, listen: false)
              .getOrderDataDetails(1, 1000, orderID)
          : Provider.of<ManageMerchantProvider>(context, listen: false)
              // .getMerchantDetails(1, 10);
              .getOrderListData(1, 1000,context);
      // notifyListeners();
    } catch (e) {
      print(e);
    }
  }

  Future<void> editHostNames(
    String userId,
    String orderID,
    String status,
    String comments,
    String countryCode,
    String phoneNumber,
    String email,
    String countryCodeString,
    String firstName,
    String lastName,
    BuildContext context,
    bool isDetailsPage,
    List<Servers> servers,
  ) async {
    try {
      final data = <String, dynamic>{
        // "userId": userId,
        'orderId': orderID,
        // "status": status,
        // "comment": comments,
        // "phone": {
        //   "countryCode": countryCode,
        //   "number": phoneNumber,
        // },
        // "email": email,
        // "countryCodeString": countryCodeString,
        // "firstName": firstName,
        // "lastName": lastName,
        // "servers": [
        //   {"serverID": serverID, "hostName": status}
        // ],
        'servers': await returnpojolist(servers),
      };

      // Map<String, dynamic> data = userId.isNotEmpty
      //     ? {
      //         "userId": userId,
      //         "orderId": orderID,
      //         // "status": status,
      //         // "comment": comments,
      //         // "phone": {
      //         //   "countryCode": countryCode,
      //         //   "number": phoneNumber,
      //         // },
      //         // "email": email,
      //         // "countryCodeString": countryCodeString,
      //         // "firstName": firstName,
      //         // "lastName": lastName,
      //         // "servers": [
      //         //   {"serverID": serverID, "hostName": status}
      //         // ],
      //         "servers": await returnpojolist(servers),
      //       }
      //     : {
      //         "orderId": orderID,
      //         // "status": status,
      //         // "comment": comments,
      //       };

      log('statusss $status $comments');
      final response = await api.editOrderStatus(data);
      // final response = await api.editOrderStatus(
      //     "6602c4be86ce88c1d7fc1581", status, comments);
      // await api.editOrderStatus(
      //     "6602c4be86ce88c1d7fc1581", "Processing", "UPdated status");
      print(response);
      Navigator.pop(context);
      Navigator.pop(context);
      // context.pop();
      showSuccessfulEditUser('User Details Updated', context);
      isDetailsPage
          ? Provider.of<ManageMerchantProvider>(context, listen: false)
              .getOrderDataDetails(1, 1000, orderID)
          : Provider.of<ManageMerchantProvider>(context, listen: false)
              // .getMerchantDetails(1, 10);
              .getOrderListData(1, 1000,context);
      // notifyListeners();
    } catch (e) {
      print(e);
    }
  }

  //Api for delete users
  Future<void> deleteAdminUsers(String userId, BuildContext context) async {
    try {
      final response = await api.deleteUser(userId);
      print(response);
      context.pop();
      getAdminUsers();
      showSuccesfulDeleteDialog(context);

      //notifyListeners();
    } catch (e) {
      print(e);
    }
  }

  void showSuccessfulEditUser(String text, BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          content: SizedBox(
            height: 300,
            width: 410,
            child: Column(
              children: [
                const Image(
                  image: AssetImage(ImageConstants.addMerchantSuccessImage),
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  text,
                  style: AppTextStyles.textStyleBold26(context).copyWith(
                    color: AppColors.kLightPrimarySwatch,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void showSuccesfulDeleteDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          content: Container(
            height: 250,
            width: 500,
            padding: const EdgeInsets.only(left: 20, right: 20),
            child: Row(
              children: [
                const Image(
                  image: AssetImage(ImageConstants.deleteUserImage),
                ),
                const SizedBox(
                  width: 30,
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'User Removed',
                      style: AppTextStyles.textStyleBold26(context).copyWith(
                        color: AppColors.kLightPrimarySwatch,
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Text(
                      'Successfully!',
                      style: AppTextStyles.textStyleBold26(context).copyWith(
                        color: AppColors.kLightPrimarySwatch,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void showSuccessfulCreateUser(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          actionsAlignment: MainAxisAlignment.start,
          content: Container(
            height: 250,
            width: 600,
            padding: const EdgeInsets.only(left: 20, right: 20),
            child: Row(
              children: [
                const Image(
                  image: AssetImage(ImageConstants.addMerchantSuccessImage),
                ),
                const SizedBox(
                  width: 30,
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Team Member has Created',
                        style: AppTextStyles.textStyleBold26(context).copyWith(
                          color: AppColors.kLightPrimarySwatch,
                        ),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Expanded(
                        child: Text(
                          'Access to Dashboard Invite has been successfully sent to \n ${firstNameController.text} with email ${emailController.text}',
                          overflow: TextOverflow.clip,
                          style:
                              AppTextStyles.textStyleBold16(context).copyWith(
                            color: AppColors.kBlack,
                            overflow: TextOverflow.clip,
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Expanded(
                        child: Text(
                          'Please inform them to check their email and use the link in the mail to start onboarding.',
                          style:
                              AppTextStyles.textStyleBold16(context).copyWith(
                            color: AppColors.kLightPrimarySwatch,
                            overflow: TextOverflow.clip,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          actions: [
            CustomListTile(
              tileColor: Provider.of<DarkThemeProvider>(context, listen: false)
                      .darkTheme
                  ? AppColors.kBlack
                  : AppColors.kWhite,
              isDarkTheme:
                  Provider.of<DarkThemeProvider>(context, listen: false)
                      .darkTheme,
              title: AppStrings.myTeamAddAnotherUser,
              onTap: () {
                context.pop();
                clearControllers();
                createUserDialog(
                  context,
                  firstNameController,
                  firstNameFocusNode,
                  lastNameController,
                  lastNameFocusNode,
                  emailController,
                  emailFocusNode,
                  phoneController,
                  phoneFocusNode,
                  dropDownValue,
                );
              },
            ),
          ],
        );
      },
    );
  }

  void clearControllers() {
    firstNameController.clear();
    lastNameController.clear();
    emailController.clear();
    phoneController.clear();
    dropDownValue = 'ADMIN';
  }

  void createUserDialog(
    BuildContext context,
    TextEditingController firstNameController,
    FocusNode firstNameFocusNode,
    TextEditingController lastNameController,
    FocusNode lastNameFocusNode,
    TextEditingController emailController,
    FocusNode emailFocusNode,
    TextEditingController phoneController,
    FocusNode phoneFocusNode,
    String dropDownValue,
  ) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(
            'Create User',
            style: AppTextStyles.textStyleBold26(context),
          ),
          actionsAlignment: MainAxisAlignment.start,
          content: Container(
            height: 350,
            width: 830,
            padding: const EdgeInsets.only(left: 20, right: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomTextFormField(
                      label: 'First Name',
                      hintText: 'First Name',
                      controller: firstNameController,
                      focusNode: firstNameFocusNode,
                      validator: AppValidators.validateName,
                      width: 300,
                    ),
                    CustomTextFormField(
                      label: 'Last Name',
                      hintText: 'Last Name',
                      controller: lastNameController,
                      focusNode: lastNameFocusNode,
                      validator: AppValidators.validateName,
                      width: 300,
                    ),
                  ],
                ),
                const SizedBox(
                  height: 20,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomTextFormField(
                      label: 'Email',
                      hintText: 'Email',
                      controller: emailController,
                      focusNode: emailFocusNode,
                      validator: AppValidators.validateEmail,
                      width: 300,
                    ),
                    CustomTextFormField(
                      label: 'Confirm Email',
                      hintText: 'Confirm Email',
                      controller: TextEditingController(),
                      validator: AppValidators.validateEmail,
                      width: 300,
                    ),
                  ],
                ),
                const SizedBox(
                  height: 20,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'User Ph. no.',
                          style: AppTextStyles.textStyleBold14(context),
                        ),
                        SizedBox(
                          width: 300,
                          child: IntlPhoneField(
                            controller: phoneController,
                            focusNode: phoneFocusNode,
                            textInputAction: TextInputAction.done,
                            dropdownIconPosition: IconPosition.trailing,
                            dropdownIcon:
                                const Icon(Icons.expand_more_outlined),
                            showCountryFlag: false,
                            decoration: const InputDecoration(
                              labelText: 'Phone Number',
                              border: OutlineInputBorder(),
                            ),
                            initialCountryCode: 'IN',
                            onChanged: (phone) {
                              print(phone.completeNumber);
                              print(phone.countryCode);
                              print('Country :${phone.countryISOCode}');
                              countryCodeString = phone.countryISOCode;
                              countryCodeController.text = phone.countryCode;
                            },
                            pickerDialogStyle: PickerDialogStyle(
                              backgroundColor: AppColors.kWhite,
                            ),
                          ),
                        ),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Choose User Role',
                          style: AppTextStyles.textStyleBold14(context),
                        ),
                        SizedBox(
                          width: 300,
                          child: DropdownButtonFormField(
                            dropdownColor: AppColors.kWhite,
                            icon: Icon(
                              Icons.arrow_drop_down,
                              color: AppColors.kLightPrimarySwatch,
                            ),
                            value: dropDownValue,
                            items: [
                              DropdownMenuItem(
                                value: 'ADMIN',
                                child: Text(
                                  'ADMIN',
                                  style: AppTextStyles.textStyleBold14(context)
                                      .copyWith(
                                    color: AppColors.kLightPrimarySwatch,
                                  ),
                                ),
                              ),
                              DropdownMenuItem(
                                value: 'MANAGER',
                                child: Text(
                                  'MANAGER',
                                  style: AppTextStyles.textStyleBold14(context)
                                      .copyWith(
                                    color: AppColors.kLightPrimarySwatch,
                                  ),
                                ),
                              ),
                              DropdownMenuItem(
                                value: 'SUPER ADMIN',
                                child: Text(
                                  'SUPER ADMIN',
                                  style: AppTextStyles.textStyleBold14(context)
                                      .copyWith(
                                    color: AppColors.kLightPrimarySwatch,
                                  ),
                                ),
                              ),
                            ],
                            onChanged: (value) {},
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
          actions: [
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              onPressed: () {
                addAdminUsers(context);
              },
              child: Text(
                'Submit & Send Invite',
                style: AppTextStyles.textStyleBold14(context).copyWith(
                  color: AppColors.kWhite,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void deleteUserDialog(
    BuildContext context,
    String userId,
  ) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          // title: Text("Delete User",
          // style: AppTextStyles.textStyleBold26(context),
          // ),
          //actionsAlignment: MainAxisAlignment.center,
          content: Container(
            height: 250,
            width: 600,
            padding: const EdgeInsets.only(left: 20, right: 20),
            child: Row(
              children: [
                const Image(
                  image: AssetImage(ImageConstants.deleteUserImage),
                ),
                const SizedBox(
                  width: 30,
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Are you sure?',
                      style: AppTextStyles.textStyleBold26(context).copyWith(
                        color: AppColors.kLightPrimarySwatch,
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Text(
                      'you want to delete user',
                      style: AppTextStyles.textStyleBold26(context).copyWith(
                        color: AppColors.kLightPrimarySwatch,
                      ),
                    ),
                    Row(
                      children: [
                        ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.kWhite,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                              side: BorderSide(color: AppColors.kRed),
                            ),
                          ),
                          onPressed: () {
                            deleteAdminUsers(userId, context);
                          },
                          child: Text(
                            'Yes',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: AppColors.kRed,
                            ),
                          ),
                        ),
                        const SizedBox(
                          width: 20,
                        ),
                        ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.kLightPrimarySwatch,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                          onPressed: () {
                            context.pop();
                          },
                          child: Text(
                            'NO',
                            style:
                                AppTextStyles.textStyleBold14(context).copyWith(
                              color: AppColors.kWhite,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
          actions: const [],
        );
      },
    );
  }

  void sendEmailDialog(
    BuildContext context,
  ) {
    final bodyController = TextEditingController();
    // firstNameController.text = userName;
    final subjectController = TextEditingController();
    // lastNameController.text = lastName;
    final emailController = TextEditingController();
    // emailController.text = email;
    final confirmEmailController = TextEditingController();
    // confirmEmailController.text = email;
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Sent Email',
                style: AppTextStyles.textStyleBold26(context),
              ),
              IconButton(
                onPressed: () {
                  context.pop();
                },
                icon: const Icon(Icons.close),
              ),
            ],
          ),
          actionsAlignment: MainAxisAlignment.start,
          content: Container(
            height: 450,
            width: 600,
            padding: const EdgeInsets.only(left: 20, right: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SizedBox(
                      width: 500,
                      height: 100,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Subject',
                            style: AppTextStyles.textStyleBold14(context),
                          ),
                          TextField(
                            maxLines: null,
                            controller: subjectController,
                            keyboardType: TextInputType.multiline,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                // SizedBox(
                //   height: 20,
                // ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SizedBox(
                      width: 500,
                      height: 300,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Body',
                            style: AppTextStyles.textStyleBold14(context),
                          ),
                          // TextFormField(
                          //     maxLines: null,
                          //     // autovalidateMode: true,
                          //     // autovalidate: true,
                          //     validator: (value) {
                          //       if (value!.contains('\n')) {
                          //         // doFun(value);
                          //         log("Entered text: $value");
                          //       }
                          //     }),
                          Expanded(
                            child: Align(
                              alignment: Alignment.topCenter,
                              // child: KeyboardListener(
                              //   focusNode: _focusNode,
                              //   autofocus: true,
                              //   onKeyEvent: (event) {
                              //     if (event is KeyDownEvent &&
                              //         event.logicalKey ==
                              //             LogicalKeyboardKey.enter) {
                              //       // your action here

                              //       emailController.text =
                              //           emailController.text +
                              //               "<br>" +
                              //               bodyController.text;
                              //     }
                              //   },
                              child: TextFormField(
                                textAlignVertical: TextAlignVertical.top,
                                expands: true,
                                maxLines: null,
                                controller: bodyController,
                                keyboardType: TextInputType.multiline,
                                // onChanged: (value) {
                                //   log("Entered text: $value");
                                // },
                                textInputAction: TextInputAction.newline,
                                // onSubmitted: (value) {
                                //   log("Entered  onSubmitted: $value");
                                // },
                              ),
                            ),
                          ),
                          // ),
                          // RawKeyboardListener(
                          //     focusNode: FocusNode(),
                          //     onKey: (event) {
                          //       if (event
                          //           .isKeyPressed(LogicalKeyboardKey.enter)) {
                          //         int cursorPos =
                          //             bodyController.selection.base.offset;

                          //         // bodyController.text =
                          //         //     "textDebut" + '\n' + "textFin";
                          //         bodyController.text =
                          //             bodyController.text + '\n';
                          //         emailController.text =
                          //             emailController.text +
                          //                 "<br>" +
                          //                 bodyController.text;
                          //         bodyController.selection =
                          //             TextSelection.fromPosition(TextPosition(
                          //                 offset: cursorPos + 1));
                          //       }
                          //     },
                          //     child: TextFormField(
                          //         // focusNode: myFocusNode,
                          //         autocorrect: false,
                          //         enableSuggestions: false,
                          //         toolbarOptions: ToolbarOptions(
                          //             copy: false, cut: false, paste: false),
                          //         keyboardType: TextInputType.visiblePassword,
                          //         textInputAction: TextInputAction.newline,
                          //         autofocus: true,
                          //         maxLines: null,
                          //         controller: bodyController,
                          //         decoration: InputDecoration(
                          //             fillColor: Colors.grey[100]),
                          //         onChanged: (String text) {
                          //           print(text);
                          //         }))
                        ],
                      ),
                    ),
                  ],
                ),
                // SizedBox(
                //   height: 20,
                // ),
                // Row(
                //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //   children: [
                //     CustomTextFormField(
                //         label: "Email",
                //         hintText: 'Email',
                //         readOnly: true,
                //         controller: emailController,
                //         validator: AppValidators.validateEmail,
                //         width: 300),

                //   ],
                // ),
                // SizedBox(
                //   height: 20,
                // ),
              ],
            ),
          ),
          actions: [
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              onPressed: () {
                var checkedMailList = '';
                final list = Provider.of<ManageMerchantProvider>(
                  context,
                  listen: false,
                ).getSupportList;
                for (var i = 0; i < list.length; i++) {
                  if (list[i].isCheck) {
                    // if(checkedMailList.isNotEmpty)
                    // checkedMailList.add(list[i].email!);
                    checkedMailList = checkedMailList.isNotEmpty
                        ? '$checkedMailList , ${list[i].email!}'
                        : list[i].email!;
                  }
                }
                const htmlEscape = HtmlEscape();

                final escaped = htmlEscape.convert(bodyController.text);

                print('print html formated: $escaped');
//                     String htmlData = """<div>
//   <h1>Demo Page</h1>
//   <p>This is a fantastic product that you should buy!</p>
//   <h3>Features</h3>
//   <ul>
//     <li>It actually works</li>
//     <li>It exists</li>
//     <li>It doesn't cost much!</li>
//   </ul>
//   <!--You can pretty much put any html in here!-->
// </div>""";
//                     String htmlData2 = """<div>
// <p>'''$escaped'''</p>
// </div>""";
//                     String htmlData3 = """<div style="white-space: pre">
//        Enjoy the videos and music you love.
//         Create your own business
//         or entertainment channel

//         Thnaks regrds
//         Rama
//     </div>""";

                final htmlData =
                    '''<div style="white-space: pre">$escaped</div>''';
                // for (int i = 0; i < checkedMailList.length; i++) {
                // log("Selected emials: $checkedMailList");
                // log("bodyController emials: ${emailController.text}");
                // }
                sentEmailToUsers(
                  subjectController.text,
                  htmlData,
                  checkedMailList,
                  context,
                );
              },
              child: Text(
                'SEND',
                style: AppTextStyles.textStyleBold14(context).copyWith(
                  color: AppColors.kWhite,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void editUserDialog(
    BuildContext context,
    String userId,
    String userName,
    String lastName,
    String email,
    String countryCode,
    String countrycodestring,
    TextEditingController phoneController,
    FocusNode phoneFocusNode,
    String dropDownValue,
  ) {
    final firstNameController = TextEditingController();
    firstNameController.text = userName;
    final lastNameController = TextEditingController();
    lastNameController.text = lastName;
    final emailController = TextEditingController();
    emailController.text = email;
    final confirmEmailController = TextEditingController();
    confirmEmailController.text = email;
    countryCodeController.text = countryCode;
    countryCodeString = countrycodestring;
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Edit User Details',
                style: AppTextStyles.textStyleBold26(context),
              ),
              IconButton(
                onPressed: () {
                  context.pop();
                },
                icon: const Icon(Icons.close),
              ),
            ],
          ),
          actionsAlignment: MainAxisAlignment.start,
          content: Container(
            height: 450,
            width: 400,
            padding: const EdgeInsets.only(left: 20, right: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomTextFormField(
                      label: 'First Name',
                      hintText: 'First Name',
                      controller: firstNameController,
                      validator: AppValidators.validateName,
                      width: 300,
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    CustomTextFormField(
                      label: 'Last Name',
                      hintText: 'Last Name',
                      controller: lastNameController,
                      validator: AppValidators.validateName,
                      width: 300,
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    CustomTextFormField(
                      label: 'Email',
                      hintText: 'Email',
                      // readOnly: true,
                      controller: emailController,
                      validator: AppValidators.validateEmail,
                      width: 300,
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    // CustomTextFormField(
                    //     label: "Confirm Email",
                    //     hintText: 'Confirm Email',
                    //     readOnly: true,
                    //     controller: emailController,
                    //     validator: AppValidators.validateEmail,
                    //     width: 300),
                    SizedBox(
                      width: 300,
                      height: 100,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Phone Number',
                            style: AppTextStyles.textStyleBold14(context),
                          ),
                          IntlPhoneField(
                            controller: phoneController,
                            focusNode: phoneFocusNode,
                            textInputAction: TextInputAction.done,
                            dropdownIconPosition: IconPosition.trailing,
                            dropdownIcon:
                                const Icon(Icons.expand_more_outlined),
                            showCountryFlag: false,

                            // decoration: InputDecoration(
                            //   labelText: 'Phone Number',
                            //   border: OutlineInputBorder(
                            //     borderSide: BorderSide(),
                            //   ),
                            // ),
                            // initialCountryCode: 'IN',
                            // initialCountryCode: 'US',
                            // initialCountryCode: countryCodeController.text,
                            initialValue: countryCodeController.text,
                            onChanged: (phone) {
                              print(phone.completeNumber);
                              print(phone.countryCode);
                              print(
                                'countryCodeString :${phone.countryISOCode}',
                              );
                              countryCodeString = phone.countryISOCode;
                              countryCodeController.text = phone.countryCode;
                            },
                            pickerDialogStyle: PickerDialogStyle(
                              backgroundColor: AppColors.kWhite,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                // SizedBox(
                //   height: 20,
                // ),
                // Row(
                //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //   children: [
                //     // CustomTextFormField(
                //     //     label: "First Name",
                //     //     hintText: 'First Name',
                //     //     controller: firstNameController,
                //     //     validator: AppValidators.validateName,
                //     //     width: 300),
                //     CustomTextFormField(
                //         label: "Last Name",
                //         hintText: 'Last Name',
                //         controller: lastNameController,
                //         validator: AppValidators.validateName,
                //         width: 300),
                //   ],
                // ),
                // SizedBox(
                //   height: 20,
                // ),
                // Row(
                //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //   children: [

                //   ],
                // ),
                const SizedBox(
                  height: 20,
                ),

                // Row(
                //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //   children: [
                //     Column(
                //       crossAxisAlignment: CrossAxisAlignment.start,
                //       children: [
                //         Text(
                //           "User Ph. no.",
                //           style: AppTextStyles.textStyleBold14(context),
                //         ),
                //         Container(
                //           width: 300,
                //           child: IntlPhoneField(
                //             controller: phoneController,
                //             focusNode: phoneFocusNode,
                //             textInputAction: TextInputAction.done,
                //             dropdownIconPosition: IconPosition.trailing,
                //             dropdownIcon: Icon(Icons.expand_more_outlined),
                //             showCountryFlag: false,
                //             decoration: InputDecoration(
                //               labelText: 'Phone Number',
                //               border: OutlineInputBorder(
                //                 borderSide: BorderSide(),
                //               ),
                //             ),
                //             initialCountryCode: 'IN',
                //             onChanged: (phone) {
                //               print(phone.completeNumber);
                //               print(phone.countryCode);
                //               countryCodeController.text = phone.countryCode;
                //             },
                //             pickerDialogStyle: PickerDialogStyle(
                //               backgroundColor: AppColors.kWhite,
                //             ),
                //           ),
                //         ),
                //       ],
                //     ),
                //     Column(
                //       crossAxisAlignment: CrossAxisAlignment.start,
                //       children: [
                //         Text(
                //           "Choose User Role",
                //           style: AppTextStyles.textStyleBold14(context),
                //         ),
                //         Container(
                //           width: 300,
                //           child: DropdownButtonFormField(
                //             dropdownColor: AppColors.kWhite,
                //             icon: Icon(Icons.arrow_drop_down,
                //                 color: AppColors.kLightPrimarySwatch),
                //             value: dropDownValue,
                //             items: [
                //               DropdownMenuItem(
                //                 child: Text(
                //                   "INIT",
                //                   style:
                //                       AppTextStyles.textStyleBold14(context)
                //                           .copyWith(
                //                     color: AppColors.kLightPrimarySwatch,
                //                   ),
                //                 ),
                //                 value: "INIT",
                //               ),
                //               DropdownMenuItem(
                //                 child: Text(
                //                   "PROCESSING",
                //                   style:
                //                       AppTextStyles.textStyleBold14(context)
                //                           .copyWith(
                //                     color: AppColors.kLightPrimarySwatch,
                //                   ),
                //                 ),
                //                 value: "PROCESSING",
                //               ),
                //               DropdownMenuItem(
                //                 child: Text(
                //                   "DONE",
                //                   style:
                //                       AppTextStyles.textStyleBold14(context)
                //                           .copyWith(
                //                     color: AppColors.kLightPrimarySwatch,
                //                   ),
                //                 ),
                //                 value: "DONE",
                //               ),
                //             ],
                //             onChanged: (value) {},
                //           ),
                //         ),
                //       ],
                //     ),
                // ],
                // ),
              ],
            ),
          ),
          actions: [
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              onPressed: () {
                updateAdminUsers(
                  userId,
                  firstNameController.text,
                  lastNameController.text,
                  emailController.text,
                  phoneController.text,
                  countryCodeController.text,
                  context,
                );
              },
              child: Text(
                'Update Details',
                style: AppTextStyles.textStyleBold14(context).copyWith(
                  color: AppColors.kWhite,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void editOrderDialog(
    BuildContext context,
    String userId,
    String orderId,
    String firstName,
    String lastName,
    String email,
    String countryCode,
    String countrycodestring,
    TextEditingController phoneController,
    FocusNode phoneFocusNode,
    List<Servers> servers,
    List<HostNameForDropdown> hostDropDownList,
    String dropDownValue, [
    bool? isDetailsPage,
  ]) {
    final commentsController = TextEditingController();
    final firstNameController = TextEditingController();
    firstNameController.text = firstName;
    final lastNameController = TextEditingController();
    lastNameController.text = lastName;
    final emailController = TextEditingController();
    emailController.text = email;
    final confirmEmailController = TextEditingController();
    confirmEmailController.text = email;
    countryCodeController.text = countryCode;
    countryCodeString = countrycodestring;
    // hostDropDownValue = hostDropDownList[0].hostName!;
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Edit Order Details',
                style: AppTextStyles.textStyleBold26(context),
              ),
              IconButton(
                onPressed: () {
                  Navigator.pop(context);
                  // isDetailsPage ?? false
                  //     ? Navigator.pop(context)
                  //     : context.pop();
                },
                icon: const Icon(Icons.close),
              ),
            ],
          ),
          actionsAlignment: MainAxisAlignment.start,
          scrollable: true,
          content: Container(
            height: 450,
            width: 540,
            padding: const EdgeInsets.only(left: 20, right: 20),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Row(
                  //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //   children: [
                  //     CustomTextFormField(
                  //         label: "First Name",
                  //         hintText: 'First Name',
                  //         controller: firstNameController,
                  //         validator: AppValidators.validateName,
                  //         width: 300),
                  //     CustomTextFormField(
                  //         label: "Last Name",
                  //         hintText: 'Last Name',
                  //         controller: lastNameController,
                  //         validator: AppValidators.validateName,
                  //         width: 300),
                  //   ],
                  // ),
                  // SizedBox(
                  //   height: 20,
                  // ),
                  // Row(
                  //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //   children: [
                  //     CustomTextFormField(
                  //         label: "Email",
                  //         hintText: 'Email',
                  //         readOnly: true,
                  //         controller: emailController,
                  //         validator: AppValidators.validateEmail,
                  //         width: 300),
                  //     CustomTextFormField(
                  //         label: "Confirm Email",
                  //         hintText: 'Confirm Email',
                  //         readOnly: true,
                  //         controller: emailController,
                  //         validator: AppValidators.validateEmail,
                  //         width: 300),
                  //   ],
                  // ),
                  const SizedBox(
                    height: 20,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Column(
                      //   crossAxisAlignment: CrossAxisAlignment.start,
                      //   children: [
                      //     Text(
                      //       "User Ph. no.",
                      //       style: AppTextStyles.textStyleBold14(context),
                      //     ),
                      //     Container(
                      //       width: 300,
                      //       child: IntlPhoneField(
                      //         controller: phoneController,
                      //         focusNode: phoneFocusNode,
                      //         textInputAction: TextInputAction.done,
                      //         dropdownIconPosition: IconPosition.trailing,
                      //         dropdownIcon: Icon(Icons.expand_more_outlined),
                      //         showCountryFlag: false,
                      //         decoration: InputDecoration(
                      //           labelText: 'Phone Number',
                      //           border: OutlineInputBorder(
                      //             borderSide: BorderSide(),
                      //           ),
                      //         ),
                      //         initialCountryCode: 'IN',
                      //         onChanged: (phone) {
                      //           print(phone.completeNumber);
                      //           print(phone.countryCode);
                      //           countryCodeController.text = phone.countryCode;
                      //         },
                      //         pickerDialogStyle: PickerDialogStyle(
                      //           backgroundColor: AppColors.kWhite,
                      //         ),
                      //       ),
                      //     ),
                      //   ],
                      // ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Order Status',
                            style: AppTextStyles.textStyleBold14(context),
                          ),
                          SizedBox(
                            width: 500,
                            height: 50,
                            child: DropdownButtonFormField(
                              dropdownColor: AppColors.kWhite,
                              icon: Icon(
                                Icons.arrow_drop_down,
                                color: AppColors.kLightPrimarySwatch,
                              ),
                              value: dropDownValue,
                              items: [
                                DropdownMenuItem(
                                  value: 'Provisioning',
                                  child: Text(
                                    'Provisioning',
                                    style:
                                        AppTextStyles.textStyleBold14(context)
                                            .copyWith(
                                      color: AppColors.kLightPrimarySwatch,
                                    ),
                                  ),
                                ),
                                DropdownMenuItem(
                                  value: 'Configuring',
                                  child: Text(
                                    'Configuring',
                                    style:
                                        AppTextStyles.textStyleBold14(context)
                                            .copyWith(
                                      color: AppColors.kLightPrimarySwatch,
                                    ),
                                  ),
                                ),
                                DropdownMenuItem(
                                  value: 'QA_Testing',
                                  child: Text(
                                    'QA Testing',
                                    style:
                                        AppTextStyles.textStyleBold14(context)
                                            .copyWith(
                                      color: AppColors.kLightPrimarySwatch,
                                    ),
                                  ),
                                ),
                                DropdownMenuItem(
                                  value: 'PackAndShip',
                                  child: Text(
                                    'Pack & Ship',
                                    style:
                                        AppTextStyles.textStyleBold14(context)
                                            .copyWith(
                                      color: AppColors.kLightPrimarySwatch,
                                    ),
                                  ),
                                ),
                                DropdownMenuItem(
                                  value: 'Shipped',
                                  child: Text(
                                    'Shipped',
                                    style:
                                        AppTextStyles.textStyleBold14(context)
                                            .copyWith(
                                      color: AppColors.kLightPrimarySwatch,
                                    ),
                                  ),
                                ),
                                // DropdownMenuItem(
                                //   child: Text(
                                //     "Received",
                                //     style:
                                //         AppTextStyles.textStyleBold14(context)
                                //             .copyWith(
                                //       color: AppColors.kLightPrimarySwatch,
                                //     ),
                                //   ),
                                //   value: "Received",
                                // ),
                              ],
                              onChanged: (value) {
                                dropDownValue = value!;
                                log('status value: $value');
                              },
                            ),
                          ),
                        ],
                      ),
                      // userId.isNotEmpty
                      //     ? Row(
                      //         mainAxisAlignment:
                      //             MainAxisAlignment.spaceBetween,
                      //         children: [
                      //           Container(
                      //             width: 300,
                      //             child: CustomTextFormField(
                      //                 label: "Email Address",
                      //                 hintText: 'Email',
                      //                 // readOnly: true,
                      //                 controller: emailController,
                      //                 validator: AppValidators.validateEmail,
                      //                 width: 300),
                      //           ),
                      //           // CustomTextFormField(
                      //           //     label: "Confirm Email",
                      //           //     hintText: 'Confirm Email',
                      //           //     readOnly: true,
                      //           //     controller: emailController,
                      //           //     validator: AppValidators.validateEmail,
                      //           //     width: 300),
                      //         ],
                      //       )
                      //     : SizedBox(),
                    ],
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      SizedBox(
                        width: 500,
                        height: 100,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Comments',
                              style: AppTextStyles.textStyleBold14(context),
                            ),
                            TextField(
                              maxLines: null,
                              controller: commentsController,
                              keyboardType: TextInputType.multiline,
                            ),
                          ],
                        ),
                      ),
                      // userId.isNotEmpty
                      //     ? Container(
                      //         width: 300,
                      //         height: 100,
                      //         child: Column(
                      //           crossAxisAlignment: CrossAxisAlignment.start,
                      //           children: [
                      //             Text(
                      //               "Phone Number",
                      //               style: AppTextStyles.textStyleBold14(
                      //                   context),
                      //             ),
                      //             IntlPhoneField(
                      //               controller: phoneController,
                      //               focusNode: phoneFocusNode,
                      //               textInputAction: TextInputAction.done,
                      //               dropdownIconPosition:
                      //                   IconPosition.trailing,
                      //               dropdownIcon:
                      //                   Icon(Icons.expand_more_outlined),
                      //               showCountryFlag: false,

                      //               // decoration: InputDecoration(
                      //               //   labelText: 'Phone Number',
                      //               //   border: OutlineInputBorder(
                      //               //     borderSide: BorderSide(),
                      //               //   ),
                      //               // ),
                      //               // initialCountryCode: 'IN',
                      //               // initialCountryCode: 'US',
                      //               // initialCountryCode: countryCodeController.text,
                      //               initialValue: countryCodeController.text,
                      //               onChanged: (phone) {
                      //                 print(phone.completeNumber);
                      //                 print(phone.countryCode);
                      //                 print(
                      //                     "countryCodeString :${phone.countryISOCode}");
                      //                 countryCodeString =
                      //                     phone.countryISOCode;
                      //                 countryCodeController.text =
                      //                     phone.countryCode;
                      //               },
                      //               pickerDialogStyle: PickerDialogStyle(
                      //                 backgroundColor: AppColors.kWhite,
                      //               ),
                      //             ),
                      //           ],
                      //         ),
                      //       )
                      //     : SizedBox(),
                    ],
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  // userId.isNotEmpty
                  //     ? Row(
                  //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //         children: [
                  //           CustomTextFormField(
                  //               label: "First Name",
                  //               hintText: 'First Name',
                  //               controller: firstNameController,
                  //               focusNode: firstNameFocusNode,
                  //               validator: AppValidators.validateName,
                  //               width: 300),
                  //           CustomTextFormField(
                  //               label: "Last Name",
                  //               hintText: 'Last Name',
                  //               controller: lastNameController,
                  //               focusNode: lastNameFocusNode,
                  //               validator: AppValidators.validateName,
                  //               width: 300),
                  //         ],
                  //       )
                  //     : SizedBox(),

                  // SizedBox(
                  //   height: 20,
                  // ),

                  // servers.isNotEmpty
                  //     ? refferalStatus(servers, hostDropDownList)
                  //     : Container(),

                  // Expanded(
                  //   child: Align(
                  //     alignment: Alignment.bottomCenter,
                  //     child: Container(
                  //       margin: const EdgeInsets.all(5),
                  //       width: double.infinity,
                  //       child: Text(
                  //           '* Hostname is mandatory to change the status to QA Testing.'),
                  //     ),
                  //   ),
                  // ),
                ],
              ),
            ),
          ),
          actions: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // const Text(
                //     '* Hostname is mandatory to change the status to QA Testing.'),
                const SizedBox(
                  height: 8,
                ),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  onPressed: () async {
                    log('Updated details: userID:$userId countryCodeString $countryCodeString : Comments:${commentsController.text}: Phone number: ${phoneController.text} Country Code:${countryCodeController.text}: dropDownValue:$dropDownValue :  email:${emailController.text}');
                    showDialog(
                      context: context,
                      builder: (context) {
                        return AlertDialog(
                          title: const Text('Are you sure?'),
                          content: const Text(
                            'Would you like to update the details?',
                          ),
                          actions: [
                            TextButton(
                              child: const Text('Cancel'),
                              onPressed: () {
                                Navigator.pop(context);
                              },
                            ),
                            TextButton(
                              child: const Text('Yes'),
                              onPressed: () async {
                                // Navigator.pop(context);
                                // Navigator.pop(context);
                                // dropDownValue == "QA_Testing" ||
                                //         dropDownValue == "QA Testing"
                                //     ? await isHostnameSelected(servers)
                                //         ? await isHostnameDuplicated(
                                //                 servers)
                                //             ? editOrderStaus(
                                //                 userId,
                                //                 orderId,
                                //                 dropDownValue,
                                //                 commentsController.text,
                                //                 countryCodeController
                                //                     .text,
                                //                 phoneController.text,
                                //                 emailController.text,
                                //                 countryCodeString,
                                //                 firstNameController.text,
                                //                 lastNameController.text,
                                //                 context,
                                //                 isDetailsPage ?? false,
                                //                 servers)
                                //             : displayErrorMessage(context,
                                //                 'Hostname cannot be assigned to more than one server')
                                //         : displayErrorMessage(context,
                                //             'Hostname has not been selected for one or more hardware devices.  This is mandatory for the QA Testing phase.')
                                //     :
                                editOrderStaus(
                                  userId,
                                  orderId,
                                  dropDownValue,
                                  commentsController.text,
                                  countryCodeController.text,
                                  phoneController.text,
                                  emailController.text,
                                  countryCodeString,
                                  firstNameController.text,
                                  lastNameController.text,
                                  context,
                                  isDetailsPage ?? false,
                                  servers,
                                );
                              },
                            ),
                          ],
                        );
                      },
                    );
                  },
                  child: Text(
                    'Update Details',
                    style: AppTextStyles.textStyleBold14(context).copyWith(
                      color: AppColors.kWhite,
                    ),
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  void editHostNameDialog(
    BuildContext context,
    String userId,
    String orderId,
    String firstName,
    String lastName,
    String email,
    String countryCode,
    String countrycodestring,
    TextEditingController phoneController,
    FocusNode phoneFocusNode,
    List<Servers> servers,
    List<HostNameForDropdown> hostDropDownList,
    String dropDownValue, [
    bool? isDetailsPage,
  ]) {
    final commentsController = TextEditingController();
    final firstNameController = TextEditingController();
    firstNameController.text = firstName;
    final lastNameController = TextEditingController();
    lastNameController.text = lastName;
    final emailController = TextEditingController();
    emailController.text = email;
    final confirmEmailController = TextEditingController();
    confirmEmailController.text = email;
    countryCodeController.text = countryCode;
    countryCodeString = countrycodestring;
    hostDropDownValue = AppStrings.dropdownDefalutName;
    // hostDropDownValue = hostDropDownList[0].hostName!;
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Edit Host Name Details',
                style: AppTextStyles.textStyleBold26(context),
              ),
              IconButton(
                onPressed: () {
                  Navigator.pop(context);
                  // isDetailsPage ?? false
                  //     ? Navigator.pop(context)
                  //     : context.pop();
                },
                icon: const Icon(Icons.close),
              ),
            ],
          ),
          actionsAlignment: MainAxisAlignment.start,
          scrollable: true,
          content: Container(
            height: 450,
            width: 540,
            padding: const EdgeInsets.only(left: 20, right: 20),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const SizedBox(
                    height: 20,
                  ),
                  servers.isNotEmpty
                      ? refferalStatus(servers, hostDropDownList)
                      : Container(),
                ],
              ),
            ),
          ),
          actions: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // const Text(
                //     '* Hostname is mandatory to change the status to QA Testing.'),
                const SizedBox(
                  height: 8,
                ),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  onPressed: () async {
                    log('Updated details: userID:$userId countryCodeString $countryCodeString : Comments:${commentsController.text}: Phone number: ${phoneController.text} Country Code:${countryCodeController.text}: dropDownValue:$dropDownValue :  email:${emailController.text}');
                    showDialog(
                      context: context,
                      builder: (context) {
                        return AlertDialog(
                          title: const Text('Are you sure?'),
                          content: const Text(
                            'Would you like to update the details?',
                          ),
                          actions: [
                            TextButton(
                              child: const Text('Cancel'),
                              onPressed: () {
                                Navigator.pop(context);
                              },
                            ),
                            TextButton(
                              child: const Text('Yes'),
                              onPressed: () async {
                                // Navigator.pop(context);
                                // Navigator.pop(context);
                                // dropDownValue == "QA_Testing" ||
                                //         dropDownValue == "QA Testing"
                                //     ? await isHostnameSelected(servers)
                                //         ? await isHostnameDuplicated(
                                //                 servers)
                                //             ? editOrderStaus(
                                //                 userId,
                                //                 orderId,
                                //                 dropDownValue,
                                //                 commentsController.text,
                                //                 countryCodeController
                                //                     .text,
                                //                 phoneController.text,
                                //                 emailController.text,
                                //                 countryCodeString,
                                //                 firstNameController.text,
                                //                 lastNameController.text,
                                //                 context,
                                //                 isDetailsPage ?? false,
                                //                 servers)
                                //             : displayErrorMessage(context,
                                //                 'Hostname cannot be assigned to more than one server')
                                //         : displayErrorMessage(context,
                                //             'Hostname has not been selected for one or more hardware devices.  This is mandatory for the QA Testing phase.')
                                //     :
                                await isHostnameDuplicated(servers)
                                    // ? showtext(servers)
                                    ? editHostNames(
                                        userId,
                                        orderId,
                                        dropDownValue,
                                        commentsController.text,
                                        countryCodeController.text,
                                        phoneController.text,
                                        emailController.text,
                                        countryCodeString,
                                        firstNameController.text,
                                        lastNameController.text,
                                        context,
                                        isDetailsPage ?? false,
                                        servers,
                                      )
                                    : displayErrorMessage(
                                        context,
                                        'Hostname cannot be assigned to more than one server',
                                      );
                              },
                            ),
                          ],
                        );
                      },
                    );
                  },
                  child: Text(
                    'Update Details',
                    style: AppTextStyles.textStyleBold14(context).copyWith(
                      color: AppColors.kWhite,
                    ),
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  Widget refferalStatus(
    List<Servers> items,
    List<HostNameForDropdown> hostDropDownList,
  ) {
    return HostnameSelectionWidget(
      items: items,
      hostDropDownList: hostDropDownList,
    );
  }

  ///Old Way of selecting Hostname
  // Widget refferalStatus(
  //     List<Servers> items, List<HostNameForDropdown> hostDropDownList) {
  //   return Padding(
  //       padding: const EdgeInsets.all(0.0),
  //       child: Container(
  //           // height: screenHeight * 0.3,
  //           // height: 200,
  //           decoration: BoxDecoration(
  //               borderRadius: BorderRadius.circular(10),
  //               border: Border.all(color: AppColors.kBlack, width: 1)),
  //           child: Padding(
  //             padding: const EdgeInsets.all(8.0),
  //             // child: Scrollbar(
  //             //   controller: ScrollController(),
  //             child: SizedBox(
  //               // height: MediaQuery.of(context).size.height,
  //               width: 2000,
  //
  //               child: ListView.builder(
  //                 // physics: const AlwaysScrollableScrollPhysics(), // new
  //
  //                 shrinkWrap: true,
  //                 itemCount: items.isEmpty ? 1 : items.length + 1,
  //                 itemBuilder: (BuildContext context, int index) {
  //                   if (index == 0) {
  //                     // return the header
  //                     return Padding(
  //                       padding: const EdgeInsets.only(bottom: 20.0),
  //                       child: Row(
  //                           mainAxisAlignment: MainAxisAlignment.start,
  //                           children: [
  //                             // SizedBox(
  //                             //   width: 20,
  //                             // ),
  //                             Expanded(
  //                               child: SelectableText(
  //                                 textAlign: TextAlign.left,
  //                                 "Server ID",
  //                                 style: AppTextStyles.textStyleBold16(context)
  //                                     .copyWith(
  //                                         color: Provider.of<DarkThemeProvider>(
  //                                                     context)
  //                                                 .darkTheme
  //                                             ? AppColors.kDarkPrimarySwatch
  //                                             : AppColors.kLightPrimarySwatch),
  //                               ),
  //                             ),
  //                             // SizedBox(
  //                             //   width: 100,
  //                             // ),
  //                             Expanded(
  //                               child: SelectableText(
  //                                 textAlign: TextAlign.left,
  //                                 "Hardware Model",
  //                                 style: AppTextStyles.textStyleBold16(context)
  //                                     .copyWith(
  //                                         color: Provider.of<DarkThemeProvider>(
  //                                                     context)
  //                                                 .darkTheme
  //                                             ? AppColors.kDarkPrimarySwatch
  //                                             : AppColors.kLightPrimarySwatch),
  //                               ),
  //                             ),
  //                             // SizedBox(
  //                             //   width: 100,
  //                             // ),
  //                             Expanded(
  //                               child: SelectableText(
  //                                 textAlign: TextAlign.center,
  //                                 "Hostname",
  //                                 style: AppTextStyles.textStyleBold16(context)
  //                                     .copyWith(
  //                                         color: Provider.of<DarkThemeProvider>(
  //                                                     context)
  //                                                 .darkTheme
  //                                             ? AppColors.kDarkPrimarySwatch
  //                                             : AppColors.kLightPrimarySwatch),
  //                               ),
  //                             ),
  //                           ]),
  //                     );
  //                   }
  //                   index -= 1;
  //
  //                   // return row
  //                   var row = items[index];
  //                   return Padding(
  //                     padding: const EdgeInsets.only(bottom: 0.0),
  //                     child: Row(
  //                       mainAxisAlignment: MainAxisAlignment.center,
  //                       crossAxisAlignment: CrossAxisAlignment.center,
  //                       children: [
  //                         Expanded(
  //                           child: SelectableText(
  //                             textAlign: TextAlign.left,
  //                             row.serverID!,
  //                             style: AppTextStyles.textStyleRegular16(context),
  //                           ),
  //                         ),
  //                         // SizedBox(
  //                         //   width: 50,
  //                         // ),
  //                         Expanded(
  //                           child: SelectableText(
  //                             textAlign: TextAlign.left,
  //                             row.model!,
  //                             // HelperFunctions.convertToDate(row.createdAt!),
  //                             style: AppTextStyles.textStyleRegular16(context),
  //                           ),
  //                         ),
  //                         // SizedBox(
  //                         //   width: 100,
  //                         // ),
  //                         // Column(
  //                         //   crossAxisAlignment: CrossAxisAlignment.stretch,
  //                         //   children: <Widget>[
  //                         //     Padding(
  //                         //         padding: EdgeInsets.only(
  //                         //             top: 8.0, left: 16.0, right: 16.0),
  //                         //         child: TextField(
  //                         //           style: TextStyle(
  //                         //               fontSize: 18.0, color: Colors.black),
  //                         //           decoration: InputDecoration(
  //                         //             prefixIcon: Icon(Icons.search),
  //                         //             suffixIcon: IconButton(
  //                         //               icon: Icon(Icons.close),
  //                         //               onPressed: () {
  //                         //                 controller.clear();
  //                         //                 FocusScope.of(context)
  //                         //                     .requestFocus(FocusNode());
  //                         //               },
  //                         //             ),
  //                         //             hintText: "Search...",
  //                         //           ),
  //                         //           controller: controller,
  //                         //         )),
  //                         //     Expanded(
  //                         //       child: Padding(
  //                         //           padding: EdgeInsets.only(top: 8.0),
  //                         //           child: _buildListView(hostDropDownList)),
  //                         //     )
  //                         //   ],
  //                         // ),
  //                         Expanded(
  //                             child: DropdownSearch<String>(
  //                           popupProps: PopupProps.menu(
  //                             showSearchBox: true,
  //                             showSelectedItems: true,
  //                             // disabledItemFn: (String s) => s.startsWith('I'),
  //                           ),
  //                           // items: [
  //                           //   "Brazil",
  //                           //   "Italia (Disabled)",
  //                           //   "Tunisia",
  //                           //   'Canada'
  //                           // ],
  //
  //                           items: retunHostNameListString(
  //                               context, hostDropDownList),
  //                           dropdownDecoratorProps:
  //                               const DropDownDecoratorProps(
  //                             dropdownSearchDecoration: InputDecoration(
  //                               // labelText: "Menu mode",
  //                               // hintText: "country in menu mode",
  //                               enabledBorder: OutlineInputBorder(
  //                                 // borderRadius: BorderRadius.circular(40),
  //                                 borderSide: BorderSide(
  //                                     color: Colors.transparent, width: 1),
  //                               ),
  //                               focusedBorder: OutlineInputBorder(
  //                                 // borderRadius: BorderRadius.circular(40),
  //                                 borderSide: BorderSide(
  //                                     color: Colors.transparent, width: 1),
  //                               ),
  //                               disabledBorder: OutlineInputBorder(
  //                                 // borderRadius: BorderRadius.circular(40),
  //                                 borderSide: BorderSide(
  //                                     color: Colors.transparent, width: 1),
  //                               ),
  //                             ),
  //                           ),
  //                           // onChanged: print,
  //                           onChanged: (value) {
  //                             hostDropDownValue = value!;
  //                             if (value == AppStrings.dropdownDefalutName) {
  //                               value = "";
  //                             }
  //                             row.selectedHostName = value;
  //                             log("status value: $value");
  //                           },
  //                           selectedItem: row.hostName!.isNotEmpty
  //                               ? row.hostName
  //                               : hostDropDownValue,
  //                           // selectedItem: hostDropDownValue,
  //                         )),
  //                         // Expanded(
  //                         //   child: Container(
  //                         //     // width: 410,
  //                         //     // height: 50,
  //                         //     child: DropdownButtonFormField(
  //                         //       decoration: const InputDecoration(
  //                         //         enabledBorder: OutlineInputBorder(
  //                         //           // borderRadius: BorderRadius.circular(40),
  //                         //           borderSide: BorderSide(
  //                         //               color: Colors.transparent, width: 1),
  //                         //         ),
  //                         //         focusedBorder: OutlineInputBorder(
  //                         //           // borderRadius: BorderRadius.circular(40),
  //                         //           borderSide: BorderSide(
  //                         //               color: Colors.transparent, width: 1),
  //                         //         ),
  //                         //         disabledBorder: OutlineInputBorder(
  //                         //           // borderRadius: BorderRadius.circular(40),
  //                         //           borderSide: BorderSide(
  //                         //               color: Colors.transparent, width: 1),
  //                         //         ),
  //                         //       ),
  //                         //       dropdownColor: AppColors.kWhite,
  //                         //       icon: Icon(Icons.arrow_drop_down,
  //                         //           color: AppColors.kLightPrimarySwatch),
  //                         //       value: hostDropDownValue,
  //                         //       items: retunHostNameList(
  //                         //           context, hostDropDownList),
  //                         //       onChanged: (value) {
  //                         //         hostDropDownValue = value!;
  //                         //         row.selectedHostName = value;
  //                         //         log("status value: $value");
  //                         //       },
  //                         //     ),
  //                         //   ),
  //                         // ),
  //                         // SizedBox(
  //                         //   width: 100,
  //                         // ),
  //                       ],
  //                     ),
  //                   );
  //                 },
  //               ),
  //             ),
  //           )));
  // }

  void displayErrorMessage(BuildContext context, String content) async {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(backgroundColor: Colors.red, content: Text(content)),
    );
    // content: Text(
    //     'Hostname has not been selected for one or more hardware devices.  This is mandatory for the QA Testing phase.')));
  }

  Future<bool> isHostnameSelected(List<Servers> servers) async {
    var ishostnameSelected = true;
    for (var i = 0; i < servers.length; i++) {
      // log('Server ID: ${servers[i].serverID}: SelectedHostname:${servers[i].selectedHostName}');
      if (servers[i].selectedHostName!.isEmpty ||
          servers[i].selectedHostName! == AppStrings.dropdownDefalutName) {
        ishostnameSelected = false;
        return ishostnameSelected;
      } else {
        // ishostnameSelected= true;
      }
    }
    return ishostnameSelected;
  }

  Future<bool> isHostnameDuplicated(List<Servers> servers) async {
    var ishostnameSelected = true;
    for (var i = 0; i < servers.length; i++) {
      // log('Server ID: ${servers[i].serverID}: SelectedHostname:${servers[i].selectedHostName}');
      if (servers[i].selectedHostName!.isNotEmpty) {
        for (var j = i + 1; j < servers.length; j++) {
          if (servers[i].selectedHostName == servers[j].selectedHostName) {
            ishostnameSelected = false;
            return ishostnameSelected;
          } else {
            // ishostnameSelected= true;
          }
        }
      }
    }
    return ishostnameSelected;
  }

  Future<List<Map<String, dynamic>>> returnpojolist(
    List<Servers> servers,
  ) async {
    final citiesAndCounters = <Map<String, dynamic>>[];

    for (final success in servers) {
      final cityAndCounters = <String, dynamic>{
        'serverID': success.serverID,
        'hostName': success.selectedHostName,
      };

      citiesAndCounters.add(cityAndCounters);
    }
    // for(int i=0;i<servers.length;i++){
    //   {"serverID": '${servers[i].serverID!}', "hostName": status}
    // }
    return citiesAndCounters;
  }

  List<DropdownMenuItem> retunHostNameList(
    BuildContext context,
    List<HostNameForDropdown> hostDropDownList,
  ) {
    final itemList = <DropdownMenuItem>[];
    for (var i = 0; i <= hostDropDownList.length; i++) {
      itemList.add(
        DropdownMenuItem(
          value: i == 0
              ? AppStrings.dropdownDefalutName
              : hostDropDownList[i - 1].hostName!,
          child: Text(
            // "Host Name $i",
            i == 0
                ? AppStrings.dropdownDefalutName
                : hostDropDownList[i - 1].hostName!,
            style: AppTextStyles.textStyleBold14(context).copyWith(
              color: AppColors.kLightPrimarySwatch,
            ),
          ),
        ),
      );
    }

    return itemList;
  }

  List<String> retunHostNameListString(
    BuildContext context,
    List<HostNameForDropdown> hostDropDownList,
  ) {
    final itemList = <String>[];
    for (var i = 0; i <= hostDropDownList.length; i++) {
      itemList.add(
        i == 0
            ? AppStrings.dropdownDefalutName
            : hostDropDownList[i - 1].hostName!,

        // value: i == 0 ? "Choose One" : hostDropDownList[i - 1].hostName!,
      );
    }

    return itemList;
  }

  void showText(List<Servers> servers) {
    for (var i = 0; i < servers.length; i++) {
      log('status ServerID: ${servers[i].serverID} :: Hostname: ${servers[i].hostName} : selectedHostName: ${servers[i].selectedHostName}');
    }
  }
}
