import 'dart:convert';
import 'package:admin_portal/api/api.dart';
import 'package:admin_portal/utils/utils_export.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'dart:typed_data';
import 'package:reactive_forms/reactive_forms.dart';

class MerchantOnboardingProvider extends ChangeNotifier {
  Uint8List? transparentLogo;
  bool isTransparentLogoUploaded = false;
  String? transparentLogoFileName;
  Uint8List? darkLogo;
  bool isDarkLogoUploaded = false;
  String? darkLogoFileName;
  TextEditingController controller = TextEditingController();
  TextEditingController companySiteController = TextEditingController();
  TextEditingController supportEmailController = TextEditingController();
  late String merchantToken;
  //FormKey
  final formKey = GlobalKey<FormState>();
  // final reactiveForm = FormGroup({
  //   'site': FormControl<String>(validators: [
  //     Validators.required,
  //     Validators.minLength(3),
  //     Validators.maxLength(50),
  //   ], asyncValidators: [
  //     UniqueSiteAsyncValidator(),
  //   ], asyncValidatorsDebounceTime: 1000),
  // });

  FormGroup getFormGorup(String token) {
    return FormGroup({
      'site': FormControl<String>(
        validators: [
          Validators.required,
          Validators.minLength(3),
          Validators.maxLength(50),
        ],
        asyncValidators: [
          UniqueSiteAsyncValidator(token: token),
        ],
        asyncValidatorsDebounceTime: 1000,
      ),
    });
  }

  // Common private method for file picking
  void _pickFile({
    required void Function(Uint8List?, bool, String?) updateProperties,
  }) async {
    try {
      // html.FileUploadInputElement uploadInput = html.FileUploadInputElement()
      //   ..multiple = false
      //   ..draggable = true
      //   ..click();

      // uploadInput.onChange.listen((e) {
      //   final files = uploadInput.files;
      //   if (files!.length == 1) {
      //     final file = files[0];
      //     final reader = html.FileReader();

      //     reader.onLoadEnd.listen((e) {
      //       updateProperties(reader.result as Uint8List?, true, file.name);
      //       notifyListeners();
      //     });

      //     reader.readAsArrayBuffer(file);
      //   }
      // });
      final result = await FilePicker.platform.pickFiles();
    } catch (e) {
      print(e);
    }
  }

  void pickTransparentLogoFile() {
    _pickFile(
      updateProperties: (logo, uploaded, fileName) {
        transparentLogo = logo;
        isTransparentLogoUploaded = uploaded;
        transparentLogoFileName = fileName;
      },
    );
  }

  void pickDarkLogoFile() {
    _pickFile(
      updateProperties: (logo, uploaded, fileName) {
        darkLogo = logo;
        isDarkLogoUploaded = uploaded;
        darkLogoFileName = fileName;
      },
    );
  }

  void clearTransparentLogoImage() {
    transparentLogo = null;
    isTransparentLogoUploaded = false;
    notifyListeners();
  }

  void clearDarkLogoImage() {
    darkLogo = null;
    isDarkLogoUploaded = false;
    notifyListeners();
  }

  //API call for checking site is available or not from API in checkTenantSiteNameExists method in API.dart
  // void checkTenantSiteNameExists(String customSiteName) async {
  //   try {
  //     final response = await api.checkTenantSiteNameExists(customSiteName);
  //   } catch (e) {
  //     print(e);
  //   }
  // }

  //API call for updateTenantCompany from API in updateTenantCompany method in API.dart
  void updateTenantCompany(
    String companyName,
    String customSiteName,
    String supportEmail,
    String companyId,
    String token,
  ) async {
    try {
      // String lightLogoBase64 =
      //     base64Encode(Uint8List.fromList(transparentLogo!));
      // String? darkLogoBase64;
      // if (darkLogo != null) {
      //   darkLogoBase64 = base64Encode(Uint8List.fromList(darkLogo!));
      // }
      final response = await api.updateTenantCompany(
        companyName,
        customSiteName,
        supportEmail,
        companyId,
        token,
      );
    } catch (e) {
      print(e);
    }
  }

  void uploadImages(String companyId, String token) async {
    try {
      final lightLogoBase64 =
          base64Encode(Uint8List.fromList(transparentLogo!));

      String? darkLogoBase64;
      if (darkLogo != null) {
        darkLogoBase64 = base64Encode(Uint8List.fromList(darkLogo!));
      }

      final response = await api.uploadImages(
        token,
        companyId,
        lightLogoBase64,
        transparentLogo!,
        optionalImage: darkLogoBase64,
        optionalImageFileName: darkLogoFileName,
      );
    } catch (e) {
      print(e);
    }
  }
}
