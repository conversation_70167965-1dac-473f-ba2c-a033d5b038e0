import 'dart:async';
import 'package:admin_portal/api/api.dart';
import 'package:admin_portal/models/models_export.dart';
import 'package:admin_portal/routes/route_names.dart';
import 'package:admin_portal/services/services_export.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class AuthProvider extends ChangeNotifier {
  final formKey = GlobalKey<FormState>();
  
  // Controllers for phone number and password login
   final emailController = TextEditingController();
  final passwordController = TextEditingController();
  
  final loginPhoneController = TextEditingController();
  final loginCountryCodeController = TextEditingController();
  final loginPasswordController = TextEditingController();
  final countryCodeStringController = TextEditingController();

  bool isLoginObscure = true;
  bool disableLoginUntillResponse = false;
  bool isLoginError = false;
  String loginErrorMessage = '';
  
  // OTP related properties
  bool _isResendButtonDisabled = false;
  int _seconds = 30;
  Timer? _timer;
  String? otpErrorMessage = '';
  bool isApiError = false;
  String apiErrorMessage = '';
  

  bool get isResendButtonDisabled => _isResendButtonDisabled;
  int get seconds => _seconds;

  // Current logged-in user
  AdminUser? _currentUser;
  AdminUser? get currentUser => _currentUser;

  void startTimer() {
    _isResendButtonDisabled = true;
    _seconds = 30;
    notifyListeners();

    _timer?.cancel(); // Cancel any existing timer
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_seconds > 0) {
        _seconds--;
        notifyListeners();
      } else {
        _isResendButtonDisabled = false;
        _timer?.cancel();
        notifyListeners();
      }
    });
  }

  void clearValidationError() {
    isLoginError = false;
    loginErrorMessage = '';
    notifyListeners();
  }

  void clearAllErrors() {
    isLoginError = false;
    loginErrorMessage = '';
    isApiError = false;
    apiErrorMessage = '';
    notifyListeners();
  }

  void clearApiError() {
    Future.delayed(const Duration(seconds: 2), () {
      isApiError = false;
      otpErrorMessage = null;
      notifyListeners();
    });
  }

  //Function for showing validation errors
  void showValidationErrors() {
    // Just trigger a rebuild to show validation errors
    // Don't set autoValidate to true to avoid continuous validation
    notifyListeners();
  }

  //Function for login
  void loginUser(BuildContext context) async {
    // Prevent multiple simultaneous login attempts
    if (disableLoginUntillResponse) return;
    
    isLoginError = false;
    loginErrorMessage = '';
    disableLoginUntillResponse = true;
    notifyListeners();
    
    try {
      final value = await api.login(
        loginCountryCodeController.text,
        loginPhoneController.text,
        loginPasswordController.text,
      );

  

      if (value['success'] == true && (value['statusCode'] == 200 || value['statusCode'] == 201)) {
        disableLoginUntillResponse = false;
        isLoginError = false;
        loginErrorMessage = '';
        notifyListeners();
        
        context.goNamed(
          AppRouteNames.otpScreen,
          extra: {'userId': value['userId']},
        );
      } else {
        // Handle API errors - show the actual error message from API
        loginErrorMessage = value['message'] ?? 'Login failed. Please try again.';
        isLoginError = true;
        disableLoginUntillResponse = false;
        notifyListeners();
        
      }
    } catch (e) {
      // Only catch actual network/connection errors, not API errors
      disableLoginUntillResponse = false;
      loginErrorMessage = 'Network error. Please check your connection and try again.';
      isLoginError = true;
      notifyListeners();
      
    }
  }

  //Function for calling OTP verification
  void verifyOtp(
    BuildContext context,
    String userId,
    String otp,
  ) async {
    isApiError = false;
    apiErrorMessage = '';
    notifyListeners();
    
    try {
      final value = await api.verifyMobileOtp(userId, otp);

      if (value['success'] == true && (value['statusCode'] == 200 || value['statusCode'] == 201)) {
        // Create AdminUser from response
        final userData = value['user'];
        
        if (userData != null) {
          _currentUser = AdminUser.fromJson({
            'user': userData,
          });
          
          // Store only user ID in secure storage (no access token)
          await SecureStorageService.storeUserData(
            userId: _currentUser!.userId,
            userName: _currentUser!.fullName,
          );
          
          isApiError = false;
          apiErrorMessage = '';
          notifyListeners();
          
          // Navigate to manage merchant screen
          context.go(context.namedLocation(AppRouteNames.manageMerchant));
        } else {
          apiErrorMessage = 'Invalid response from server. Please try again.';
          isApiError = true;
          notifyListeners();
        }
      } else {
        // Handle API errors
        apiErrorMessage = value['message'] ?? 'OTP verification failed. Please try again.';
        isApiError = true;
        notifyListeners();
        print('OTP verification failed: $apiErrorMessage');
      }
    } catch (e) {
      // Handle network or other errors
      apiErrorMessage = 'Network error. Please check your connection and try again.';
      isApiError = true;
      notifyListeners();
      print('OTP verification exception: $e');
    }
  }

  void changeLoginObscure() {
    isLoginObscure = !isLoginObscure;
    notifyListeners();
  }

  void clearControllers() {
    loginPhoneController.clear();
    loginCountryCodeController.clear();
    loginPasswordController.clear();
    countryCodeStringController.clear();
  }

  // Get stored user information from secure storage
  Future<String?> getStoredUserId() async {
    return SecureStorageService.getUserId();
  }

  Future<String?> getStoredFullName() async {
    return SecureStorageService.getUserName();
  }

  Future<String?> getStoredToken() async {
    return SecureStorageService.getToken();
  }

  Future<bool> isUserLoggedIn() async {
    return SecureStorageService.isUserLoggedIn();
  }

  // Logout method
  Future<void> logout(BuildContext context) async {
    try {
      // Call logout API
      await api.logout();
    } catch (e) {
      // Continue with logout even if API call fails
      print('Logout API call failed: $e');
    }
    
    // Clear all stored data
    _currentUser = null;
    await SecureStorageService.clearAllData();
    clearControllers();
    clearAllErrors();
    notifyListeners();
    
    // Navigate to login screen
    if (context.mounted) {
      context.goNamed(AppRouteNames.login);
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    loginPhoneController.dispose();
    loginCountryCodeController.dispose();
    loginPasswordController.dispose();
    countryCodeStringController.dispose();
    super.dispose();
  }
}
