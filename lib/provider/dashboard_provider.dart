import 'package:admin_portal/routes/route_names.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class DashBoardProvider extends ChangeNotifier {
  int selectedIndex = 0;

  void changeSelectedIndex(int index, BuildContext context) {
    selectedIndex = index;
    returnScreen(index, context);
    notifyListeners();
  }

  void returnScreen(int index, BuildContext context) {
    switch (index) {
      case 0:
        GoRouter.of(context).pushNamed(AppRouteNames.manageMerchant);
      case 1:
        GoRouter.of(context).pushNamed(AppRouteNames.addmerchant);
      case 2:
        GoRouter.of(context).pushNamed(AppRouteNames.addmerchant);
      default:
        GoRouter.of(context).pushNamed(AppRouteNames.manageMerchant);
    }
  }
}
