import 'package:admin_portal/api/api.dart';
import 'package:admin_portal/models/models_export.dart';
import 'package:flutter/material.dart';

class MerchantDetailsProvider extends ChangeNotifier {
  MerchantDetails? _merchantDetails;
  MerchantDetails? get merchantDetails => _merchantDetails;
  set merchantDetails(MerchantDetails? merchantDetails) {
    _merchantDetails = merchantDetails;
    notifyListeners();
  }

  Future<void> getMerchantDetails(String merchantId) async {
    try {
      final response = await api.getTenantCompanyDetails(merchantId);
      _merchantDetails = MerchantDetails.fromJson(response);

      notifyListeners();
    } catch (e) {
      rethrow;
    }
  }

  void clickOnHideMerchant(String companyId) async {
    try {
      final response = await api.toggleHiddenTenant(companyId);
    } catch (e) {
      print(e);
    }
  }

  void toggleMerchant(String companyId) async {
    try {
      final response = await api.toggleTenant(companyId);
      _merchantDetails = MerchantDetails.fromJson(response);
      notifyListeners();
    } catch (e) {
      print(e);
    }
  }
}
