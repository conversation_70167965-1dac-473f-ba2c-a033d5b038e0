import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

// Conditional import for web
import 'package:http/browser_client.dart'
    if (dart.library.io) 'package:http/http.dart' as browser_client;

class HttpClientService {
  static const Duration _defaultTimeout = Duration(seconds: 30);

  /// Makes an HTTP POST request
  static Future<http.Response> post(
    Uri url, {
    Map<String, String>? headers,
    Object? body,
    Encoding? encoding,
    Duration? timeout,
  }) async {
    return http
        .post(
          url,
          headers: headers,
          body: body,
          encoding: encoding,
        )
        .timeout(timeout ?? _defaultTimeout);
  }

  /// Makes an HTTP GET request
  static Future<http.Response> get(
    Uri url, {
    Map<String, String>? headers,
    Duration? timeout,
  }) async {
    return http.get(url, headers: headers).timeout(timeout ?? _defaultTimeout);
  }

  /// Makes an HTTP PUT request
  static Future<http.Response> put(
    Uri url, {
    Map<String, String>? headers,
    Object? body,
    Encoding? encoding,
    Duration? timeout,
  }) async {
    return http
        .put(
          url,
          headers: headers,
          body: body,
          encoding: encoding,
        )
        .timeout(timeout ?? _defaultTimeout);
  }

  /// Makes an HTTP PATCH request
  static Future<http.Response> patch(
    Uri url, {
    Map<String, String>? headers,
    Object? body,
    Encoding? encoding,
    Duration? timeout,
  }) async {
    return http
        .patch(
          url,
          headers: headers,
          body: body,
          encoding: encoding,
        )
        .timeout(timeout ?? _defaultTimeout);
  }
}
