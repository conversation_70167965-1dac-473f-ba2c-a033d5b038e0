import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PlatformStorageService {
  static const _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
    webOptions: WebOptions(
      dbName: 'nexqloud_admin_portal',
      publicKey: 'nexqloud_admin_portal_key',
    ),
  );

  // Keys for storage
  static const String _tokenKey = 'auth_token';
  static const String _userIdKey = 'user_id';
  static const String _userNameKey = 'user_name';
  static const String _isLoginKey = 'is_login';

  // Store authentication token
  static Future<void> setToken(String token) async {
    if (kIsWeb) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_tokenKey, token);
    } else {
      await _storage.write(key: _tokenKey, value: token);
    }
  }

  // Get authentication token
  static Future<String?> getToken() async {
    try {
      if (kIsWeb) {
        final prefs = await SharedPreferences.getInstance();
        return prefs.getString(_tokenKey);
      } else {
        return await _storage.read(key: _tokenKey);
      }
    } catch (e) {
      return null;
    }
  }

  // Store user ID
  static Future<void> setUserId(String userId) async {
    if (kIsWeb) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_userIdKey, userId);
    } else {
      await _storage.write(key: _userIdKey, value: userId);
    }
  }

  // Get user ID
  static Future<String?> getUserId() async {
    try {
      if (kIsWeb) {
        final prefs = await SharedPreferences.getInstance();
        return prefs.getString(_userIdKey);
      } else {
        return await _storage.read(key: _userIdKey);
      }
    } catch (e) {
      // If it's an OperationError, the data might be corrupted
      if (e.toString().contains('OperationError') && !kIsWeb) {
        try {
          await _storage.delete(key: _userIdKey);
        } catch (deleteError) {
          // Ignore delete errors
        }
      }
      return null;
    }
  }

  // Store user name
  static Future<void> setUserName(String userName) async {
    if (kIsWeb) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_userNameKey, userName);
    } else {
      await _storage.write(key: _userNameKey, value: userName);
    }
  }

  // Get user name
  static Future<String?> getUserName() async {
    try {
      if (kIsWeb) {
        final prefs = await SharedPreferences.getInstance();
        return prefs.getString(_userNameKey);
      } else {
        return await _storage.read(key: _userNameKey);
      }
    } catch (e) {
      return null;
    }
  }

  // Store login status
  static Future<void> setLoginStatus(bool isLoggedIn) async {
    if (kIsWeb) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_isLoginKey, isLoggedIn);
    } else {
      await _storage.write(key: _isLoginKey, value: isLoggedIn.toString());
    }
  }

  // Get login status
  static Future<bool> getLoginStatus() async {
    try {
      if (kIsWeb) {
        final prefs = await SharedPreferences.getInstance();
        return prefs.getBool(_isLoginKey) ?? false;
      } else {
        final status = await _storage.read(key: _isLoginKey);
        return status == 'true';
      }
    } catch (e) {
      return false;
    }
  }

  // Store all user data at once
  static Future<void> storeUserData({
    String? token,
    required String userId,
    required String userName,
  }) async {
    final futures = <Future<void>>[
      setUserId(userId),
      setUserName(userName),
      setLoginStatus(true),
    ];
    
    // Only store token if provided
    if (token != null && token.isNotEmpty) {
      futures.add(setToken(token));
    }
    
    await Future.wait(futures);
  }

  // Clear all authentication data
  static Future<void> clearAllData() async {
    try {
      if (kIsWeb) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove(_tokenKey);
        await prefs.remove(_userIdKey);
        await prefs.remove(_userNameKey);
        await prefs.remove(_isLoginKey);
      } else {
        await _storage.deleteAll();
      }
    } catch (e) {
      // Ignore errors during cleanup
    }
  }

  // Clear specific keys to fix corrupted data
  static Future<void> clearCorruptedData() async {
    try {
      if (kIsWeb) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove(_userIdKey);
        await prefs.remove(_userNameKey);
        await prefs.remove(_tokenKey);
        await prefs.remove(_isLoginKey);
      } else {
        await _storage.delete(key: _userIdKey);
        await _storage.delete(key: _userNameKey);
        await _storage.delete(key: _tokenKey);
        await _storage.delete(key: _isLoginKey);
      }
    } catch (e) {
      // Ignore errors during cleanup
    }
  }

  // Force reset storage (use as last resort)
  static Future<void> forceResetStorage() async {
    await clearAllData();
  }

  // Check if user is logged in
  static Future<bool> isUserLoggedIn() async {
    try {
      final userId = await getUserId();
      final isLogin = await getLoginStatus();
      return userId != null && userId.isNotEmpty && isLogin;
    } catch (e) {
      // If there's an OperationError, clear corrupted data and return false
      if (e.toString().contains('OperationError')) {
        await clearCorruptedData();
      }
      return false;
    }
  }

  // Get all stored user data
  static Future<Map<String, String?>> getAllUserData() async {
    return {
      'token': await getToken(),
      'userId': await getUserId(),
      'userName': await getUserName(),
      'isLogin': (await getLoginStatus()).toString(),
    };
  }
}
