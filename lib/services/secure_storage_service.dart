import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class SecureStorageService {
  static const _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // Keys for secure storage
  static const String _tokenKey = 'auth_token';
  static const String _userIdKey = 'user_id';
  static const String _userNameKey = 'user_name';
  static const String _isLoginKey = 'is_login';

  // Store authentication token
  static Future<void> setToken(String token) async {
    await _storage.write(key: _tokenKey, value: token);
  }

  // Get authentication token
  static Future<String?> getToken() async {
    try {
      return await _storage.read(key: _tokenKey);
    } catch (e) {
      // print('Error reading token: $e');
      return null;
    }
  }

  // Store user ID
  static Future<void> setUserId(String userId) async {
    await _storage.write(key: _userId<PERSON><PERSON>, value: userId);
  }

  // Get user ID
  static Future<String?> getUserId() async {
    try {
      return await _storage.read(key: _userIdKey);
    } catch (e) {
      // print('Error reading user ID: $e');
      // If it's an OperationError, the data might be corrupted
      if (e.toString().contains('OperationError')) {
        // print('OperationError detected for user ID, clearing specific key...');
        try {
          await _storage.delete(key: _userIdKey);
        } catch (deleteError) {
          // print('Error deleting corrupted user ID: $deleteError');
        }
      }
      return null;
    }
  }

  // Store user name
  static Future<void> setUserName(String userName) async {
    await _storage.write(key: _userNameKey, value: userName);
  }

  // Get user name
  static Future<String?> getUserName() async {
    try {
      return await _storage.read(key: _userNameKey);
    } catch (e) {
      // print('Error reading user name: $e');
      return null;
    }
  }

  // Store login status
  static Future<void> setLoginStatus(bool isLoggedIn) async {
    await _storage.write(key: _isLoginKey, value: isLoggedIn.toString());
  }

  // Get login status
  static Future<bool> getLoginStatus() async {
    try {
      final status = await _storage.read(key: _isLoginKey);
      return status == 'true';
    } catch (e) {
      // print('Error reading login status: $e');
      return false;
    }
  }

  // Store all user data at once
  static Future<void> storeUserData({
    String? token,
    required String userId,
    required String userName,
  }) async {
    final futures = <Future<void>>[
      setUserId(userId),
      setUserName(userName),
      setLoginStatus(true),
    ];
    
    // Only store token if provided
    if (token != null && token.isNotEmpty) {
      futures.add(setToken(token));
    }
    
    await Future.wait(futures);
  }

  // Clear all authentication data
  static Future<void> clearAllData() async {
    try {
      await _storage.deleteAll();
      // print('All secure storage data cleared successfully');
    } catch (e) {
      // print('Error clearing secure storage data: $e');
    }
  }

  // Clear specific keys to fix corrupted data
  static Future<void> clearCorruptedData() async {
    try {
      await _storage.delete(key: _userIdKey);
      await _storage.delete(key: _userNameKey);
      await _storage.delete(key: _tokenKey);
      await _storage.delete(key: _isLoginKey);
      // print('Corrupted secure storage data cleared successfully');
    } catch (e) {
      // print('Error clearing corrupted data: $e');
    }
  }

  // Force reset secure storage (use as last resort)
  static Future<void> forceResetStorage() async {
    try {
      // Try to delete all keys individually
      await _storage.delete(key: _userIdKey);
      await _storage.delete(key: _userNameKey);
      await _storage.delete(key: _tokenKey);
      await _storage.delete(key: _isLoginKey);
      
      // Try to delete all data
      await _storage.deleteAll();
      // print('Secure storage force reset completed');
    } catch (e) {
      // print('Error during force reset: $e');
      // Even if reset fails, we continue
    }
  }

  // Check if user is logged in
  static Future<bool> isUserLoggedIn() async {
    try {
      final userId = await getUserId();
      final isLogin = await getLoginStatus();
      return userId != null && userId.isNotEmpty && isLogin;
    } catch (e) {
      // print('Error checking login status: $e');
      // If there's an OperationError, clear corrupted data and return false
      if (e.toString().contains('OperationError')) {
        // print('Detected OperationError, clearing corrupted data...');
        await clearCorruptedData();
      }
      return false;
    }
  }

  // Get all stored user data
  static Future<Map<String, String?>> getAllUserData() async {
    return {
      'token': await getToken(),
      'userId': await getUserId(),
      'userName': await getUserName(),
      'isLogin': (await getLoginStatus()).toString(),
    };
  }

}
