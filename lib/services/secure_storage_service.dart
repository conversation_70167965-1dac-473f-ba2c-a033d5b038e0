import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SecureStorageService {
  static const _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
    webOptions: WebOptions(
      dbName: 'FlutterSecureStorage',
      publicKey: 'flutter_secure_storage_public_key',
    ),
  );

  // Fallback storage for web platform
  static SharedPreferences? _prefs;

  // Keys for secure storage
  static const String _tokenKey = 'auth_token';
  static const String _userIdKey = 'user_id';
  static const String _userNameKey = 'user_name';
  static const String _isLoginKey = 'is_login';

  // Initialize fallback storage for web
  static Future<void> _initFallbackStorage() async {
    if (kIsWeb && _prefs == null) {
      _prefs = await SharedPreferences.getInstance();
    }
  }

  // Check if we should use fallback storage
  static bool get _useFallbackStorage => kIsWeb;

  // Generic read method with fallback
  static Future<String?> _read(String key) async {
    try {
      if (_useFallbackStorage) {
        await _initFallbackStorage();
        return _prefs?.getString(key);
      } else {
        return await _storage.read(key: key);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error reading $key: $e');
      }
      return null;
    }
  }

  // Generic write method with fallback
  static Future<void> _write(String key, String value) async {
    try {
      if (_useFallbackStorage) {
        await _initFallbackStorage();
        await _prefs?.setString(key, value);
      } else {
        await _storage.write(key: key, value: value);
      }
    } catch (e) {}
  }

  // Generic delete method with fallback
  static Future<void> _delete(String key) async {
    try {
      if (_useFallbackStorage) {
        await _initFallbackStorage();
        await _prefs?.remove(key);
      } else {
        await _storage.delete(key: key);
      }
    } catch (e) {
      print('Error deleting $key: $e');
    }
  }

  // Store authentication token
  static Future<void> setToken(String token) async {
    await _write(_tokenKey, token);
  }

  // Get authentication token
  static Future<String?> getToken() async {
    return await _read(_tokenKey);
  }

  // Store user ID
  static Future<void> setUserId(String userId) async {
    await _write(_userIdKey, userId);
  }

  // Get user ID
  static Future<String?> getUserId() async {
    return await _read(_userIdKey);
  }

  // Store user name
  static Future<void> setUserName(String userName) async {
    await _write(_userNameKey, userName);
  }

  // Get user name
  static Future<String?> getUserName() async {
    return await _read(_userNameKey);
  }

  // Store login status
  static Future<void> setLoginStatus(bool isLoggedIn) async {
    await _write(_isLoginKey, isLoggedIn.toString());
  }

  // Get login status
  static Future<bool> getLoginStatus() async {
    final status = await _read(_isLoginKey);
    return status == 'true';
  }

  // Store all user data at once
  static Future<void> storeUserData({
    String? token,
    required String userId,
    required String userName,
  }) async {
    final futures = <Future<void>>[
      setUserId(userId),
      setUserName(userName),
      setLoginStatus(true),
    ];

    // Only store token if provided
    if (token != null && token.isNotEmpty) {
      futures.add(setToken(token));
    }

    await Future.wait(futures);
  }

  // Clear all authentication data
  static Future<void> clearAllData() async {
    try {
      if (_useFallbackStorage) {
        await _initFallbackStorage();
        await _prefs?.clear();
      } else {
        await _storage.deleteAll();
      }
    } catch (e) {
      print('Error clearing storage data: $e');
    }
  }

  // Clear specific keys to fix corrupted data
  static Future<void> clearCorruptedData() async {
    try {
      await _delete(_userIdKey);
      await _delete(_userNameKey);
      await _delete(_tokenKey);
      await _delete(_isLoginKey);
    } catch (e) {
      print('Error clearing corrupted data: $e');
    }
  }

  // Force reset secure storage (use as last resort)
  static Future<void> forceResetStorage() async {
    try {
      // Try to delete all keys individually
      await _delete(_userIdKey);
      await _delete(_userNameKey);
      await _delete(_tokenKey);
      await _delete(_isLoginKey);

      // Try to delete all data
      await clearAllData();
    } catch (e) {
      print('Error during force reset: $e');
      // Even if reset fails, we continue
    }
  }

  // Check if user is logged in
  static Future<bool> isUserLoggedIn() async {
    try {
      final userId = await getUserId();
      final isLogin = await getLoginStatus();
      return userId != null && userId.isNotEmpty && isLogin;
    } catch (e) {
      print('Error checking login status: $e');
      return false;
    }
  }

  // Get all stored user data
  static Future<Map<String, String?>> getAllUserData() async {
    return {
      'token': await getToken(),
      'userId': await getUserId(),
      'userName': await getUserName(),
      'isLogin': (await getLoginStatus()).toString(),
    };
  }
}
