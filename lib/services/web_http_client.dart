import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

class WebHttpClient {
  static const Duration _defaultTimeout = Duration(seconds: 30);

  /// Makes an HTTP POST request with credentials for web
  static Future<http.Response> post(
    Uri url, {
    Map<String, String>? headers,
    Object? body,
    Encoding? encoding,
    Duration? timeout,
  }) async {
    // For now, just use the standard HTTP client but log that we're trying to use credentials
    if (kIsWeb && _shouldUseCredentials(url.toString())) {
      if (kDebugMode) {
        print(
          'WebHttpClient: Making POST request to $url - credentials should be handled by browser JS',
        );
      }
    }

    return http
        .post(
          url,
          headers: headers,
          body: body,
          encoding: encoding,
        )
        .timeout(timeout ?? _defaultTimeout);
  }

  /// Makes an HTTP GET request with credentials for web
  static Future<http.Response> get(
    Uri url, {
    Map<String, String>? headers,
    Duration? timeout,
  }) async {
    // For now, just use the standard HTTP client but log that we're trying to use credentials
    if (kIsWeb && _shouldUseCredentials(url.toString())) {
      if (kDebugMode) {
        print(
          'WebHttpClient: Making GET request to $url - credentials should be handled by browser JS',
        );
      }
    }

    return http.get(url, headers: headers).timeout(timeout ?? _defaultTimeout);
  }

  /// Makes an HTTP PUT request with credentials for web
  static Future<http.Response> put(
    Uri url, {
    Map<String, String>? headers,
    Object? body,
    Encoding? encoding,
    Duration? timeout,
  }) async {
    // For now, just use the standard HTTP client but log that we're trying to use credentials
    if (kIsWeb && _shouldUseCredentials(url.toString())) {
      if (kDebugMode) {
        print(
          'WebHttpClient: Making PUT request to $url - credentials should be handled by browser JS',
        );
      }
    }

    return http
        .put(
          url,
          headers: headers,
          body: body,
          encoding: encoding,
        )
        .timeout(timeout ?? _defaultTimeout);
  }

  /// Makes an HTTP PATCH request with credentials for web
  static Future<http.Response> patch(
    Uri url, {
    Map<String, String>? headers,
    Object? body,
    Encoding? encoding,
    Duration? timeout,
  }) async {
    // For now, just use the standard HTTP client but log that we're trying to use credentials
    if (kIsWeb && _shouldUseCredentials(url.toString())) {
      if (kDebugMode) {
        print(
          'WebHttpClient: Making PATCH request to $url - credentials should be handled by browser JS',
        );
      }
    }

    return http
        .patch(
          url,
          headers: headers,
          body: body,
          encoding: encoding,
        )
        .timeout(timeout ?? _defaultTimeout);
  }

  /// Check if URL should use credentials
  static bool _shouldUseCredentials(String url) {
    return url.contains('localapi.dks.nexqloud.net') ||
        url.contains('devapi.dks.nexqloud.net') ||
        url.contains('api.nexqloud.net') ||
        url.contains('stageapi.dks.nexqloud.net');
  }
}
