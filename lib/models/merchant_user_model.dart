// To parse this JSON data, do
//
//     final merchantUsers = merchantUsersFromJson(jsonString);

import 'dart:convert';

MerchantUsers merchantUsersFromJson(String str) =>
    MerchantUsers.fromJson(json.decode(str));

String merchantUsersToJson(MerchantUsers data) => json.encode(data.toJson());

class MerchantUsers {
  MerchantUsers({
    required this.id,
    required this.userName,
    required this.userEmail,
    required this.role,
  });

  factory MerchantUsers.fromJson(Map<String, dynamic> json) => MerchantUsers(
        id: json['_id'],
        userName: json['user_name'],
        userEmail: json['user_email'],
        role: json['role'],
      );
  String id;
  String userName;
  String userEmail;
  String role;

  Map<String, dynamic> toJson() => {
        '_id': id,
        'user_name': userName,
        'user_email': userEmail,
        'role': role,
      };
}
