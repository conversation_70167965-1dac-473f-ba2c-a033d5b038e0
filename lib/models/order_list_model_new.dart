class GetOrderListNew {
  GetOrderListNew({
    this.totlaOrderCount,
    this.totalHardwareItems,
    this.completedHardwaresCount,
    this.pendingHardwaresCount,
    this.activeHardwaresCount,
    this.disconnectedHardwaresCount,
    this.limit,
    this.page,
    this.total,
    this.totalPages,
    this.customers,
    this.hostNameForDropdown,
  });

  GetOrderListNew.fromJson(Map<String, dynamic> json) {
    totlaOrderCount = json['totlaOrderCount'] ?? 0;
    totalHardwareItems = json['totalHardwareItems'] ?? 0;
    completedHardwaresCount = json['completedHardwaresCount'] ?? 0;
    pendingHardwaresCount = json['pendingHardwaresCount'] ?? 0;
    activeHardwaresCount = json['activeHardwaresCount'] ?? 0;
    disconnectedHardwaresCount = json['disconnectedHardwaresCount'] ?? 0;
    limit = json['limit'];
    page = int.tryParse(json["page"].toString());
    total = json['total'];
    totalPages = json['totalPages'];
    if (json['customers'] != null) {
      customers = <CustomersNew>[];
      json['customers'].forEach((v) {
        customers!.add(CustomersNew.fromJson(v));
      });
    }
    if (json['hostNameForDropdown'] != null) {
      hostNameForDropdown = <HostNameForDropdown>[];
      json['hostNameForDropdown'].forEach((v) {
        hostNameForDropdown!.add(HostNameForDropdown.fromJson(v));
      });
    }
  }
  int? totlaOrderCount;
  int? totalHardwareItems;
  int? completedHardwaresCount;
  int? pendingHardwaresCount;
  int? activeHardwaresCount;
  int? disconnectedHardwaresCount;
  int? limit;
  int? page;
  int? total;
  int? totalPages;
  List<CustomersNew>? customers;
  List<HostNameForDropdown>? hostNameForDropdown;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['totlaOrderCount'] = totlaOrderCount;
    data['totalHardwareItems'] = totalHardwareItems;
    data['completedHardwaresCount'] = completedHardwaresCount;
    data['pendingHardwaresCount'] = pendingHardwaresCount;
    data['activeHardwaresCount'] = activeHardwaresCount;
    data['disconnectedHardwaresCount'] = disconnectedHardwaresCount;
    data['limit'] = limit;
    data['page'] = page;
    data['total'] = total;
    data['totalPages'] = totalPages;
    if (customers != null) {
      data['customers'] = customers!.map((v) => v.toJson()).toList();
    }
    if (hostNameForDropdown != null) {
      data['hostNameForDropdown'] =
          hostNameForDropdown!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class CustomersNew {
  CustomersNew({
    this.sId,
    this.orderId,
    this.priceAmount,
    this.nowpaymentsRequest,
    this.nowpaymentsInvoice,
    this.npId,
    this.createdAt,
    this.updatedAt,
    this.nowpaymentsStatus,
    this.servers,
    this.nlId,
    this.priceCurrency,
    // this.orderId,
    this.orderDescription,
    this.ipnCallbackUrl,
    this.successUrl,
    this.cancelUrl,
    this.trigger,
    this.status,
    this.comment,
    this.orderComment,
    this.acceptedOn,
    this.disputedOn,
    this.disputeUpdatedOn,
    this.disputeLastStatus,
    this.fulfilledOn,
    this.refundedOn,
    this.customerPaid,
    this.netAmount,
    this.applicationFee,
    this.shippingProvider,
    this.shippingTracking,
    this.shippingTrackingURL,
    this.customerInfo,
    this.allAddresses,
    this.shippingAddress,
    this.billingAddress,
    this.purchasedItems,
    this.purchasedItemsCount,
    this.totals,
    this.customData,
    this.paypalDetails,
    this.stripeCard,
    this.stripeDetails,
    this.paymentProcessor,
    this.hasDownloads,
    this.downloadFiles,
    this.metadata,
    this.isCustomerDeleted,
    this.isShippingRequired,
    this.firstName,
    this.lastName,
    this.userId,
    this.phone,
    this.countryCode,
    this.countryCodeString,
    this.email,
    this.triggerType,
    this.payload,
    this.hey,
    this.products,
    this.amount,
    this.customerName,
    this.customerEmail,
    this.productId,
    this.paymentId,
    this.orderID,
    this.hostNameForDropdown,
  });

  CustomersNew.fromJson(Map<String, dynamic> json) {
    sId = json['_id'];
    orderId = json['orderId'] ?? '';
    priceAmount = json['price_amount'];
    nowpaymentsRequest = json['nowpayments_request'] != null
        ? NowpaymentsRequest.fromJson(json['nowpayments_request'])
        : null;
    nowpaymentsInvoice = json['nowpayments_invoice'] != null
        ? NowpaymentsInvoice.fromJson(json['nowpayments_invoice'])
        : null;
    npId = json['np_id'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    nowpaymentsStatus = json['nowpayments_status'] != null
        ? NowpaymentsStatus.fromJson(json['nowpayments_status'])
        : null;
    if (json['servers'] != null) {
      servers = <Servers>[];
      json['servers'].forEach((v) {
        if (v != null) servers!.add(Servers.fromJson(v));
      });
    }
    nlId = json['nl_id'];
    priceCurrency = json['price_currency'];
    // orderId = json['order_id'];
    orderDescription = json['order_description'];
    ipnCallbackUrl = json['ipn_callback_url'];
    successUrl = json['success_url'];
    cancelUrl = json['cancel_url'];
    trigger = json['trigger'] ?? '';
    status = json['status'] ?? '';
    comment = json['comment'];
    orderComment = json['orderComment'];
    acceptedOn = json['acceptedOn'] ?? '';
    disputedOn = json['disputedOn'];
    disputeUpdatedOn = json['disputeUpdatedOn'];
    disputeLastStatus = json['disputeLastStatus'];
    fulfilledOn = json['fulfilledOn'];
    refundedOn = json['refundedOn'];
    customerPaid = json['customerPaid'] != null
        ? CustomerPaid.fromJson(json['customerPaid'])
        : null;
    netAmount = json['netAmount'] != null
        ? CustomerPaid.fromJson(json['netAmount'])
        : null;
    applicationFee = json['applicationFee'] != null
        ? ApplicationFee.fromJson(json['applicationFee'])
        : null;
    shippingProvider = json['shippingProvider'];
    shippingTracking = json['shippingTracking'];
    shippingTrackingURL = json['shippingTrackingURL'];
    customerInfo = json['customerInfo'] != null
        ? CustomerInfo.fromJson(json['customerInfo'])
        : CustomerInfo(fullName: '', email: '');
    if (json['allAddresses'] != null) {
      allAddresses = <AllAddresses>[];
      json['allAddresses'].forEach((v) {
        allAddresses!.add(AllAddresses.fromJson(v));
      });
    }
    shippingAddress = json['shippingAddress'] != null
        ? AllAddresses.fromJson(json['shippingAddress'])
        : AllAddresses(
            addressee: '',
            type: '',
            line1: '',
            line2: '',
            city: '',
            state: '',
            country: '',
            postalCode: '',
          );
    billingAddress = json['billingAddress'] != null
        ? AllAddresses.fromJson(json['billingAddress'])
        : AllAddresses(
            addressee: '',
            type: '',
            line1: '',
            line2: '',
            city: '',
            state: '',
            country: '',
            postalCode: '',
          );
    if (json['purchasedItems'] != null) {
      purchasedItems = <PurchasedItems>[];
      json['purchasedItems'].forEach((v) {
        if (v != null) purchasedItems!.add(PurchasedItems.fromJson(v));
      });
    }
    purchasedItemsCount = json['purchasedItemsCount'];
    totals = json['totals'] != null ? Totals.fromJson(json['totals']) : null;
    if (json['customData'] != null) {
      customData = <CustomData>[];
      json['customData'].forEach((v) {
        customData!.add(CustomData.fromJson(v));
      });
    }
    paypalDetails = json['paypalDetails'];
    stripeCard = json['stripeCard'] != null
        ? StripeCard.fromJson(json['stripeCard'])
        : null;
    stripeDetails = json['stripeDetails'] != null
        ? StripeDetails.fromJson(json['stripeDetails'])
        : null;
    paymentProcessor = json['paymentProcessor'] ?? '';
    hasDownloads = json['hasDownloads'];
    if (json['downloadFiles'] != null) {
      downloadFiles = [];
      // json['downloadFiles'].forEach((v) {
      //   downloadFiles!.add(new Null.fromJson(v));
      // });
    }
    metadata =
        json['metadata'] != null ? Metadata.fromJson(json['metadata']) : null;
    isCustomerDeleted = json['isCustomerDeleted'];
    isShippingRequired = json['isShippingRequired'];
    firstName = json['firstName'] ?? '';
    lastName = json['lastName'] ?? '';
    userId = json['userId'];
    // phone = json['phone'] != null ?  Phone.fromJson(json['phone']) : null;
    phone = json['phone'] != null
        ? Phone.fromJson(json['phone'])
        : Phone(countryCode: '', number: '');
    countryCode = json['countryCode'];
    countryCodeString = json['countryCodeString'] ?? '';
    email = json['email'] ?? '';
    triggerType = json['triggerType'];
    payload =
        json['payload'] != null ? Payload.fromJson(json['payload']) : null;
    hey = json['hey'];
    if (json['products'] != null) {
      products = <Products>[];
      json['products'].forEach((v) {
        products!.add(Products.fromJson(v));
      });
    }
    amount = json['amount'];
    customerName = json['customerName'] ?? '';
    customerEmail = json['customerEmail'] ?? '';
    productId = json['productId'];
    paymentId = json['paymentId'];
    orderID = json['orderID'];
    if (json['hostNameForDropdown'] != null) {
      hostNameForDropdown = <HostNameForDropdown>[];
      json['hostNameForDropdown'].forEach((v) {
        hostNameForDropdown!.add(HostNameForDropdown.fromJson(v));
      });
    }
  }
  String? sId;
  String? orderId;
  dynamic priceAmount;
  NowpaymentsRequest? nowpaymentsRequest;
  NowpaymentsInvoice? nowpaymentsInvoice;
  dynamic npId;
  String? createdAt;
  String? updatedAt;
  NowpaymentsStatus? nowpaymentsStatus;
  List<Servers>? servers = [];
  dynamic nlId;
  String? priceCurrency;
  // String? orderId;
  String? orderDescription;
  String? ipnCallbackUrl;
  String? successUrl;
  String? cancelUrl;
  String? trigger;
  String? status;
  String? comment;
  String? orderComment;
  String? acceptedOn;
  dynamic disputedOn;
  dynamic disputeUpdatedOn;
  dynamic disputeLastStatus;
  String? fulfilledOn;
  dynamic refundedOn;
  CustomerPaid? customerPaid;
  CustomerPaid? netAmount;
  ApplicationFee? applicationFee;
  dynamic shippingProvider;
  dynamic shippingTracking;
  dynamic shippingTrackingURL;
  CustomerInfo? customerInfo;
  List<AllAddresses>? allAddresses;
  AllAddresses? shippingAddress;
  AllAddresses? billingAddress;
  List<PurchasedItems>? purchasedItems = [];
  int? purchasedItemsCount;
  Totals? totals;
  List<CustomData>? customData;
  dynamic paypalDetails;
  StripeCard? stripeCard;
  StripeDetails? stripeDetails;
  String? paymentProcessor;
  bool? hasDownloads;
  List<void>? downloadFiles;
  Metadata? metadata;
  bool? isCustomerDeleted;
  bool? isShippingRequired;
  String? firstName;
  String? lastName;
  String? userId;
  Phone? phone;
  String? countryCode;
  String? countryCodeString;
  String? email;
  String? triggerType;
  Payload? payload;
  String? hey;
  List<Products>? products;
  String? amount;
  String? customerName;
  String? customerEmail;
  String? productId;
  String? paymentId;
  String? orderID;
  List<HostNameForDropdown>? hostNameForDropdown;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['_id'] = sId;
    data['orderId'] = orderId;
    data['price_amount'] = priceAmount;
    if (nowpaymentsRequest != null) {
      data['nowpayments_request'] = nowpaymentsRequest!.toJson();
    }
    if (nowpaymentsInvoice != null) {
      data['nowpayments_invoice'] = nowpaymentsInvoice!.toJson();
    }
    data['np_id'] = npId;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    if (nowpaymentsStatus != null) {
      data['nowpayments_status'] = nowpaymentsStatus!.toJson();
    }
    if (servers != null) {
      data['servers'] = servers!.map((v) => v.toJson()).toList();
    }
    data['nl_id'] = nlId;
    data['price_currency'] = priceCurrency;
    data['order_id'] = orderId;
    data['order_description'] = orderDescription;
    data['ipn_callback_url'] = ipnCallbackUrl;
    data['success_url'] = successUrl;
    data['cancel_url'] = cancelUrl;
    data['trigger'] = trigger;
    data['status'] = status;
    data['comment'] = comment;
    data['orderComment'] = orderComment;
    data['acceptedOn'] = acceptedOn;
    data['disputedOn'] = disputedOn;
    data['disputeUpdatedOn'] = disputeUpdatedOn;
    data['disputeLastStatus'] = disputeLastStatus;
    data['fulfilledOn'] = fulfilledOn;
    data['refundedOn'] = refundedOn;
    if (customerPaid != null) {
      data['customerPaid'] = customerPaid!.toJson();
    }
    if (netAmount != null) {
      data['netAmount'] = netAmount!.toJson();
    }
    if (applicationFee != null) {
      data['applicationFee'] = applicationFee!.toJson();
    }
    data['shippingProvider'] = shippingProvider;
    data['shippingTracking'] = shippingTracking;
    data['shippingTrackingURL'] = shippingTrackingURL;
    if (customerInfo != null) {
      data['customerInfo'] = customerInfo!.toJson();
    }
    if (allAddresses != null) {
      data['allAddresses'] = allAddresses!.map((v) => v.toJson()).toList();
    }
    if (shippingAddress != null) {
      data['shippingAddress'] = shippingAddress!.toJson();
    }
    if (billingAddress != null) {
      data['billingAddress'] = billingAddress!.toJson();
    }
    if (purchasedItems != null) {
      data['purchasedItems'] = purchasedItems!.map((v) => v.toJson()).toList();
    }
    data['purchasedItemsCount'] = purchasedItemsCount;
    if (totals != null) {
      data['totals'] = totals!.toJson();
    }
    if (customData != null) {
      data['customData'] = customData!.map((v) => v.toJson()).toList();
    }
    data['paypalDetails'] = paypalDetails;
    if (stripeCard != null) {
      data['stripeCard'] = stripeCard!.toJson();
    }
    if (stripeDetails != null) {
      data['stripeDetails'] = stripeDetails!.toJson();
    }
    data['paymentProcessor'] = paymentProcessor;
    data['hasDownloads'] = hasDownloads;
    if (downloadFiles != null) {
      // data['downloadFiles'] =
      //     this.downloadFiles!.map((v) => v.toJson()).toList();
    }
    if (metadata != null) {
      data['metadata'] = metadata!.toJson();
    }
    data['isCustomerDeleted'] = isCustomerDeleted;
    data['isShippingRequired'] = isShippingRequired;
    data['firstName'] = firstName;
    data['lastName'] = lastName;
    data['userId'] = userId;
    if (phone != null) {
      data['phone'] = phone!.toJson();
    }
    data['countryCode'] = countryCode;
    data['countryCodeString'] = countryCodeString;
    data['email'] = email;
    data['triggerType'] = triggerType;
    if (payload != null) {
      data['payload'] = payload!.toJson();
    }
    data['hey'] = hey;
    if (products != null) {
      data['products'] = products!.map((v) => v.toJson()).toList();
    }
    data['amount'] = amount;
    data['customerName'] = customerName;
    data['customerEmail'] = customerEmail;
    data['productId'] = productId;
    data['paymentId'] = paymentId;
    data['orderID'] = orderID;
    if (hostNameForDropdown != null) {
      data['hostNameForDropdown'] =
          hostNameForDropdown!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class NowpaymentsRequest {
  NowpaymentsRequest({
    this.priceAmount,
    this.priceCurrency,
    this.orderId,
    this.orderDescription,
    this.ipnCallbackUrl,
    this.successUrl,
    this.cancelUrl,
  });

  NowpaymentsRequest.fromJson(Map<String, dynamic> json) {
    priceAmount = json['price_amount'];
    priceCurrency = json['price_currency'];
    orderId = json['order_id'];
    orderDescription = json['order_description'];
    ipnCallbackUrl = json['ipn_callback_url'];
    successUrl = json['success_url'];
    cancelUrl = json['cancel_url'];
  }
  String? priceAmount;
  String? priceCurrency;
  String? orderId;
  String? orderDescription;
  String? ipnCallbackUrl;
  String? successUrl;
  String? cancelUrl;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['price_amount'] = priceAmount;
    data['price_currency'] = priceCurrency;
    data['order_id'] = orderId;
    data['order_description'] = orderDescription;
    data['ipn_callback_url'] = ipnCallbackUrl;
    data['success_url'] = successUrl;
    data['cancel_url'] = cancelUrl;
    return data;
  }
}

class NowpaymentsInvoice {
  NowpaymentsInvoice({
    this.id,
    this.tokenId,
    this.orderId,
    this.orderDescription,
    this.priceAmount,
    this.priceCurrency,
    this.payCurrency,
    this.ipnCallbackUrl,
    this.invoiceUrl,
    this.successUrl,
    this.cancelUrl,
    this.partiallyPaidUrl,
    this.payoutCurrency,
    this.createdAt,
    this.updatedAt,
    this.isFixedRate,
    this.isFeePaidByUser,
  });

  NowpaymentsInvoice.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    tokenId = json['token_id'];
    orderId = json['order_id'];
    orderDescription = json['order_description'];
    priceAmount = json['price_amount'];
    priceCurrency = json['price_currency'];
    payCurrency = json['pay_currency'];
    ipnCallbackUrl = json['ipn_callback_url'];
    invoiceUrl = json['invoice_url'];
    successUrl = json['success_url'];
    cancelUrl = json['cancel_url'];
    partiallyPaidUrl = json['partially_paid_url'];
    payoutCurrency = json['payout_currency'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    isFixedRate = json['is_fixed_rate'];
    isFeePaidByUser = json['is_fee_paid_by_user'];
  }
  String? id;
  String? tokenId;
  String? orderId;
  String? orderDescription;
  String? priceAmount;
  String? priceCurrency;
  dynamic payCurrency;
  String? ipnCallbackUrl;
  String? invoiceUrl;
  String? successUrl;
  String? cancelUrl;
  dynamic partiallyPaidUrl;
  dynamic payoutCurrency;
  String? createdAt;
  String? updatedAt;
  bool? isFixedRate;
  bool? isFeePaidByUser;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['token_id'] = tokenId;
    data['order_id'] = orderId;
    data['order_description'] = orderDescription;
    data['price_amount'] = priceAmount;
    data['price_currency'] = priceCurrency;
    data['pay_currency'] = payCurrency;
    data['ipn_callback_url'] = ipnCallbackUrl;
    data['invoice_url'] = invoiceUrl;
    data['success_url'] = successUrl;
    data['cancel_url'] = cancelUrl;
    data['partially_paid_url'] = partiallyPaidUrl;
    data['payout_currency'] = payoutCurrency;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['is_fixed_rate'] = isFixedRate;
    data['is_fee_paid_by_user'] = isFeePaidByUser;
    return data;
  }
}

class NowpaymentsStatus {
  NowpaymentsStatus({
    this.paymentId,
    this.invoiceId,
    this.paymentStatus,
    this.payAddress,
    this.payinExtraId,
    this.priceAmount,
    this.priceCurrency,
    this.payAmount,
    this.actuallyPaid,
    this.payCurrency,
    this.orderId,
    this.orderDescription,
    this.purchaseId,
    this.outcomeAmount,
    this.outcomeCurrency,
    this.payoutHash,
    this.payinHash,
    this.createdAt,
    this.updatedAt,
    this.burningPercent,
    this.type,
  });

  NowpaymentsStatus.fromJson(Map<String, dynamic> json) {
    paymentId = json['payment_id'];
    invoiceId = json['invoice_id'];
    paymentStatus = json['payment_status'];
    payAddress = json['pay_address'];
    payinExtraId = json['payin_extra_id'];
    priceAmount = json['price_amount'];
    priceCurrency = json['price_currency'];
    payAmount = json['pay_amount'];
    actuallyPaid = json['actually_paid'];
    payCurrency = json['pay_currency'];
    orderId = json['order_id'];
    orderDescription = json['order_description'];
    purchaseId = json['purchase_id'];
    outcomeAmount = json['outcome_amount'];
    outcomeCurrency = json['outcome_currency'];
    payoutHash = json['payout_hash'];
    payinHash = json['payin_hash'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    burningPercent = json['burning_percent'];
    type = json['type'];
  }
  int? paymentId;
  int? invoiceId;
  String? paymentStatus;
  String? payAddress;
  dynamic payinExtraId;
  double? priceAmount;
  String? priceCurrency;
  double? payAmount;
  dynamic actuallyPaid;
  String? payCurrency;
  String? orderId;
  String? orderDescription;
  int? purchaseId;
  double? outcomeAmount;
  String? outcomeCurrency;
  dynamic payoutHash;
  dynamic payinHash;
  String? createdAt;
  String? updatedAt;
  String? burningPercent;
  String? type;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['payment_id'] = paymentId;
    data['invoice_id'] = invoiceId;
    data['payment_status'] = paymentStatus;
    data['pay_address'] = payAddress;
    data['payin_extra_id'] = payinExtraId;
    data['price_amount'] = priceAmount;
    data['price_currency'] = priceCurrency;
    data['pay_amount'] = payAmount;
    data['actually_paid'] = actuallyPaid;
    data['pay_currency'] = payCurrency;
    data['order_id'] = orderId;
    data['order_description'] = orderDescription;
    data['purchase_id'] = purchaseId;
    data['outcome_amount'] = outcomeAmount;
    data['outcome_currency'] = outcomeCurrency;
    data['payout_hash'] = payoutHash;
    data['payin_hash'] = payinHash;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['burning_percent'] = burningPercent;
    data['type'] = type;
    return data;
  }
}

class Servers {
  Servers({
    this.name,
    this.serverID,
    this.userId,
    this.deviceStatus,
    this.licenseLinked,
    this.cpuScore,
    this.cloudScore,
    this.gpuScore,
    this.ramScore,
    this.diskScore,
    this.normalizedGpuScore,
    this.normalizedCpuScore,
    this.normalizedRamScore,
    this.normalizedDiskScore,
    this.normalizedISPScore,
    this.normalizedDeviceScore,
    this.totalEarning,
    this.totalEarningsAfterRecentOnline,
    this.earningsUpToLastWeek,
    this.macAddress,
    this.model,
    this.warrantyExpiration,
    this.location,
    this.purchaseDate,
    this.licenseId,
    this.manufacturer,
    this.software,
    this.phone,
    this.hostName,
    this.isOnline,
    this.supportStatus,
  });

  Servers.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    serverID = json['serverID'];
    userId = json['userId'] ?? '';
    hostName = json['hostName'] ?? '';
    deviceStatus = json['deviceStatus'];
    isOnline = json['isOnline'] ?? false;
    supportStatus = json['supportStatus'];
    licenseLinked = json['licenseLinked'];
    cpuScore = json['cpuScore'];
    cloudScore = json['cloudScore'] ?? 0.00;
    cloudScore = cloudScore!;
    gpuScore = json['gpuScore'];
    ramScore = json['ramScore'];
    diskScore = json['diskScore'];
    normalizedGpuScore = json['normalizedGpuScore'];
    normalizedCpuScore = json['normalizedCpuScore'];
    normalizedRamScore = json['normalizedRamScore'];
    normalizedDiskScore = json['normalizedDiskScore'];
    // normalizedISPScore = json['normalizedISPScore'].toDouble();
    normalizedISPScore = json['normalizedISPScore'] ?? 0.00;
    normalizedISPScore = normalizedISPScore!;
    normalizedDeviceScore = json['normalizedDeviceScore'];
    totalEarning = json['totalEarning'] ?? 0.00;
    totalEarning = totalEarning!;
    totalEarningsAfterRecentOnline = json['totalEarningsAfterRecentOnline'];
    earningsUpToLastWeek = json['earningsUpToLastWeek'];
    macAddress = json['macAddress'];
    model = json['model'];
    warrantyExpiration = json['warrantyExpiration'];
    location = json['location'];
    purchaseDate = json['purchaseDate'] ?? '';
    licenseId = json['licenseId'] ?? '';
    manufacturer = json['manufacturer'];
    if (json['software'] != null) {
      software = <Software>[];
      json['software'].forEach((v) {
        software!.add(Software.fromJson(v));
      });
    }
    phone = json['phone'] != null ? Phone.fromJson(json['phone']) : null;
  }
  String? name;
  String? serverID;
  String? hostName;
  String? userId;
  bool? deviceStatus;
  bool? isOnline;
  bool? licenseLinked;
  String? supportStatus;
  int? cpuScore;
  double? cloudScore;
  int? gpuScore;
  double? ramScore;
  int? diskScore;
  double? normalizedGpuScore;
  double? normalizedCpuScore;
  double? normalizedRamScore;
  double? normalizedDiskScore;
  double? normalizedISPScore;
  double? normalizedDeviceScore;
  double? totalEarning;
  double? totalEarningsAfterRecentOnline;
  int? earningsUpToLastWeek;
  String? macAddress;
  String? model;
  String? warrantyExpiration;
  String? location;
  String? purchaseDate;
  String? licenseId;
  String? manufacturer;
  List<Software>? software;
  Phone? phone;
  String? selectedHostName = '';

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['name'] = name;
    data['serverID'] = serverID;
    data['userId'] = userId;
    data['hostname'] = hostName;
    data['deviceStatus'] = deviceStatus;
    data['isOnline'] = isOnline;
    data['supportStatus'] = supportStatus;
    data['licenseLinked'] = licenseLinked;
    data['cpuScore'] = cpuScore;
    data['cloudScore'] = cloudScore;
    data['gpuScore'] = gpuScore;
    data['ramScore'] = ramScore;
    data['diskScore'] = diskScore;
    data['normalizedGpuScore'] = normalizedGpuScore;
    data['normalizedCpuScore'] = normalizedCpuScore;
    data['normalizedRamScore'] = normalizedRamScore;
    data['normalizedDiskScore'] = normalizedDiskScore;
    data['normalizedISPScore'] = normalizedISPScore;
    data['normalizedDeviceScore'] = normalizedDeviceScore;
    data['totalEarning'] = totalEarning;
    data['totalEarningsAfterRecentOnline'] = totalEarningsAfterRecentOnline;
    data['earningsUpToLastWeek'] = earningsUpToLastWeek;
    data['macAddress'] = macAddress;
    data['model'] = model;
    data['warrantyExpiration'] = warrantyExpiration;
    data['location'] = location;
    data['purchaseDate'] = purchaseDate;
    data['licenseId'] = licenseId;
    data['manufacturer'] = manufacturer;
    if (software != null) {
      data['software'] = software!.map((v) => v.toJson()).toList();
    }
    if (phone != null) {
      data['phone'] = phone!.toJson();
    }
    return data;
  }
}

class Software {
  Software({
    this.name,
    this.version,
    this.licenseType,
    this.expirationDate,
    this.licenseKey,
  });

  Software.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    version = json['version'];
    licenseType = json['licenseType'];
    expirationDate = json['expirationDate'];
    licenseKey = json['licenseKey'];
  }
  String? name;
  String? version;
  String? licenseType;
  String? expirationDate;
  String? licenseKey;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['name'] = name;
    data['version'] = version;
    data['licenseType'] = licenseType;
    data['expirationDate'] = expirationDate;
    data['licenseKey'] = licenseKey;
    return data;
  }
}

class Phone {
  Phone({this.countryCode, this.number});

  Phone.fromJson(Map<String, dynamic> json) {
    countryCode = json['countryCode'];
    number = json['number'];
  }
  String? countryCode;
  String? number;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['countryCode'] = countryCode;
    data['number'] = number;
    return data;
  }
}

class CustomerPaid {
  CustomerPaid({this.unit, this.value, this.string});

  CustomerPaid.fromJson(Map<String, dynamic> json) {
    unit = json['unit'];
    value = json['value'];
    string = json['string'];
  }
  String? unit;
  int? value;
  String? string;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['unit'] = unit;
    data['value'] = value;
    data['string'] = string;
    return data;
  }
}

class ApplicationFee {
  ApplicationFee({this.value, this.unit});

  ApplicationFee.fromJson(Map<String, dynamic> json) {
    value = json['value'];
    unit = json['unit'];
  }
  int? value;
  String? unit;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['value'] = value;
    data['unit'] = unit;
    return data;
  }
}

class CustomerInfo {
  CustomerInfo({this.fullName, this.email});

  CustomerInfo.fromJson(Map<String, dynamic> json) {
    fullName = json['fullName'];
    email = json['email'];
  }
  String? fullName;
  String? email;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['fullName'] = fullName;
    data['email'] = email;
    return data;
  }
}

class AllAddresses {
  AllAddresses({
    this.type,
    this.addressee,
    this.line1,
    this.line2,
    this.city,
    this.state,
    this.country,
    this.postalCode,
  });

  AllAddresses.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    addressee = json['addressee'];
    line1 = json['line1'];
    line2 = json['line2'];
    city = json['city'];
    state = json['state'];
    country = json['country'];
    postalCode = json['postalCode'];
  }
  String? type;
  String? addressee;
  String? line1;
  String? line2;
  String? city;
  String? state;
  String? country;
  String? postalCode;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['type'] = type;
    data['addressee'] = addressee;
    data['line1'] = line1;
    data['line2'] = line2;
    data['city'] = city;
    data['state'] = state;
    data['country'] = country;
    data['postalCode'] = postalCode;
    return data;
  }
}

class ShippingAddress {
  ShippingAddress({
    this.type,
    this.addressee,
    this.line1,
    this.line2,
    this.city,
    this.state,
    this.country,
    this.postalCode,
  });

  ShippingAddress.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    addressee = json['addressee'];
    line1 = json['line1'];
    line2 = json['line2'];
    city = json['city'];
    state = json['state'];
    country = json['country'];
    postalCode = json['postalCode'];
  }
  String? type;
  String? addressee;
  String? line1;
  dynamic line2;
  String? city;
  String? state;
  String? country;
  String? postalCode;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['type'] = type;
    data['addressee'] = addressee;
    data['line1'] = line1;
    data['line2'] = line2;
    data['city'] = city;
    data['state'] = state;
    data['country'] = country;
    data['postalCode'] = postalCode;
    return data;
  }
}

class PurchasedItems {
  PurchasedItems({
    this.count,
    this.rowTotal,
    this.productId,
    this.productName,
    this.productSlug,
    this.variantId,
    this.variantName,
    this.variantSlug,
    this.variantSKU,
    this.variantImage,
    this.variantPrice,
    this.weight,
    this.height,
    this.width,
    this.length,
  });

  PurchasedItems.fromJson(Map<String, dynamic> json) {
    count = json['count'];
    rowTotal = json['rowTotal'] != null
        ? CustomerPaid.fromJson(json['rowTotal'])
        : null;
    productId = json['productId'];
    productName = json['productName'];
    productSlug = json['productSlug'];
    variantId = json['variantId'];
    variantName = json['variantName'];
    variantSlug = json['variantSlug'];
    variantSKU = json['variantSKU'];
    variantImage = json['variantImage'] != null
        ? VariantImage.fromJson(json['variantImage'])
        : null;
    variantPrice = json['variantPrice'] != null
        ? CustomerPaid.fromJson(json['variantPrice'])
        : null;
    weight = json['weight'];
    height = json['height'] ?? 0.00;
    height = height!;
    width = json['width'];
    length = json['length'];
  }
  int? count;
  CustomerPaid? rowTotal;
  String? productId;
  String? productName;
  String? productSlug;
  String? variantId;
  String? variantName;
  String? variantSlug;
  String? variantSKU;
  VariantImage? variantImage;
  CustomerPaid? variantPrice;
  int? weight;
  double? height;
  double? width;
  double? length;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['count'] = count;
    if (rowTotal != null) {
      data['rowTotal'] = rowTotal!.toJson();
    }
    data['productId'] = productId;
    data['productName'] = productName;
    data['productSlug'] = productSlug;
    data['variantId'] = variantId;
    data['variantName'] = variantName;
    data['variantSlug'] = variantSlug;
    data['variantSKU'] = variantSKU;
    if (variantImage != null) {
      data['variantImage'] = variantImage!.toJson();
    }
    if (variantPrice != null) {
      data['variantPrice'] = variantPrice!.toJson();
    }
    data['weight'] = weight;
    data['height'] = height;
    data['width'] = width;
    data['length'] = length;
    return data;
  }
}

class VariantImage {
  VariantImage({this.fileId, this.url, this.alt, this.file});

  VariantImage.fromJson(Map<String, dynamic> json) {
    fileId = json['fileId'];
    url = json['url'];
    alt = json['alt'];
    file = json['file'];
  }
  String? fileId;
  String? url;
  dynamic alt;
  dynamic file;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['fileId'] = fileId;
    data['url'] = url;
    data['alt'] = alt;
    data['file'] = file;
    return data;
  }
}

class Totals {
  Totals({this.subtotal, this.extras, this.total});

  Totals.fromJson(Map<String, dynamic> json) {
    subtotal = json['subtotal'] != null
        ? CustomerPaid.fromJson(json['subtotal'])
        : null;
    if (json['extras'] != null) {
      extras = <Extras>[];
      json['extras'].forEach((v) {
        extras!.add(Extras.fromJson(v));
      });
    }
    total = json['total'] != null ? CustomerPaid.fromJson(json['total']) : null;
  }
  CustomerPaid? subtotal;
  List<Extras>? extras;
  CustomerPaid? total;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (subtotal != null) {
      data['subtotal'] = subtotal!.toJson();
    }
    if (extras != null) {
      data['extras'] = extras!.map((v) => v.toJson()).toList();
    }
    if (total != null) {
      data['total'] = total!.toJson();
    }
    return data;
  }
}

class Extras {
  Extras({this.type, this.name, this.description, this.price});

  Extras.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    name = json['name'];
    description = json['description'];
    price = json['price'] != null ? CustomerPaid.fromJson(json['price']) : null;
  }
  String? type;
  String? name;
  String? description;
  CustomerPaid? price;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['type'] = type;
    data['name'] = name;
    data['description'] = description;
    if (price != null) {
      data['price'] = price!.toJson();
    }
    return data;
  }
}

// class CustomData {
//   String? name;
//   String? textInput;

//   CustomData({this.name, this.textInput});

//   CustomData.fromJson(Map<String, dynamic> json) {
//     name = json['name'];
//     textInput = json['textInput'];
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['name'] = this.name;
//     data['textInput'] = this.textInput;
//     return data;
//   }
// }

class StripeCard {
  StripeCard({this.last4, this.brand, this.ownerName, this.expires});

  StripeCard.fromJson(Map<String, dynamic> json) {
    last4 = json['last4'];
    brand = json['brand'];
    ownerName = json['ownerName'];
    expires =
        json['expires'] != null ? Expires.fromJson(json['expires']) : null;
  }
  String? last4;
  String? brand;
  String? ownerName;
  Expires? expires;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['last4'] = last4;
    data['brand'] = brand;
    data['ownerName'] = ownerName;
    if (expires != null) {
      data['expires'] = expires!.toJson();
    }
    return data;
  }
}

class Expires {
  Expires({this.month, this.year});

  Expires.fromJson(Map<String, dynamic> json) {
    month = json['month'];
    year = json['year'];
  }
  int? month;
  int? year;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['month'] = month;
    data['year'] = year;
    return data;
  }
}

class StripeDetails {
  StripeDetails({
    this.customerId,
    this.paymentMethod,
    this.chargeId,
    this.disputeId,
    this.paymentIntentId,
    this.subscriptionId,
    this.refundId,
    this.refundReason,
  });

  StripeDetails.fromJson(Map<String, dynamic> json) {
    customerId = json['customerId'];
    paymentMethod = json['paymentMethod'];
    chargeId = json['chargeId'];
    disputeId = json['disputeId'];
    paymentIntentId = json['paymentIntentId'];
    subscriptionId = json['subscriptionId'];
    refundId = json['refundId'];
    refundReason = json['refundReason'];
  }
  String? customerId;
  String? paymentMethod;
  String? chargeId;
  dynamic disputeId;
  String? paymentIntentId;
  dynamic subscriptionId;
  dynamic refundId;
  dynamic refundReason;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['customerId'] = customerId;
    data['paymentMethod'] = paymentMethod;
    data['chargeId'] = chargeId;
    data['disputeId'] = disputeId;
    data['paymentIntentId'] = paymentIntentId;
    data['subscriptionId'] = subscriptionId;
    data['refundId'] = refundId;
    data['refundReason'] = refundReason;
    return data;
  }
}

class Metadata {
  Metadata({this.isBuyNow});

  Metadata.fromJson(Map<String, dynamic> json) {
    isBuyNow = json['isBuyNow'];
  }
  bool? isBuyNow;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['isBuyNow'] = isBuyNow;
    return data;
  }
}

class Payload {
  Payload({
    this.orderId,
    this.status,
    this.comment,
    this.orderComment,
    this.acceptedOn,
    this.disputedOn,
    this.disputeUpdatedOn,
    this.disputeLastStatus,
    this.fulfilledOn,
    this.refundedOn,
    this.customerPaid,
    this.netAmount,
    this.applicationFee,
    this.shippingProvider,
    this.shippingTracking,
    this.shippingTrackingURL,
    this.customerInfo,
    this.allAddresses,
    this.shippingAddress,
    this.billingAddress,
    this.purchasedItems,
    this.purchasedItemsCount,
    this.totals,
    this.customData,
    this.paypalDetails,
    this.stripeCard,
    this.stripeDetails,
    this.paymentProcessor,
    this.hasDownloads,
    this.downloadFiles,
    this.metadata,
    this.isCustomerDeleted,
    this.isShippingRequired,
  });

  Payload.fromJson(Map<String, dynamic> json) {
    orderId = json['orderId'];
    status = json['status'];
    comment = json['comment'];
    orderComment = json['orderComment'];
    acceptedOn = json['acceptedOn'];
    disputedOn = json['disputedOn'];
    disputeUpdatedOn = json['disputeUpdatedOn'];
    disputeLastStatus = json['disputeLastStatus'];
    fulfilledOn = json['fulfilledOn'];
    refundedOn = json['refundedOn'];
    customerPaid = json['customerPaid'] != null
        ? CustomerPaid.fromJson(json['customerPaid'])
        : null;
    netAmount = json['netAmount'] != null
        ? CustomerPaid.fromJson(json['netAmount'])
        : null;
    applicationFee = json['applicationFee'] != null
        ? ApplicationFee.fromJson(json['applicationFee'])
        : null;
    shippingProvider = json['shippingProvider'];
    shippingTracking = json['shippingTracking'];
    shippingTrackingURL = json['shippingTrackingURL'];
    customerInfo = json['customerInfo'] != null
        ? CustomerInfo.fromJson(json['customerInfo'])
        : null;
    if (json['allAddresses'] != null) {
      allAddresses = <AllAddresses>[];
      json['allAddresses'].forEach((v) {
        allAddresses!.add(AllAddresses.fromJson(v));
      });
    }
    shippingAddress = json['shippingAddress'] != null
        ? AllAddresses.fromJson(json['shippingAddress'])
        : null;
    billingAddress = json['billingAddress'] != null
        ? AllAddresses.fromJson(json['billingAddress'])
        : null;
    if (json['purchasedItems'] != null) {
      purchasedItems = <PurchasedItems>[];
      json['purchasedItems'].forEach((v) {
        purchasedItems!.add(PurchasedItems.fromJson(v));
      });
    }
    purchasedItemsCount = json['purchasedItemsCount'];
    totals = json['totals'] != null ? Totals.fromJson(json['totals']) : null;
    if (json['customData'] != null) {
      customData = <CustomData>[];
      json['customData'].forEach((v) {
        customData!.add(CustomData.fromJson(v));
      });
    }
    paypalDetails = json['paypalDetails'];
    stripeCard = json['stripeCard'] != null
        ? StripeCard.fromJson(json['stripeCard'])
        : null;
    stripeDetails = json['stripeDetails'] != null
        ? StripeDetails.fromJson(json['stripeDetails'])
        : null;
    paymentProcessor = json['paymentProcessor'];
    hasDownloads = json['hasDownloads'];
    if (json['downloadFiles'] != null) {
      // downloadFiles = <Null>[];
      // json['downloadFiles'].forEach((v) {
      //   downloadFiles!.add(new Null.fromJson(v));
      // });
    }
    metadata =
        json['metadata'] != null ? Metadata.fromJson(json['metadata']) : null;
    isCustomerDeleted = json['isCustomerDeleted'];
    isShippingRequired = json['isShippingRequired'];
  }
  String? orderId;
  String? status;
  String? comment;
  String? orderComment;
  String? acceptedOn;
  dynamic disputedOn;
  dynamic disputeUpdatedOn;
  dynamic disputeLastStatus;
  String? fulfilledOn;
  dynamic refundedOn;
  CustomerPaid? customerPaid;
  CustomerPaid? netAmount;
  ApplicationFee? applicationFee;
  String? shippingProvider;
  String? shippingTracking;
  String? shippingTrackingURL;
  CustomerInfo? customerInfo;
  List<AllAddresses>? allAddresses;
  AllAddresses? shippingAddress;
  AllAddresses? billingAddress;
  List<PurchasedItems>? purchasedItems;
  int? purchasedItemsCount;
  Totals? totals;
  List<CustomData>? customData;
  dynamic paypalDetails;
  StripeCard? stripeCard;
  StripeDetails? stripeDetails;
  String? paymentProcessor;
  bool? hasDownloads;
  List? downloadFiles;
  Metadata? metadata;
  bool? isCustomerDeleted;
  bool? isShippingRequired;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['orderId'] = orderId;
    data['status'] = status;
    data['comment'] = comment;
    data['orderComment'] = orderComment;
    data['acceptedOn'] = acceptedOn;
    data['disputedOn'] = disputedOn;
    data['disputeUpdatedOn'] = disputeUpdatedOn;
    data['disputeLastStatus'] = disputeLastStatus;
    data['fulfilledOn'] = fulfilledOn;
    data['refundedOn'] = refundedOn;
    if (customerPaid != null) {
      data['customerPaid'] = customerPaid!.toJson();
    }
    if (netAmount != null) {
      data['netAmount'] = netAmount!.toJson();
    }
    if (applicationFee != null) {
      data['applicationFee'] = applicationFee!.toJson();
    }
    data['shippingProvider'] = shippingProvider;
    data['shippingTracking'] = shippingTracking;
    data['shippingTrackingURL'] = shippingTrackingURL;
    if (customerInfo != null) {
      data['customerInfo'] = customerInfo!.toJson();
    }
    if (allAddresses != null) {
      data['allAddresses'] = allAddresses!.map((v) => v.toJson()).toList();
    }
    if (shippingAddress != null) {
      data['shippingAddress'] = shippingAddress!.toJson();
    }
    if (billingAddress != null) {
      data['billingAddress'] = billingAddress!.toJson();
    }
    if (purchasedItems != null) {
      data['purchasedItems'] = purchasedItems!.map((v) => v.toJson()).toList();
    }
    data['purchasedItemsCount'] = purchasedItemsCount;
    if (totals != null) {
      data['totals'] = totals!.toJson();
    }
    if (customData != null) {
      data['customData'] = customData!.map((v) => v.toJson()).toList();
    }
    data['paypalDetails'] = paypalDetails;
    if (stripeCard != null) {
      data['stripeCard'] = stripeCard!.toJson();
    }
    if (stripeDetails != null) {
      data['stripeDetails'] = stripeDetails!.toJson();
    }
    data['paymentProcessor'] = paymentProcessor;
    data['hasDownloads'] = hasDownloads;
    if (downloadFiles != null) {
      data['downloadFiles'] = downloadFiles!.map((v) => v.toJson()).toList();
    }
    if (metadata != null) {
      data['metadata'] = metadata!.toJson();
    }
    data['isCustomerDeleted'] = isCustomerDeleted;
    data['isShippingRequired'] = isShippingRequired;
    return data;
  }
}

class CustomData {
  CustomData({this.name, this.textInput, this.textArea});

  CustomData.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    textInput = json['textInput'];
    textArea = json['textArea'];
  }
  String? name;
  String? textInput;
  String? textArea;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['name'] = name;
    data['textInput'] = textInput;
    data['textArea'] = textArea;
    return data;
  }
}

class Products {
  Products({this.data});

  Products.fromJson(Map<String, dynamic> json) {
    data = json['data'];
  }
  int? data;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['data'] = this.data;
    return data;
  }
}

class HostNameForDropdown {
  HostNameForDropdown({this.hostName, this.id, this.portNumber});

  HostNameForDropdown.fromJson(Map<String, dynamic> json) {
    hostName = json['hostName'];
    id = json['id'];
    portNumber = json['portNumber'];
  }
  String? hostName;
  String? id;
  int? portNumber;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['hostName'] = hostName;
    data['id'] = id;
    data['portNumber'] = portNumber;
    return data;
  }
}
