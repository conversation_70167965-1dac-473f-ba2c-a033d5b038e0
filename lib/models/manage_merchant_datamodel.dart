import 'dart:developer';

import 'package:admin_portal/configs/configs_export.dart';
import 'package:admin_portal/models/models_export.dart';
import 'package:admin_portal/models/order_list_model_new.dart';
import 'package:admin_portal/provider/provider_export.dart';
import 'package:admin_portal/routes/route_names.dart';
import 'package:admin_portal/utils/app_strings.dart';
import 'package:admin_portal/utils/constants/global_config.dart';
import 'package:admin_portal/utils/helper_functions.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:iconsax/iconsax.dart';
import 'package:provider/provider.dart';

class ManageMerchantData extends DataTableSource {
  ManageMerchantData(this.total,this.data, this.hostDropDownList, this.context);

  final List<HostNameForDropdown> hostDropDownList;
  final List<CustomersNew> data;
  final BuildContext context;
  int total ;

  String hostName = '';

  bool ishostnameSelected = true;
  bool ishostnameSelectedOnline = true;
  TextEditingController phoneController = TextEditingController();
  FocusNode phoneFocusNode = FocusNode();
  String dropDownValue = 'Provisioning';
  void updateData(List<CustomersNew> newData, int newTotal) {
    data.clear();
    data.addAll(newData);
    total = newTotal;
    notifyListeners();
  }

  @override
  DataRow? getRow(int index) {
    print("currentPageOrderListPagination ${GlobalConfig().currentPageOrderListPagination} $index");
    
    // Check if data is empty first
    if (data.isEmpty) {
      return null;
    }
    
    // Handle index out of range by calculating the correct local index
    if (index >= data.length) {
      // Calculate the local index within the current page
      int localIndex = index % data.length;
      
      // Ensure the local index is within bounds
      if (localIndex >= data.length) {
        return null;
      }
      
      index = localIndex;
    }

    return DataRow.byIndex(
      index: index,
      cells: [
        DataCell(
          Builder(
            builder: (context) {
              return Row(
                children: [
                  SelectableText(
                    data[index].orderId!.isNotEmpty ? data[index].orderId! : '',
                    style: AppTextStyles.textStyleBold14(context).copyWith(
                      color: Provider.of<DarkThemeProvider>(context).darkTheme
                          ? AppColors.kDarkPrimarySwatch
                          : AppColors.kLightPrimarySwatch,
                    ),
                  ),
                  data[index].orderId!.isNotEmpty
                      ? IconButton(
                          onPressed: () {
                            log('OrderId: ${data[index].orderId!}');
                            data[index].orderId!.isNotEmpty
                                ? context.go(
                                    context.namedLocation(
                                      AppRouteNames.orderDetails,
                                      pathParameters: {
                                        'orderId': data[index].orderId!,
                                      },
                                    ),
                                    extra: {
                                      'email': data[index].email!,
                                    },
                                  )
                                : () {};
                          },
                          icon: Icon(
                            Icons.article_outlined,
                            color: Provider.of<DarkThemeProvider>(context)
                                    .darkTheme
                                ? AppColors.kDarkPrimarySwatch
                                : AppColors.kLightPrimarySwatch,
                          ),
                        )
                      : Container(),
                ],
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              return Row(
                children: [
                  SelectableText(
                    data[index].status!.isNotEmpty
                        ? HelperFunctions.returnStatus(data[index].status!)
                        : '',
                    style: AppTextStyles.textStyleBold14(context).copyWith(
                      color: Provider.of<DarkThemeProvider>(context).darkTheme
                          ? AppColors.kDarkPrimarySwatch
                          : AppColors.kLightPrimarySwatch,
                    ),
                  ),
                  data[index].orderId!.isNotEmpty
                      ? IconButton(
                          onPressed: () {
                            log('Country Code: ${data[index].phone!.countryCode!}  ::: number: ${data[index].phone!.number!}');

                            phoneController.text = data[index].phone!.number!;
                            Provider.of<MyTeamProvider>(context, listen: false)
                                .editOrderDialog(
                              context,
                              data[index].servers!.isEmpty
                                  ? ''
                                  : data[index].servers![0].userId!.isNotEmpty
                                      ? data[index].servers![0].userId!
                                      : '',
                              data[index].orderId!,
                              data[index].firstName!,
                              data[index].lastName!,
                              data[index].email!,
                              data[index].phone!.countryCode!,
                              data[index].countryCodeString!,
                              phoneController,
                              phoneFocusNode,
                              data[index].servers!,
                              hostDropDownList,
                              data[index].status!.isEmpty
                                  ? dropDownValue
                                  : (data[index].status! ==
                                              AppStrings.Provisioning ||
                                          data[index].status! ==
                                              AppStrings.Configuring ||
                                          data[index].status! ==
                                              AppStrings.QA_Testing ||
                                          data[index].status! ==
                                              AppStrings.QATesting ||
                                          data[index].status! ==
                                              AppStrings.PackAndShip ||
                                          data[index].status! ==
                                              AppStrings.PackShip ||
                                          data[index].status! ==
                                              AppStrings.Shipped)
                                      ? data[index].status!
                                      : data[index].status! ==
                                              AppStrings.Received
                                          ? AppStrings.Shipped
                                          : dropDownValue,
                            );
                          },
                          icon: Icon(
                            Iconsax.edit,
                            color: Provider.of<DarkThemeProvider>(context)
                                    .darkTheme
                                ? AppColors.kDarkPrimarySwatch
                                : AppColors.kLightPrimarySwatch,
                          ),
                        )
                      : Container(),
                ],
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              return Row(
                children: [
                  SelectableText(
                    '          ',
                    style: AppTextStyles.textStyleBold14(context).copyWith(
                      color: Provider.of<DarkThemeProvider>(context).darkTheme
                          ? AppColors.kDarkPrimarySwatch
                          : AppColors.kLightPrimarySwatch,
                    ),
                  ),
                  data[index].orderId!.isNotEmpty
                      ? IconButton(
                          onPressed: () {
                            log('Country Code: ${data[index].phone!.countryCode!}  ::: number: ${data[index].phone!.number!}');

                            phoneController.text = data[index].phone!.number!;
                            Provider.of<MyTeamProvider>(context, listen: false)
                                .editHostNameDialog(
                              context,
                              data[index].servers!.isEmpty
                                  ? ''
                                  : data[index].servers![0].userId!.isNotEmpty
                                      ? data[index].servers![0].userId!
                                      : '',
                              data[index].orderId!,
                              data[index].firstName!,
                              data[index].lastName!,
                              data[index].email!,
                              data[index].phone!.countryCode!,
                              data[index].countryCodeString!,
                              phoneController,
                              phoneFocusNode,
                              data[index].servers!,
                              hostDropDownList,
                              data[index].status!.isEmpty
                                  ? dropDownValue
                                  : (data[index].status! ==
                                              AppStrings.Provisioning ||
                                          data[index].status! ==
                                              AppStrings.Configuring ||
                                          data[index].status! ==
                                              AppStrings.QA_Testing ||
                                          data[index].status! ==
                                              AppStrings.QATesting ||
                                          data[index].status! ==
                                              AppStrings.PackAndShip ||
                                          data[index].status! ==
                                              AppStrings.PackShip ||
                                          data[index].status! ==
                                              AppStrings.Shipped)
                                      ? data[index].status!
                                      : data[index].status! ==
                                              AppStrings.Received
                                          ? AppStrings.Shipped
                                          : dropDownValue,
                            );
                          },
                          icon: Icon(
                            Iconsax.edit,
                            color: Provider.of<DarkThemeProvider>(context)
                                    .darkTheme
                                ? AppColors.kDarkPrimarySwatch
                                : AppColors.kLightPrimarySwatch,
                          ),
                        )
                      : Container(),
                ],
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 25),
                child: SelectableText(
                  data[index].servers!.isNotEmpty
                      ? returnHostnames(data[index].servers!)
                      : '0/0',
                  style: AppTextStyles.textStyleBold14(context).copyWith(
                    color: data[index].servers!.isNotEmpty
                        ? ishostnameSelected
                            ? AppColors.kGreen
                            : AppColors.kRed
                        : AppColors.kRed,
                  ),
                ),
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: SelectableText(
                  data[index].servers!.isNotEmpty
                      ? returnHostnamesOnline(data[index].servers!)
                      : '0/0',
                  style: AppTextStyles.textStyleBold14(context).copyWith(
                    color: data[index].servers!.isNotEmpty
                        ? ishostnameSelectedOnline
                            ? AppColors.kGreen
                            : AppColors.kRed
                        : AppColors.kRed,
                  ),
                ),
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              return SelectableText(
                data[index].acceptedOn!.isNotEmpty
                    ? HelperFunctions.convertToDate(data[index].acceptedOn!)
                    : '',
                style: AppTextStyles.textStyleBold14(context),
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              return SelectableText(
                data[index].customerName!.isEmpty
                    ? data[index].customerInfo!.fullName!
                    : data[index].customerName!,
                style: AppTextStyles.textStyleBold14(context),
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              return SelectableText(
                data[index].customerEmail!.isEmpty
                    ? data[index].customerInfo!.email!
                    : data[index].customerEmail!,
                style: AppTextStyles.textStyleBold14(context),
              );
            },
          ),
        ),
      ],
    );
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => total;

  @override
  int get selectedRowCount => 0;

  Color getStatusColor(MerchantStatus status) {
    switch (status) {
      case MerchantStatus.ACTIVE:
        return AppColors.kGreen;
      case MerchantStatus.INVITED:
        return AppColors.kYellow;
      case MerchantStatus.STOPPED:
        return AppColors.kRed;
      default:
        return Colors.green;
    }
  }

  String getStatusText(MerchantStatus status) {
    switch (status) {
      case MerchantStatus.ACTIVE:
        return 'ACTIVE';
      case MerchantStatus.INVITED:
        return 'INVITED';
      case MerchantStatus.STOPPED:
        return 'STOPPED';
      default:
        return 'ACTIVE';
    }
  }

  Widget hideMerchantIcon(int index, bool hideMerchant, String companyId) {
    if (hideMerchant) {
      return IconButton(
        onPressed: () {
          clickOnHideMerchant(index, companyId);
        },
        icon: const Icon(
          Icons.visibility_off_outlined,
        ),
      );
    } else {
      return IconButton(
        onPressed: () {
          clickOnHideMerchant(index, companyId);
        },
        icon: const Icon(
          Icons.visibility_outlined,
        ),
      );
    }
  }

  void clickOnViewDetails(int index) {}
  void clickOnHideMerchant(int index, String companyId) async {}
  bool returnHostnamesChecked(List<Servers> servers) {
    var ishostnameSelected = true;
    for (var i = 0; i < servers.length; i++) {
      if (servers[i].hostName!.isEmpty) {
        ishostnameSelected = false;

        return ishostnameSelected;
      }
    }
    return ishostnameSelected;
  }

  String returnHostnames(List<Servers> servers) {
    ishostnameSelected = true;
    var count = 0;
    var hostnameSelected = '$count/${servers.length}';
    for (var i = 0; i < servers.length; i++) {
      if (servers[i].hostName!.isNotEmpty) {
        count = count + 1;
      } else {
        ishostnameSelected = false;
      }
    }
    hostnameSelected = '$count/${servers.length}';
    return hostnameSelected;
  }

  String returnHostnamesOnline(List<Servers> servers) {
    ishostnameSelectedOnline = true;
    var count = 0;
    var ishostnameSelected = '$count/${servers.length}';
    for (var i = 0; i < servers.length; i++) {
      if (servers[i].hostName!.isNotEmpty) {
        if (servers[i].isOnline!) {
          count = count + 1;
        } else {
          ishostnameSelectedOnline = false;
        }
      } else {
        ishostnameSelectedOnline = false;
      }
    }
    ishostnameSelected = '$count/${servers.length}';
    return ishostnameSelected;
  }
}
