import 'package:admin_portal/configs/configs_export.dart';
import 'package:admin_portal/models/models_export.dart';
import 'package:admin_portal/provider/provider_export.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:iconsax/iconsax.dart';
import 'package:provider/provider.dart';

class MyTeamUsersData extends DataTableSource {
  MyTeamUsersData(this.data, this.context);
  final List<AdminUsers> data;
  final BuildContext context;
  TextEditingController phoneController = TextEditingController();
  FocusNode phoneFocusNode = FocusNode();
  String dropDownValue = 'ADMIN';
  @override
  DataRow? getRow(int index) {
    // TODO: implement getRow
    return DataRow.byIndex(
      index: index,
      cells: [
        DataCell(
          Builder(
            builder: (context) {
              return Row(
                children: [
                  Container(
                    height: 30,
                    width: 30,
                    decoration: BoxDecoration(
                      color:
                          Provider.of<DarkThemeProvider>(context, listen: false)
                                  .darkTheme
                              ? AppColors.kDarkPrimarySwatch
                              : AppColors.kLightPrimarySwatch,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Center(
                      child: Text(
                        data[index].name.substring(0, 1),
                        style: GoogleFonts.lemon(
                          fontSize: 16,
                          fontWeight: FontWeight.w400,
                          color: AppColors.kWhite,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(
                    width: 10,
                  ),
                  Text(
                    data[index].id,
                    style: AppTextStyles.textStyleBold14(context),
                  ),
                ],
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              return Text(
                data[index].name,
                style: AppTextStyles.textStyleBold14(context),
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              return Text(
                data[index].email,
                style: AppTextStyles.textStyleBold14(context),
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              return Text(
                data[index].role[0],
                style: AppTextStyles.textStyleBold14(context),
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context2) {
              return Row(
                children: [
                  TextButton(
                    onPressed: () {
                      Provider.of<MyTeamProvider>(context, listen: false)
                          .editUserDialog(
                        context,
                        data[index].id,
                        data[index].name,
                        '',
                        data[index].email,
                        '',
                        '',
                        phoneController,
                        phoneFocusNode,
                        dropDownValue,
                      );
                    },
                    child: Text(
                      'EDIT',
                      style: AppTextStyles.textStyleBold14(context2).copyWith(
                        color: Provider.of<DarkThemeProvider>(context).darkTheme
                            ? AppColors.kDarkPrimarySwatch
                            : AppColors.kLightPrimarySwatch,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      Provider.of<MyTeamProvider>(context, listen: false)
                          .deleteUserDialog(context, data[index].id);
                    },
                    icon: Icon(Iconsax.trash, color: AppColors.kRed),
                  ),
                ],
              );
            },
          ),
        ),
      ],
    );
  }

  @override
  // TODO: implement isRowCountApproximate
  bool get isRowCountApproximate => false;

  @override
  // TODO: implement rowCount
  int get rowCount => data.length;

  @override
  // TODO: implement selectedRowCount
  int get selectedRowCount => 0;
}
