import 'package:admin_portal/configs/configs_export.dart';
import 'package:admin_portal/models/order_list_model_new.dart';
import 'package:admin_portal/provider/provider_export.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

class OrderSub1Data extends DataTableSource {
  OrderSub1Data(this.data, this.context);
  final List<CustomersNew> data;
  final BuildContext context;
  @override
  DataRow? getRow(int index) {
    // TODO: implement getRow
    return DataRow.byIndex(
      index: index,
      cells: [
        DataCell(
          Builder(
            builder: (context) {
              return Row(
                children: [
                  Container(
                    height: 30,
                    width: 30,
                    decoration: BoxDecoration(
                      color:
                          Provider.of<DarkThemeProvider>(context, listen: false)
                                  .darkTheme
                              ? AppColors.kDarkPrimarySwatch
                              : AppColors.kLightPrimarySwatch,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Center(
                      child: Text(
                        data[index].orderId!.substring(0, 1),
                        style: GoogleFonts.lemon(
                          fontSize: 16,
                          fontWeight: FontWeight.w400,
                          color: AppColors.kWhite,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(
                    width: 10,
                  ),
                  Text(
                    data[index].orderId!,
                    style: AppTextStyles.textStyleBold14(context),
                  ),
                ],
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              return Text(
                data[index].comment!,
                style: AppTextStyles.textStyleBold14(context),
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              return Text(
                '${data[index].shippingAddress!.addressee!} \n${data[index].shippingAddress!.line1!} \n${data[index].shippingAddress!.city!}\n${data[index].shippingAddress!.state} \n${data[index].shippingAddress!.country!} \n${data[index].shippingAddress!.postalCode!}',
                style: AppTextStyles.textStyleBold14(context),
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              return Text(
                '${data[index].billingAddress!.addressee!} \n${data[index].billingAddress!.line1!} \n${data[index].billingAddress!.city!}\n${data[index].billingAddress!.state} \n${data[index].billingAddress!.country!} \n${data[index].billingAddress!.postalCode!}',
                style: AppTextStyles.textStyleBold14(context),
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              return Text(
                data[index].customerPaid!.string!,
                style: AppTextStyles.textStyleBold14(context),
              );
            },
          ),
        ),

        // DataCell(Builder(builder: (context) {
        //   return Text(data[index].role,
        //       style: AppTextStyles.textStyleBold14(context));
        // })),
        DataCell(
          Builder(
            builder: (context) {
              return Row(
                children: [
                  TextButton(
                    onPressed: () {},
                    child: Text(
                      data[index].purchasedItems!.isNotEmpty
                          ? returnPurchaseItems(data[index].purchasedItems!)
                          : '',
                      // "View Details",
                      style: AppTextStyles.textStyleBold14(context).copyWith(
                        color: Provider.of<DarkThemeProvider>(context).darkTheme
                            ? AppColors.kDarkPrimarySwatch
                            : AppColors.kLightPrimarySwatch,
                      ),
                    ),
                  ),
                  // IconButton(
                  //     onPressed: () {},
                  //     icon: Icon(Iconsax.trash, color: AppColors.kRed))
                ],
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              return Row(
                children: [
                  TextButton(
                    onPressed: () {},
                    child: Text(
                      'Name: ${data[index].stripeCard!.ownerName!}\nlast4: ${data[index].stripeCard!.last4!}\nbrand: ${data[index].stripeCard!.brand!}\nmonth: ${data[index].stripeCard!.expires!.month!}\nyear: ${data[index].stripeCard!.expires!.year}\n',
                      // "View Details",
                      style: AppTextStyles.textStyleBold14(context).copyWith(
                        color: Provider.of<DarkThemeProvider>(context).darkTheme
                            ? AppColors.kDarkPrimarySwatch
                            : AppColors.kLightPrimarySwatch,
                      ),
                    ),
                  ),
                  // IconButton(
                  //     onPressed: () {},
                  //     icon: Icon(Iconsax.trash, color: AppColors.kRed))
                ],
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              return Row(
                children: [
                  TextButton(
                    onPressed: () {},
                    child: Text(
                      returnServerItems(data[index].servers!),
                      // "View Details",
                      style: AppTextStyles.textStyleBold14(context).copyWith(
                        color: Provider.of<DarkThemeProvider>(context).darkTheme
                            ? AppColors.kDarkPrimarySwatch
                            : AppColors.kLightPrimarySwatch,
                      ),
                    ),
                  ),
                  // IconButton(
                  //     onPressed: () {},
                  //     icon: Icon(Iconsax.trash, color: AppColors.kRed))
                ],
              );
            },
          ),
        ),
      ],
    );
  }

  @override
  // TODO: implement isRowCountApproximate
  bool get isRowCountApproximate => false;

  @override
  // TODO: implement rowCount
  int get rowCount => data.length;

  @override
  // TODO: implement selectedRowCount
  int get selectedRowCount => 0;
}

String returnServerItems(List<Servers> list) {
  var purchasednames = '';
  for (var i = 0; i < list.length; i++) {
    purchasednames =
        '${purchasednames}ServerID: ${list[i].serverID}         LicenseLinked: ${list[i].licenseLinked} \n';
    // return purchasedItems[i].productName!;
  }
  return purchasednames;
}

String returnPurchaseItems(List<PurchasedItems> purchasedItems) {
  var purchasednames = '';
  for (var i = 0; i < purchasedItems.length; i++) {
    purchasednames = "$purchasednames${purchasedItems[i].productName} \n";
    // return purchasedItems[i].productName!;
  }
  return purchasednames;
}
