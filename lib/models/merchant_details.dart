// To parse this JSON data, do
//
//     final merchantDetails = merchantDetailsFromJson(jsonString);

import 'dart:convert';

enum MerchantStatus {
  ACTIVE,
  INVITED, //pending
  STOPPED,
  UNKNOWN,
}

MerchantDetails merchantDetailsFromJson(String str) =>
    MerchantDetails.fromJson(json.decode(str));

String merchantDetailsToJson(MerchantDetails data) =>
    json.encode(data.toJson());

MerchantDetailsResponse merchantDetailsResponseFromJson(String str) =>
    MerchantDetailsResponse.fromJson(json.decode(str));

String merchantDetailsResponseToJson(MerchantDetailsResponse data) =>
    json.encode(data.toJson());

class MerchantDetailsResponse {
  MerchantDetailsResponse({
    required this.documents,
    required this.page,
    required this.limit,
    required this.total,
    required this.totalPages,
  });

  factory MerchantDetailsResponse.fromJson(Map<String, dynamic> json) =>
      MerchantDetailsResponse(
        documents: List<MerchantDetails>.from(
          json['documents'].map((x) => MerchantDetails.fromJson(x)),
        ),
        page: json['page'],
        limit: json['limit'],
        total: json['total'],
        totalPages: json['totalPages'],
      );
  List<MerchantDetails> documents;
  String page;
  String limit;
  int total;
  dynamic totalPages;

  Map<String, dynamic> toJson() => {
        'documents': List<dynamic>.from(documents.map((x) => x.toJson())),
        'page': page,
        'limit': limit,
        'total': total,
        'totalPages': totalPages,
      };
}

class MerchantDetails {
  MerchantDetails({
    required this.id,
    required this.userName,
    required this.email,
    required this.status,
    required this.phone,
    required this.hidden,
    required this.dateAdded,
    required this.merchantId,
    required this.compnayLogo,
    required this.name,
    required this.siteName,
    required this.supportEmail,
  });

  factory MerchantDetails.fromJson(Map<String, dynamic> json) =>
      MerchantDetails(
        id: json['_id'],
        userName: json['user_name'],
        email: json['email'],
        status: getMerchantStatus(json['status']),
        phone: Phone.fromJson(json['phone']),
        hidden: json['hidden'],
        dateAdded: json['dateAdded'] ?? DateTime.now().toString(),
        merchantId: json['merchantid'] ?? '',
        compnayLogo: CompnayLogo.fromJson(json['compnay_logo'] ?? {}),
        name: json['name'] ?? '',
        siteName: json['site_name'] ?? '',
        supportEmail: json['support_email'] ?? '',
      );
  String id;
  String userName;
  String email;
  MerchantStatus status;
  Phone phone;
  bool hidden;
  String dateAdded;
  String merchantId;
  CompnayLogo compnayLogo;
  String name;
  String siteName;
  String supportEmail;

  Map<String, dynamic> toJson() => {
        '_id': id,
        'user_name': userName,
        'email': email,
        'status': getStatusText(status),
        'phone': phone.toJson(),
        'hidden': hidden,
        'dateAdded': dateAdded,
        'merchantId': merchantId,
        'compnay_logo': compnayLogo.toJson(),
        'name': name,
        'site_name': siteName,
        'support_email': supportEmail,
      };

  static MerchantStatus getMerchantStatus(String status) {
    switch (status) {
      case 'active':
        return MerchantStatus.ACTIVE;
      // case "inactive":
      //   return MerchantStatus.INACTIVE;
      case 'pending':
        return MerchantStatus.INVITED;
      case 'inactive':
        return MerchantStatus.STOPPED;
      default:
        return MerchantStatus.UNKNOWN;
    }
  }

  static String getStatusText(MerchantStatus status) {
    switch (status) {
      case MerchantStatus.ACTIVE:
        return 'active';
      case MerchantStatus.INVITED:
        return 'pending';
      case MerchantStatus.STOPPED:
        return 'stopped';
      default:
        return 'unknown';
    }
  }
}

class CompnayLogo {
  CompnayLogo({
    required this.dark,
    required this.light,
  });

  factory CompnayLogo.fromJson(Map<String, dynamic> json) => CompnayLogo(
        dark: json['dark'] ?? '',
        light: json['light'] ?? '',
      );
  String dark;
  String light;

  Map<String, dynamic> toJson() => {
        'dark': dark,
        'light': light,
      };
}

class Phone {
  Phone({
    required this.countryCode,
    required this.number,
  });

  factory Phone.fromJson(Map<String, dynamic> json) => Phone(
        countryCode: json['countryCode'],
        number: json['number'],
      );
  String countryCode;
  String number;

  Map<String, dynamic> toJson() => {
        'countryCode': countryCode,
        'number': number,
      };
}
