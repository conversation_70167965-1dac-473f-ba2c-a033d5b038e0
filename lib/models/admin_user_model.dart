class AdminUser {

  factory AdminUser.fromJson(Map<String, dynamic> json) {
    final user = json['user'] ?? {};
    final phone = user['phone'] ?? {};
    
    return AdminUser(
      userId: user['userId'] ?? '',
      fullName: user['fullName'] ?? '',
      email: user['email'] ?? '',
      countryCode: phone['countryCode'] ?? '',
      phoneNumber: phone['number'] ?? '',
      kycVerificationStatus: user['kycVerificationStatus'] ?? 'notstarted',
      accessToken: json['access_token'],
    );
  }
  final String userId;
  final String fullName;
  final String email;
  final String countryCode;
  final String phoneNumber;
  final String kycVerificationStatus;
  final String? accessToken;

  const AdminUser({
    required this.userId,
    required this.fullName,
    required this.email,
    required this.countryCode,
    required this.phoneNumber,
    required this.kycVerificationStatus,
    this.accessToken,
  });

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'fullName': fullName,
      'email': email,
      'countryCode': countryCode,
      'phoneNumber': phoneNumber,
      'kycVerificationStatus': kycVerificationStatus,
      if (accessToken != null) 'accessToken': accessToken,
    };
  }

  AdminUser copyWith({
    String? userId,
    String? fullName,
    String? email,
    String? countryCode,
    String? phoneNumber,
    String? kycVerificationStatus,
    String? accessToken,
  }) {
    return AdminUser(
      userId: userId ?? this.userId,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      countryCode: countryCode ?? this.countryCode,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      kycVerificationStatus: kycVerificationStatus ?? this.kycVerificationStatus,
      accessToken: accessToken ?? this.accessToken,
    );
  }

  @override
  String toString() {
    return 'AdminUser(userId: $userId, fullName: $fullName, email: $email, countryCode: $countryCode, phoneNumber: $phoneNumber, kycVerificationStatus: $kycVerificationStatus)';
  }
}

class LoginResponse {
  final String userId;
  final String message;
  final bool success;

  const LoginResponse({
    required this.userId,
    required this.message,
    required this.success,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      userId: json['userId'] ?? '',
      message: json['message'] ?? '',
      success: json['success'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'message': message,
      'success': success,
    };
  }
}

