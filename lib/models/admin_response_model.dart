// To parse this JSON data, do
//
//     final adminUsersResponseModel = adminUsersResponseModelFromJson(jsonString);

import 'dart:convert';

AdminUsersResponseModel adminUsersResponseModelFromJson(String str) =>
    AdminUsersResponseModel.fromJson(json.decode(str));

String adminUsersResponseModelToJson(AdminUsersResponseModel data) =>
    json.encode(data.toJson());

class AdminUsersResponseModel {
  AdminUsersResponseModel({
    required this.documents,
    required this.page,
    required this.limit,
    required this.total,
    required this.totalPages,
  });

  factory AdminUsersResponseModel.fromJson(Map<String, dynamic> json) =>
      AdminUsersResponseModel(
        documents: List<AdminUsers>.from(
          json['documents'].map((x) => AdminUsers.fromJson(x)),
        ),
        page: json['page'],
        limit: json['limit'],
        total: json['total'],
        totalPages: json['totalPages'],
      );
  List<AdminUsers> documents;
  int page;
  int limit;
  int total;
  int totalPages;

  Map<String, dynamic> toJson() => {
        'documents': List<dynamic>.from(documents.map((x) => x.toJson())),
        'page': page,
        'limit': limit,
        'total': total,
        'totalPages': totalPages,
      };
}

class AdminUsers {
  AdminUsers({
    required this.id,
    required this.name,
    required this.email,
    required this.role,
    required this.userType,
    required this.companyIds,
    required this.userStatus,
    this.otp,
    this.otpExpiresIn,
  });

  factory AdminUsers.fromJson(Map<String, dynamic> json) => AdminUsers(
        id: json['_id'],
        name: json['name'],
        email: json['email'],
        role: List<String>.from(json['role'].map((x) => x)),
        userType: json['userType'],
        companyIds: List<dynamic>.from(json['companyIds'].map((x) => x)),
        userStatus: json['userStatus'],
        otp: json['otp'],
        otpExpiresIn: json['otp_expires_in'] == null
            ? null
            : DateTime.parse(json['otp_expires_in']),
      );
  String id;
  String name;
  String email;
  List<String> role;
  String userType;
  List<dynamic> companyIds;
  String userStatus;
  String? otp;
  DateTime? otpExpiresIn;

  Map<String, dynamic> toJson() => {
        '_id': id,
        'name': name,
        'email': email,
        'role': List<dynamic>.from(role.map((x) => x)),
        'userType': userType,
        'companyIds': List<dynamic>.from(companyIds.map((x) => x)),
        'userStatus': userStatus,
        'otp': otp,
        'otp_expires_in': otpExpiresIn?.toIso8601String(),
      };
}
