import 'dart:developer';

import 'package:admin_portal/configs/configs_export.dart';
import 'package:admin_portal/models/models_export.dart';
import 'package:admin_portal/models/order_list_model.dart';
import 'package:admin_portal/provider/provider_export.dart';
import 'package:admin_portal/routes/route_names.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:iconsax/iconsax.dart';
import 'package:provider/provider.dart';

class ManageMerchantData extends DataTableSource {
  ManageMerchantData(this.data, this.context);
  // final List<MerchantDetails> data;
  final List<OrderCustomers> data;
  // final List<CustomersNew> data;
  final BuildContext context;
  TextEditingController phoneController = TextEditingController();
  FocusNode phoneFocusNode = FocusNode();
  String dropDownValue = 'Provisioning';
  // String dropDownValue = "ADMIN";

  // @override
  // DataRow? getRow(int index) {
  //   // TODO: implement getRow
  //   DateTime targetDateTime = DateTime.parse(data[index].dateAdded);
  //   return DataRow.byIndex(index: index, cells: [
  //     DataCell(Builder(builder: (context) {
  //       return Text(data[index].merchantId,
  //           style: AppTextStyles.textStyleBold14(context));
  //     })),
  //     DataCell(Builder(builder: (context) {
  //       return Text(
  //         data[index].userName,
  //         style: AppTextStyles.textStyleBold14(context),
  //       );
  //     })),
  //     DataCell(Builder(builder: (context) {
  //       // return Text(
  //       //   DateFormat('MMM d, y').format(
  //       //     targetDateTime,
  //       //   ),
  //       //   style: AppTextStyles.textStyleBold14(context),
  //       // );
  //       return Text("XXXXXXXXXX");
  //     })),

  //     //TODO: Add hide merchant as per API
  //     // DataCell(hideMerchantIcon(index, data[index].hidden, data[index].id)),
  //     DataCell(Builder(
  //       builder: (context) {
  //         return Text("<EMAIL>");
  //       },
  //     )),
  //     DataCell(Builder(builder: (context) {
  //       // return Text(getStatusText(data[index].status),
  //       return Text("Processing",
  //           style: AppTextStyles.textStyleBlueBold(context).copyWith(
  //             color: getStatusColor(data[index].status),
  //           ));
  //     })),
  //     DataCell(Builder(
  //       builder: (context) {
  //         return Text("Hyderabad \nTelangana");
  //       },
  //     )),
  //     DataCell(Builder(
  //       builder: (context) {
  //         return Text("Yocto - NanoServer");
  //       },
  //     )),
  //     DataCell(Builder(
  //       builder: (context) {
  //         return Text("stripe");
  //       },
  //     )),
  //     DataCell(Builder(builder: (context) {
  //       return TextButton(
  //         style: TextButton.styleFrom(
  //             textStyle: AppTextStyles.textStyleBold14(context).copyWith(
  //           decoration: TextDecoration.underline,
  //         )),
  //         onPressed: () {
  //           Provider.of<MyTeamProvider>(context, listen: false).editOrderDialog(
  //             context,
  //             data[index].id,
  //             data[index].name,
  //             data[index].email,
  //             phoneController,
  //             phoneFocusNode,
  //             dropDownValue,
  //           );
  //           // context.go(
  //           //   context.namedLocation(
  //           //     AppRouteNames.merchantDetails,
  //           //     pathParameters: {
  //           //       "merchantId": data[index].id,
  //           //     },
  //           //   ),
  //           // );
  //         },
  //         // child: const Text(
  //         //   "View Details",
  //         // ),
  //         child: Text(
  //           "EDIT",
  //           style: AppTextStyles.textStyleBold14(context).copyWith(
  //               color: Provider.of<DarkThemeProvider>(context).darkTheme
  //                   ? AppColors.kDarkPrimarySwatch
  //                   : AppColors.kLightPrimarySwatch),
  //         ),
  //       );
  //     })),
  //   ]);
  // }

  @override
  DataRow? getRow(int index) {
    // TODO: implement getRow
    // DateTime targetDateTime = DateTime.parse(data[index].dateAdded);
    return DataRow.byIndex(
      index: index,
      cells: [
        DataCell(
          Builder(
            builder: (context) {
              return Row(
                children: [
                  Text(
                    data[index].orders!.isNotEmpty
                        ? data[index].orders![0].orderId!
                        : '',
                    style: AppTextStyles.textStyleBold14(context).copyWith(
                      color: Provider.of<DarkThemeProvider>(context).darkTheme
                          ? AppColors.kDarkPrimarySwatch
                          : AppColors.kLightPrimarySwatch,
                    ),
                  ),
                  data[index].orders!.isNotEmpty
                      ? IconButton(
                          onPressed: () {
                            log('OrderId: ${data[index].orders![0].orderId!}');
                            data[index].orders!.isNotEmpty
                                ? context.go(
                                    context.namedLocation(
                                      AppRouteNames.orderDetails,
                                      pathParameters: {
                                        'orderId':
                                            data[index].orders![0].orderId!,
                                        // "email": data[index].email!,
                                        // "orderDetails": data[index];
                                      },
                                      // queryParameters: {
                                      //   "email": data[index].email!,
                                      // }
                                    ),
                                    extra: {
                                      'email': data[index].email!,
                                    },
                                  )
                                : () {};
                          },
                          icon: Icon(
                            Icons.content_paste_go,
                            color: Provider.of<DarkThemeProvider>(context)
                                    .darkTheme
                                ? AppColors.kDarkPrimarySwatch
                                : AppColors.kLightPrimarySwatch,
                          ),
                        )
                      : Container(),
                ],
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              return Text(
                '${data[index].firstName!} ${data[index].lastName!}',
                style: AppTextStyles.textStyleBold14(context),
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              // return Text(
              //   DateFormat('MMM d, y').format(
              //     targetDateTime,
              //   ),
              //   style: AppTextStyles.textStyleBold14(context),
              // );
              return Text(
                data[index].phone!.countryCode! + data[index].phone!.number!,
                style: AppTextStyles.textStyleBold14(context),
              );
            },
          ),
        ),

        //TODO: Add hide merchant as per API
        // DataCell(hideMerchantIcon(index, data[index].hidden, data[index].id)),
        DataCell(
          Builder(
            builder: (context) {
              return Text(
                data[index].email!,
                style: AppTextStyles.textStyleBold14(context),
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              // return Text(getStatusText(data[index].status),
              return Row(
                children: [
                  Text(
                    data[index].orders!.isNotEmpty
                        ? data[index].orders![0].status!
                        : '',
                    style: AppTextStyles.textStyleBold14(context).copyWith(
                      color: Provider.of<DarkThemeProvider>(context).darkTheme
                          ? AppColors.kDarkPrimarySwatch
                          : AppColors.kLightPrimarySwatch,
                    ),
                    // style: AppTextStyles.textStyleBlueBold(context).copyWith(
                    // color: getStatusColor(data[index].orders![0].status!),
                  ),
                  data[index].orders!.isNotEmpty
                      ? IconButton(
                          onPressed: () {
                            log('Country Code: ${data[index].phone!.countryCode!}  ::: number: ${data[index].phone!.number!}');

                            // Provider.of<MyTeamProvider>(context, listen: false)
                            //     .deleteUserDialog(context, data[index].id);
                            phoneController.text = data[index].phone!.number!;
                            // Provider.of<MyTeamProvider>(context, listen: false)
                            //     .editOrderDialog(
                            //   context,
                            //   data[index].userId!,
                            //   data[index].orders![0].orderId!,
                            //   data[index].orders![0].servers![0].serverID!,
                            //   data[index].firstName!,
                            //   data[index].email!,
                            //   data[index].phone!.countryCode!,
                            //   data[index].countryCodeString!,
                            //   phoneController,
                            //   phoneFocusNode,
                            //   dropDownValue,
                            // );
                          },
                          icon: Icon(
                            Iconsax.edit,
                            color:
                                // Colors.blue
                                Provider.of<DarkThemeProvider>(context)
                                        .darkTheme
                                    ? AppColors.kDarkPrimarySwatch
                                    : AppColors.kLightPrimarySwatch,
                          ),
                        )
                      : Container(),
                ],
              );
            },
          ),
        ),
        // DataCell(Builder(
        //   builder: (context) {
        //     return Text(
        //       data[index].orders!.isNotEmpty
        //           ? "${data[index].orders![0].billingAddress!.city} \n${data[index].orders![0].billingAddress!.state}"
        //           : "",
        //     );
        //   },
        // )),
        // DataCell(Builder(
        //   builder: (context) {
        //     return Text("Yocto - NanoServer");
        //   },
        // )),
        // DataCell(Builder(
        //   builder: (context) {
        //     return Text(
        //       data[index].orders!.isNotEmpty
        //           ? data[index].orders![0].paymentProcessor!
        //           : "",
        //     );
        //   },
        // )),
        // DataCell(Builder(builder: (context) {
        //   return TextButton(
        //     style: TextButton.styleFrom(
        //         textStyle: AppTextStyles.textStyleBold14(context).copyWith(
        //       decoration: TextDecoration.underline,
        //     )),
        //     onPressed: () {

        //       log("OrderId: ${data[index].orders![0].orderId!}");
        //       data[index].orders!.isNotEmpty
        //           ? context.go(
        //               context.namedLocation(
        //                 AppRouteNames.orderDetails,
        //                 pathParameters: {
        //                   "orderId": data[index].orders![0].orderId!,
        //                   // "email": data[index].email!,
        //                   // "orderDetails": data[index];
        //                 },
        //                 // queryParameters: {
        //                 //   "email": data[index].email!,
        //                 // }
        //               ),
        //               extra: {
        //                   "email": data[index].email!,
        //                 })

        //           : () {};
        //     },
        //     child: const Text(
        //       "View Details",
        //     ),
        //   );
        // })),
        // DataCell(Builder(builder: (context) {
        //   return TextButton(
        //     style: TextButton.styleFrom(
        //         textStyle: AppTextStyles.textStyleBold14(context).copyWith(
        //       decoration: TextDecoration.underline,
        //     )),
        //     onPressed: () {
        //       Provider.of<MyTeamProvider>(context, listen: false).editOrderDialog(
        //         context,
        //         data[index].userId!,
        //         data[index].firstName!,
        //         data[index].email!,
        //         phoneController,
        //         phoneFocusNode,
        //         dropDownValue,
        //       );
        //       // context.go(
        //       //   context.namedLocation(
        //       //     AppRouteNames.merchantDetails,
        //       //     pathParameters: {
        //       //       "merchantId": data[index].id,
        //       //     },
        //       //   ),
        //       // );
        //     },
        //     // child: const Text(
        //     //   "View Details",
        //     // ),
        //     child: Text(
        //       "EDIT",
        //       style: AppTextStyles.textStyleBold14(context).copyWith(
        //           color: Provider.of<DarkThemeProvider>(context).darkTheme
        //               ? AppColors.kDarkPrimarySwatch
        //               : AppColors.kLightPrimarySwatch),
        //     ),
        //   );
        // })),
      ],
    );
  }

  @override
  // TODO: implement isRowCountApproximate
  bool get isRowCountApproximate => false;

  @override
  // TODO: implement rowCount
  int get rowCount => data.length;

  @override
  // TODO: implement selectedRowCount
  int get selectedRowCount => 0;

  Color getStatusColor(MerchantStatus status) {
    switch (status) {
      case MerchantStatus.ACTIVE:
        return AppColors.kGreen;
      case MerchantStatus.INVITED:
        return AppColors.kYellow;
      case MerchantStatus.STOPPED:
        return AppColors.kRed;
      default:
        return Colors.green;
    }
  }

  String getStatusText(MerchantStatus status) {
    switch (status) {
      case MerchantStatus.ACTIVE:
        return 'ACTIVE';
      case MerchantStatus.INVITED:
        return 'INVITED';
      case MerchantStatus.STOPPED:
        return 'STOPPED';
      default:
        return 'ACTIVE';
    }
  }

  Widget hideMerchantIcon(int index, bool hideMerchant, String companyId) {
    if (hideMerchant) {
      return IconButton(
        onPressed: () {
          clickOnHideMerchant(index, companyId);
        },
        icon: const Icon(
          Icons.visibility_off_outlined,
        ),
      );
    } else {
      return IconButton(
        onPressed: () {
          clickOnHideMerchant(index, companyId);
        },
        icon: const Icon(
          Icons.visibility_outlined,
        ),
      );
    }
  }

  void clickOnViewDetails(int index) {}
  void clickOnHideMerchant(int index, String companyId) async {
    // try {
    //   final response = await api.toggleHiddenTenant(companyId);
    //   data[index] = MerchantDetails.fromJson(response);
    //   print(data[index].hidden);
    //   Provider.of<ManageMerchantProvider>(context, listen: false)
    //       .merchantDetails = data;
    //   Provider.of<ManageMerchantProvider>(context, listen: false)
    //       .notifyListeners();
    // } catch (e) {
    //   print(e);
    // }
  }
}
