class PaginatedResult<T> {
  PaginatedResult({
    // required this.data,
    required this.total,
    required this.totalPages,
  });

  // create empty factory

  factory PaginatedResult.empty() => PaginatedResult(
        // data: [],
        total: 0,
        totalPages: 0,
      );

  factory PaginatedResult.fromJson(Map<String, dynamic> json) =>
      PaginatedResult(
        // data: json['data'],
        total: json['total'],
        totalPages: json['totalPages'],
      );
  // final List<T> data;
  final int total;
  final int totalPages;

  Map<String, dynamic> toJson() => {
        // 'data': data,
        'total': total,
        'totalPages': totalPages,
      };
}
