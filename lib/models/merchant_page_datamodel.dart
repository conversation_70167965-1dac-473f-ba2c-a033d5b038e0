import 'package:admin_portal/configs/configs_export.dart';
import 'package:admin_portal/models/support_list_model.dart';
import 'package:admin_portal/provider/provider_export.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

class MerchantData extends DataTableSource {
  MerchantData(this.data, this.context);
  // final List<MerchantUsers> data;
  final List<Customers> data;
  final BuildContext context;
  @override
  DataRow? getRow(int index) {
    // TODO: implement getRow
    return DataRow.byIndex(
      index: index,
      cells: [
        DataCell(
          Builder(
            builder: (context) {
              return Row(
                children: [
                  Container(
                    height: 30,
                    width: 30,
                    decoration: BoxDecoration(
                      color:
                          Provider.of<DarkThemeProvider>(context, listen: false)
                                  .darkTheme
                              ? AppColors.kDarkPrimarySwatch
                              : AppColors.kLightPrimarySwatch,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Center(
                      child: Text(
                        data[index].firstName!.isNotEmpty
                            ? data[index].firstName!.substring(0, 1)
                            : data[index].userId!.substring(0, 1),
                        style: GoogleFonts.lemon(
                          fontSize: 16,
                          fontWeight: FontWeight.w400,
                          color: AppColors.kWhite,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(
                    width: 10,
                  ),
                  Text(
                    data[index].userId!,
                    style: AppTextStyles.textStyleBold14(context),
                  ),
                ],
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              return SingleChildScrollView(
                child: Column(
                  children: [
                    Text(
                      data[index].nFTs!.isNotEmpty
                          ? returnNFT(data[index].nFTs!)
                          : '',
                      style: AppTextStyles.textStyleBold14(context).copyWith(
                        color: Provider.of<DarkThemeProvider>(context).darkTheme
                            ? AppColors.kDarkPrimarySwatch
                            : AppColors.kLightPrimarySwatch,
                      ),
                    ),
                  ],
                ),
              );
              // return Expanded(
              //   child: Text(
              //     data[index].nFTs!.isNotEmpty ? returnNFT(data[index].nFTs!) : "",
              //     style: AppTextStyles.textStyleBold14(context).copyWith(
              //         color: Provider.of<DarkThemeProvider>(context).darkTheme
              //             ? AppColors.kDarkPrimarySwatch
              //             : AppColors.kLightPrimarySwatch),
              //   ),
              // );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              return Text(
                data[index].orders!.isNotEmpty
                    ? returnServerItems(data[index].orders![0].servers!)
                    : '',
                style: AppTextStyles.textStyleBold14(context).copyWith(
                  color: Provider.of<DarkThemeProvider>(context).darkTheme
                      ? AppColors.kDarkPrimarySwatch
                      : AppColors.kLightPrimarySwatch,
                ),
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              return Text(
                data[index].orders!.isNotEmpty
                    ? returnOrderItems(data[index].orders!)
                    : '',
                style: AppTextStyles.textStyleBold14(context).copyWith(
                  color: Provider.of<DarkThemeProvider>(context).darkTheme
                      ? AppColors.kDarkPrimarySwatch
                      : AppColors.kLightPrimarySwatch,
                ),
              );
            },
          ),
        ),
        // DataCell(Builder(builder: (context) {
        //   return Row(
        //     children: [
        //       TextButton(
        //         onPressed: () {},
        //         child: Text(
        //           "EDIT",
        //           style: AppTextStyles.textStyleBold14(context).copyWith(
        //               color: Provider.of<DarkThemeProvider>(context).darkTheme
        //                   ? AppColors.kDarkPrimarySwatch
        //                   : AppColors.kLightPrimarySwatch),
        //         ),
        //       ),
        //       IconButton(
        //           onPressed: () {},
        //           icon: Icon(Iconsax.trash, color: AppColors.kRed))
        //     ],
        //   );
        // })),
        DataCell(
          Builder(
            builder: (context) {
              return SingleChildScrollView(
                child: Column(
                  children: [
                    Text(
                      data[index].orders!.isNotEmpty
                          ? 'Shipping Address: \n${data[index].orders![0].shippingAddress!.addressee!} \n${data[index].orders![0].shippingAddress!.line1!} \n${data[index].orders![0].shippingAddress!.city!}\n${data[index].orders![0].shippingAddress!.state} \n${data[index].orders![0].shippingAddress!.country!} \n${data[index].orders![0].shippingAddress!.postalCode!} \n\nBilling Address: \n${data[index].orders![0].billingAddress!.addressee!} \n${data[index].orders![0].billingAddress!.line1!} \n${data[index].orders![0].billingAddress!.city!}\n${data[index].orders![0].billingAddress!.state} \n${data[index].orders![0].billingAddress!.country!} \n${data[index].orders![0].billingAddress!.postalCode!}'
                          : '',
                      style: AppTextStyles.textStyleBold14(context),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  @override
  // TODO: implement isRowCountApproximate
  bool get isRowCountApproximate => false;

  @override
  // TODO: implement rowCount
  int get rowCount => data.length;

  @override
  // TODO: implement selectedRowCount
  int get selectedRowCount => 0;
}

String returnNFT(List<NFTs> items) {
  var itemNames = '';
  for (var i = 0; i < items.length; i++) {
    final tokenID = 'Token ID: ${items[i].nft!.tokenId!}';
    // String imageURL = items[i].imageUrl!.isNotEmpty
    //     ? " Image URL: ${items[i].imageUrl!}"
    //     : "";
    // String contractAddress = items[i].contractAddress!.isNotEmpty
    //     ? "   Contract Address: ${items[i].contractAddress!}"
    //     : "";
    final finalData = tokenID;
    itemNames = "$itemNames$finalData\n";
  }
  return itemNames;
}

String returnServerItems(List<Servers> list) {
  var purchasednames = '';
  for (var i = 0; i < list.length; i++) {
    purchasednames =
        '${purchasednames}ServerID: ${list[i].serverID}         LicenseLinked: ${list[i].licenseLinked} \n';
    // return purchasedItems[i].productName!;
  }
  return purchasednames;
}

String returnOrderItems(List<Orders> list) {
  var purchasednames = '';
  for (var i = 0; i < list.length; i++) {
    purchasednames =
        '${purchasednames}OrderID: ${list[i].orderId}         Status: ${list[i].status}         Comment: ${list[i].comment} \n';
    // return purchasedItems[i].productName!;
  }
  return purchasednames;
}
