import 'package:admin_portal/configs/configs_export.dart';
import 'package:admin_portal/models/order_list_model.dart';
import 'package:admin_portal/provider/provider_export.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

class OrderSub1Data extends DataTableSource {
  OrderSub1Data(this.data, this.context);
  final List<OrderCustomers> data;
  final BuildContext context;
  @override
  DataRow? getRow(int index) {
    // TODO: implement getRow
    return DataRow.byIndex(
      index: index,
      cells: [
        DataCell(
          Builder(
            builder: (context) {
              return Row(
                children: [
                  Container(
                    height: 30,
                    width: 30,
                    decoration: BoxDecoration(
                      color:
                          Provider.of<DarkThemeProvider>(context, listen: false)
                                  .darkTheme
                              ? AppColors.kDarkPrimarySwatch
                              : AppColors.kLightPrimarySwatch,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Center(
                      child: Text(
                        data[index].firstName!.substring(0, 1),
                        style: GoogleFonts.lemon(
                          fontSize: 16,
                          fontWeight: FontWeight.w400,
                          color: AppColors.kWhite,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(
                    width: 10,
                  ),
                  Text(
                    data[index].orders![0].orderId!,
                    style: AppTextStyles.textStyleBold14(context),
                  ),
                ],
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              return Text(
                data[index].orders![0].comment!,
                style: AppTextStyles.textStyleBold14(context),
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              return Text(
                '${data[index].orders![0].shippingAddress!.addressee!} \n${data[index].orders![0].shippingAddress!.line1!} \n${data[index].orders![0].shippingAddress!.city!}\n${data[index].orders![0].shippingAddress!.state} \n${data[index].orders![0].shippingAddress!.country!} \n${data[index].orders![0].shippingAddress!.postalCode!}',
                style: AppTextStyles.textStyleBold14(context),
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              return Text(
                '${data[index].orders![0].billingAddress!.addressee!} \n${data[index].orders![0].billingAddress!.line1!} \n${data[index].orders![0].billingAddress!.city!}\n${data[index].orders![0].billingAddress!.state} \n${data[index].orders![0].billingAddress!.country!} \n${data[index].orders![0].billingAddress!.postalCode!}',
                style: AppTextStyles.textStyleBold14(context),
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              return Text(
                data[index].orders![0].customerPaid!.string!,
                style: AppTextStyles.textStyleBold14(context),
              );
            },
          ),
        ),

        // DataCell(Builder(builder: (context) {
        //   return Text(data[index].role,
        //       style: AppTextStyles.textStyleBold14(context));
        // })),
        DataCell(
          Builder(
            builder: (context) {
              return Row(
                children: [
                  TextButton(
                    onPressed: () {},
                    child: Text(
                      returnPurchaseItems(
                        data[index].orders![0].purchasedItems!,
                      ),
                      // "View Details",
                      style: AppTextStyles.textStyleBold14(context).copyWith(
                        color: Provider.of<DarkThemeProvider>(context).darkTheme
                            ? AppColors.kDarkPrimarySwatch
                            : AppColors.kLightPrimarySwatch,
                      ),
                    ),
                  ),
                  // IconButton(
                  //     onPressed: () {},
                  //     icon: Icon(Iconsax.trash, color: AppColors.kRed))
                ],
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              return Row(
                children: [
                  TextButton(
                    onPressed: () {},
                    child: Text(
                      'Name: ${data[index].orders![0].stripeCard!.ownerName!}\nlast4: ${data[index].orders![0].stripeCard!.last4!}\nbrand: ${data[index].orders![0].stripeCard!.brand!}\nmonth: ${data[index].orders![0].stripeCard!.expires!.month!}\nyear: ${data[index].orders![0].stripeCard!.expires!.year}\n',
                      // "View Details",
                      style: AppTextStyles.textStyleBold14(context).copyWith(
                        color: Provider.of<DarkThemeProvider>(context).darkTheme
                            ? AppColors.kDarkPrimarySwatch
                            : AppColors.kLightPrimarySwatch,
                      ),
                    ),
                  ),
                  // IconButton(
                  //     onPressed: () {},
                  //     icon: Icon(Iconsax.trash, color: AppColors.kRed))
                ],
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              return Row(
                children: [
                  TextButton(
                    onPressed: () {},
                    child: Text(
                      returnServerItems(data[index].orders![0].servers!),
                      // "View Details",
                      style: AppTextStyles.textStyleBold14(context).copyWith(
                        color: Provider.of<DarkThemeProvider>(context).darkTheme
                            ? AppColors.kDarkPrimarySwatch
                            : AppColors.kLightPrimarySwatch,
                      ),
                    ),
                  ),
                  // IconButton(
                  //     onPressed: () {},
                  //     icon: Icon(Iconsax.trash, color: AppColors.kRed))
                ],
              );
            },
          ),
        ),
      ],
    );
  }

  @override
  // TODO: implement isRowCountApproximate
  bool get isRowCountApproximate => false;

  @override
  // TODO: implement rowCount
  int get rowCount => data.length;

  @override
  // TODO: implement selectedRowCount
  int get selectedRowCount => 0;
}

String returnServerItems(List<Servers> list) {
  var purchasednames = '';
  for (var i = 0; i < list.length; i++) {
    purchasednames =
        '${purchasednames}ServerID: ${list[i].serverID}         LicenseLinked: ${list[i].licenseLinked} \n';
    // return purchasedItems[i].productName!;
  }
  return purchasednames;
}

String returnPurchaseItems(List<PurchasedItems> purchasedItems) {
  var purchasednames = '';
  for (var i = 0; i < purchasedItems.length; i++) {
    purchasednames = "$purchasednames${purchasedItems[i].productName} \n";
    // return purchasedItems[i].productName!;
  }
  return purchasednames;
}
