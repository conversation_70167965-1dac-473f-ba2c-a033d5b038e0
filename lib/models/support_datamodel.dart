import 'dart:developer';

import 'package:admin_portal/configs/configs_export.dart';
import 'package:admin_portal/models/models_export.dart';
import 'package:admin_portal/models/support_list_model.dart';
import 'package:admin_portal/provider/provider_export.dart';
import 'package:admin_portal/routes/route_names.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:iconsax/iconsax.dart';
import 'package:provider/provider.dart';

class SupportData extends DataTableSource {
  SupportData(this.total,this.data);
  final List<Customers> data;
  TextEditingController phoneController = TextEditingController();
  FocusNode phoneFocusNode = FocusNode();
  String dropDownValue = 'PROCESSING';
  int total ;
  void updateData(List<Customers> newData, int newTotal) {
    data.clear();
    data.addAll(newData);
    total = newTotal;
    notifyListeners();
  }

  @override
  DataRow? getRow(int index) {
    if (data.isEmpty) {
      return null;
    }
    // Handle index out of range by calculating the correct local index
    if (index >= data.length) {
      // Calculate the local index within the current page
      int localIndex = index % data.length;

      // Ensure the local index is within bounds
      if (localIndex >= data.length) {
        return null;
      }

      index = localIndex;
    }

    return DataRow.byIndex(
      index: index,
      cells: [
        DataCell(
          Builder(
            builder: (context) {
              return Checkbox(
                onChanged: (value) {
                  Provider.of<ManageMerchantProvider>(context, listen: false)
                      .setItemChecked(value, index);
                },
                value: data[index].isCheck,
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              return Row(
                children: [
                  SelectableText(
                    data[index].userId!.isNotEmpty ? data[index].userId! : '',
                    style: AppTextStyles.textStyleBold14(context).copyWith(
                      color: Provider.of<DarkThemeProvider>(context).darkTheme
                          ? AppColors.kDarkPrimarySwatch
                          : AppColors.kLightPrimarySwatch,
                    ),
                  ),
                  data[index].userId!.isNotEmpty
                      ? IconButton(
                          onPressed: () {
                            log('UserID: ${data[index].userId!}');
                            data[index].userId!.isNotEmpty
                                ? context.go(
                                    context.namedLocation(
                                      AppRouteNames.merchantDetails,
                                      pathParameters: {
                                        'merchantId': data[index].userId!,
                                      },
                                    ),
                                    extra: {
                                      'email': data[index].email!,
                                    },
                                  )
                                : () {};
                          },
                          icon: Icon(
                            Icons.article_outlined,
                            color: Provider.of<DarkThemeProvider>(context)
                                    .darkTheme
                                ? AppColors.kDarkPrimarySwatch
                                : AppColors.kLightPrimarySwatch,
                          ),
                        )
                      : Container(),
                ],
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              return Row(
                children: [
                  SelectableText(
                    '${data[index].firstName!} ${data[index].lastName!}',
                    style: AppTextStyles.textStyleBold14(context).copyWith(
                      color: Provider.of<DarkThemeProvider>(context).darkTheme
                          ? AppColors.kDarkPrimarySwatch
                          : AppColors.kLightPrimarySwatch,
                    ),
                  ),
                  Visibility(
                    visible: false,
                    child: IconButton(
                      onPressed: () {
                        phoneController.text = data[index].phone!.number!;
                        Provider.of<MyTeamProvider>(context, listen: false)
                            .editUserDialog(
                          context,
                          data[index].userId!,
                          data[index].firstName!,
                          data[index].lastName!,
                          data[index].email!,
                          data[index].phone!.countryCode!,
                          data[index].countryCodeString!,
                          phoneController,
                          phoneFocusNode,
                          dropDownValue,
                        );
                      },
                      icon: Icon(
                        Iconsax.edit,
                        color: Provider.of<DarkThemeProvider>(context).darkTheme
                            ? AppColors.kDarkPrimarySwatch
                            : AppColors.kLightPrimarySwatch,
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              return SelectableText(
                data[index].phone!.countryCode! + data[index].phone!.number!,
                style: AppTextStyles.textStyleBold14(context),
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              return SelectableText(
                data[index].email!,
                style: AppTextStyles.textStyleBold14(context),
              );
            },
          ),
        ),
        DataCell(
          Builder(
            builder: (context) {
              return SelectableText(
                data[index].cryptoWallet!.address!,
                style: AppTextStyles.textStyleBold14(context),
              );
            },
          ),
        ),
      ],
    );
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => total;

  @override
  int get selectedRowCount => 0;

  Color getStatusColor(MerchantStatus status) {
    switch (status) {
      case MerchantStatus.ACTIVE:
        return AppColors.kGreen;
      case MerchantStatus.INVITED:
        return AppColors.kYellow;
      case MerchantStatus.STOPPED:
        return AppColors.kRed;
      default:
        return Colors.green;
    }
  }

  String getStatusText(MerchantStatus status) {
    switch (status) {
      case MerchantStatus.ACTIVE:
        return 'ACTIVE';
      case MerchantStatus.INVITED:
        return 'INVITED';
      case MerchantStatus.STOPPED:
        return 'STOPPED';
      default:
        return 'ACTIVE';
    }
  }

  Widget hideMerchantIcon(int index, bool hideMerchant, String companyId) {
    if (hideMerchant) {
      return IconButton(
        onPressed: () {
          clickOnHideMerchant(index, companyId);
        },
        icon: const Icon(
          Icons.visibility_off_outlined,
        ),
      );
    } else {
      return IconButton(
        onPressed: () {
          clickOnHideMerchant(index, companyId);
        },
        icon: const Icon(
          Icons.visibility_outlined,
        ),
      );
    }
  }

  void clickOnViewDetails(int index) {}
  void clickOnHideMerchant(int index, String companyId) async {}
}
