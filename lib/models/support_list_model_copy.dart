import 'dart:convert';

GetSupportList supportListModelResponseFromJson(String str) =>
    GetSupportList.fromJson(json.decode(str));

String supportListModelResponseToJson(GetSupportList data) =>
    json.encode(data.toJson());

class GetSupportList {
  GetSupportList({
    this.limit,
    this.page,
    this.total,
    this.totalPages,
    this.customers,
  });

  GetSupportList.fromJson(Map<String, dynamic> json) {
    limit = json['limit'];
    page = json['page'];
    total = json['total'];
    totalPages = json['totalPages'];
    if (json['customers'] != null) {
      customers = <Customers>[];
      json['customers'].forEach((v) {
        customers!.add(Customers.fromJson(v));
      });
    }
  }
  String? limit;
  String? page;
  int? total;
  int? totalPages;
  List<Customers>? customers;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['limit'] = limit;
    data['page'] = page;
    data['total'] = total;
    data['totalPages'] = totalPages;
    if (customers != null) {
      data['customers'] = customers!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Customers {
  Customers({
    this.firstName,
    this.lastName,
    this.userId,
    this.phone,
    this.countryCode,
    this.countryCodeString,
    this.email,
    this.nFTs,
    this.servers,
    this.orders,
  });

  Customers.fromJson(Map<String, dynamic> json) {
    firstName = json['firstName'] ?? '';
    lastName = json['lastName'] ?? '';
    userId = json['userId'];
    phone = json['phone'] != null
        ? Phone.fromJson(json['phone'])
        : Phone(countryCode: '', number: '');
    countryCode = json['countryCode'] ?? '';
    countryCodeString = json['countryCodeString'] ?? '';
    email = json['email'] ?? '';
    if (json['NFTs'] != null) {
      nFTs = <NFTs>[];
      json['NFTs'].forEach((v) {
        // print('NFTS data: $v');
        if (v != null) nFTs!.add(NFTs.fromJson(v));
      });
    }
    if (json['servers'] != null) {
      servers = <Servers>[];
      json['servers'].forEach((v) {
        if (v != null) servers!.add(Servers.fromJson(v));
      });
    }
    if (json['orders'] != null) {
      orders = <Orders>[];
      json['orders'].forEach((v) {
        if (v != null) orders!.add(Orders.fromJson(v));
      });
    }
  }
  String? firstName;
  String? lastName;
  String? userId;
  Phone? phone;
  String? countryCode;
  String? countryCodeString;
  String? email;
  List<NFTs>? nFTs;
  List<Servers>? servers;
  List<Orders>? orders;
  bool isCheck = false;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['firstName'] = firstName;
    data['lastName'] = lastName;
    data['userId'] = userId;
    if (phone != null) {
      data['phone'] = phone!.toJson();
    }
    data['email'] = email;
    if (nFTs != null) {
      data['NFTs'] = nFTs!.map((v) => v.toJson()).toList();
    }
    if (servers != null) {
      data['servers'] = servers!.map((v) => v.toJson()).toList();
    }
    if (orders != null) {
      data['orders'] = orders!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Phone {
  Phone({this.countryCode, this.number});

  Phone.fromJson(Map<String, dynamic> json) {
    countryCode = json['countryCode'] ?? '';
    number = json['number'] ?? '';
  }
  String? countryCode;
  String? number;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['countryCode'] = countryCode;
    data['number'] = number;
    return data;
  }
}

class NFTs {
  NFTs({this.tokenId, this.imageUrl, this.contractAddress});

  NFTs.fromJson(Map<String, dynamic> json) {
    tokenId = json['tokenId'] ?? '';
    imageUrl = json['imageUrl'] ?? '';
    error = json['error'] ?? '';
    contractAddress = json['contractAddress'] ?? '';
  }
  dynamic tokenId;
  String? contractAddress;
  String? imageUrl;
  String? error;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['tokenId'] = tokenId;
    data['imageUrl'] = imageUrl;
    data['error'] = error;
    data['contractAddress'] = contractAddress;
    return data;
  }
}

class Servers {
  Servers({
    this.name,
    this.serverID,
    this.userId,
    this.deviceStatus,
    this.purchaseDate,
    this.licenseId,
    this.licenseLinked,
    this.cpuScore,
    this.cloudScore,
    this.gpuScore,
    this.ramScore,
    this.diskScore,
    this.normalizedGpuScore,
    this.normalizedCpuScore,
    this.normalizedRamScore,
    this.normalizedDiskScore,
    this.normalizedISPScore,
    this.normalizedDeviceScore,
    this.totalEarning,
    this.totalEarningsAfterRecentOnline,
    this.macAddress,
    this.model,
    this.manufacturer,
    this.warrantyExpiration,
    this.location,
    this.software,
    this.phone,
  });

  Servers.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    serverID = json['serverID'];
    userId = json['userId'];
    deviceStatus = json['deviceStatus'];
    purchaseDate = json['purchaseDate'];
    licenseId = json['licenseId'];
    licenseLinked = json['licenseLinked'];
    cpuScore = json['cpuScore'];
    cloudScore = json['cloudScore'];
    gpuScore = json['gpuScore'];
    ramScore = json['ramScore'];
    diskScore = json['diskScore'];
    normalizedGpuScore = json['normalizedGpuScore'];
    normalizedCpuScore = json['normalizedCpuScore'];
    normalizedRamScore = json['normalizedRamScore'];
    normalizedDiskScore = json['normalizedDiskScore'];
    normalizedISPScore = json['normalizedISPScore'];
    normalizedDeviceScore = json['normalizedDeviceScore'];
    totalEarning = json['totalEarning'];
    totalEarningsAfterRecentOnline = json['totalEarningsAfterRecentOnline'];
    macAddress = json['macAddress'];
    model = json['model'];
    manufacturer = json['manufacturer'];
    warrantyExpiration = json['warrantyExpiration'];
    location = json['location'];
    if (json['software'] != null) {
      software = <Software>[];
      json['software'].forEach((v) {
        software!.add(Software.fromJson(v));
      });
    }
    phone = json['phone'] != null ? Phone.fromJson(json['phone']) : null;
  }
  String? name;
  String? serverID;
  String? userId;
  bool? deviceStatus;
  String? purchaseDate;
  String? licenseId;
  bool? licenseLinked;
  int? cpuScore;
  double? cloudScore;
  int? gpuScore;
  double? ramScore;
  int? diskScore;
  double? normalizedGpuScore;
  double? normalizedCpuScore;
  double? normalizedRamScore;
  double? normalizedDiskScore;
  int? normalizedISPScore;
  double? normalizedDeviceScore;
  double? totalEarning;
  double? totalEarningsAfterRecentOnline;
  String? macAddress;
  String? model;
  String? manufacturer;
  String? warrantyExpiration;
  String? location;
  List<Software>? software;
  Phone? phone;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['name'] = name;
    data['serverID'] = serverID;
    data['userId'] = userId;
    data['deviceStatus'] = deviceStatus;
    data['purchaseDate'] = purchaseDate;
    data['licenseId'] = licenseId;
    data['licenseLinked'] = licenseLinked;
    data['cpuScore'] = cpuScore;
    data['cloudScore'] = cloudScore;
    data['gpuScore'] = gpuScore;
    data['ramScore'] = ramScore;
    data['diskScore'] = diskScore;
    data['normalizedGpuScore'] = normalizedGpuScore;
    data['normalizedCpuScore'] = normalizedCpuScore;
    data['normalizedRamScore'] = normalizedRamScore;
    data['normalizedDiskScore'] = normalizedDiskScore;
    data['normalizedISPScore'] = normalizedISPScore;
    data['normalizedDeviceScore'] = normalizedDeviceScore;
    data['totalEarning'] = totalEarning;
    data['totalEarningsAfterRecentOnline'] = totalEarningsAfterRecentOnline;
    data['macAddress'] = macAddress;
    data['model'] = model;
    data['manufacturer'] = manufacturer;
    data['warrantyExpiration'] = warrantyExpiration;
    data['location'] = location;
    if (software != null) {
      data['software'] = software!.map((v) => v.toJson()).toList();
    }
    if (phone != null) {
      data['phone'] = phone!.toJson();
    }
    return data;
  }
}

class Software {
  Software({
    this.name,
    this.version,
    this.licenseType,
    this.expirationDate,
    this.licenseKey,
  });

  Software.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    version = json['version'];
    licenseType = json['licenseType'];
    expirationDate = json['expirationDate'];
    licenseKey = json['licenseKey'];
  }
  String? name;
  String? version;
  String? licenseType;
  String? expirationDate;
  String? licenseKey;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['name'] = name;
    data['version'] = version;
    data['licenseType'] = licenseType;
    data['expirationDate'] = expirationDate;
    data['licenseKey'] = licenseKey;
    return data;
  }
}

class Orders {
  Orders({
    this.sId,
    this.trigger,
    this.orderId,
    this.status,
    this.comment,
    this.orderComment,
    this.acceptedOn,
    this.disputedOn,
    this.disputeUpdatedOn,
    this.disputeLastStatus,
    this.fulfilledOn,
    this.refundedOn,
    this.customerPaid,
    this.netAmount,
    this.applicationFee,
    this.shippingProvider,
    this.shippingTracking,
    this.shippingTrackingURL,
    this.customerInfo,
    this.allAddresses,
    this.shippingAddress,
    this.billingAddress,
    this.purchasedItems,
    this.purchasedItemsCount,
    this.totals,
    this.customData,
    this.paypalDetails,
    this.stripeCard,
    this.stripeDetails,
    this.paymentProcessor,
    this.hasDownloads,
    this.downloadFiles,
    this.metadata,
    this.isCustomerDeleted,
    this.isShippingRequired,
    this.createdAt,
    this.updatedAt,
    this.servers,
  });

  Orders.fromJson(Map<String, dynamic> json) {
    sId = json['_id'];
    trigger = json['trigger'];
    orderId = json['orderId'];
    status = json['status'];
    comment = json['comment'];
    orderComment = json['orderComment'];
    acceptedOn = json['acceptedOn'];
    disputedOn = json['disputedOn'];
    disputeUpdatedOn = json['disputeUpdatedOn'];
    disputeLastStatus = json['disputeLastStatus'];
    fulfilledOn = json['fulfilledOn'];
    refundedOn = json['refundedOn'];
    customerPaid = json['customerPaid'] != null
        ? CustomerPaid.fromJson(json['customerPaid'])
        : null;
    netAmount = json['netAmount'] != null
        ? CustomerPaid.fromJson(json['netAmount'])
        : null;
    applicationFee = json['applicationFee'] != null
        ? ApplicationFee.fromJson(json['applicationFee'])
        : null;
    shippingProvider = json['shippingProvider'];
    shippingTracking = json['shippingTracking'];
    shippingTrackingURL = json['shippingTrackingURL'];
    customerInfo = json['customerInfo'] != null
        ? CustomerInfo.fromJson(json['customerInfo'])
        : null;
    if (json['allAddresses'] != null) {
      allAddresses = <AllAddresses>[];
      json['allAddresses'].forEach((v) {
        allAddresses!.add(AllAddresses.fromJson(v));
      });
    }
    shippingAddress = json['shippingAddress'] != null
        ? AllAddresses.fromJson(json['shippingAddress'])
        : null;
    billingAddress = json['billingAddress'] != null
        ? AllAddresses.fromJson(json['billingAddress'])
        : null;
    if (json['purchasedItems'] != null) {
      purchasedItems = <PurchasedItems>[];
      json['purchasedItems'].forEach((v) {
        purchasedItems!.add(PurchasedItems.fromJson(v));
      });
    }
    purchasedItemsCount = json['purchasedItemsCount'];
    totals = json['totals'] != null ? Totals.fromJson(json['totals']) : null;
    if (json['customData'] != null) {
      customData = <CustomData>[];
      json['customData'].forEach((v) {
        customData!.add(CustomData.fromJson(v));
      });
    }
    paypalDetails = json['paypalDetails'];
    stripeCard = json['stripeCard'] != null
        ? StripeCard.fromJson(json['stripeCard'])
        : null;
    stripeDetails = json['stripeDetails'] != null
        ? StripeDetails.fromJson(json['stripeDetails'])
        : null;
    paymentProcessor = json['paymentProcessor'];
    hasDownloads = json['hasDownloads'];
    if (json['downloadFiles'] != null) {
      downloadFiles = [];
      json['downloadFiles'].forEach((v) {
        // downloadFiles!.add(Null.fromJson(v));
      });
    }
    metadata =
        json['metadata'] != null ? Metadata.fromJson(json['metadata']) : null;
    isCustomerDeleted = json['isCustomerDeleted'];
    isShippingRequired = json['isShippingRequired'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    if (json['servers'] != null) {
      servers = <Servers>[];
      json['servers'].forEach((v) {
        if (v != null) servers!.add(Servers.fromJson(v));
      });
    }
  }
  String? sId;
  String? trigger;
  String? orderId;
  String? status;
  String? comment;
  String? orderComment;
  String? acceptedOn;
  dynamic disputedOn;
  dynamic disputeUpdatedOn;
  dynamic disputeLastStatus;
  dynamic fulfilledOn;
  dynamic refundedOn;
  CustomerPaid? customerPaid;
  CustomerPaid? netAmount;
  ApplicationFee? applicationFee;
  dynamic shippingProvider;
  dynamic shippingTracking;
  dynamic shippingTrackingURL;
  CustomerInfo? customerInfo;
  List<AllAddresses>? allAddresses;
  AllAddresses? shippingAddress;
  AllAddresses? billingAddress;
  List<PurchasedItems>? purchasedItems;
  int? purchasedItemsCount;
  Totals? totals;
  List<CustomData>? customData;
  dynamic paypalDetails;
  StripeCard? stripeCard;
  StripeDetails? stripeDetails;
  String? paymentProcessor;
  bool? hasDownloads;
  // List<Null>? downloadFiles;
  List? downloadFiles;
  Metadata? metadata;
  bool? isCustomerDeleted;
  bool? isShippingRequired;
  String? createdAt;
  String? updatedAt;
  List<Servers>? servers;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['_id'] = sId;
    data['trigger'] = trigger;
    data['orderId'] = orderId;
    data['status'] = status;
    data['comment'] = comment;
    data['orderComment'] = orderComment;
    data['acceptedOn'] = acceptedOn;
    data['disputedOn'] = disputedOn;
    data['disputeUpdatedOn'] = disputeUpdatedOn;
    data['disputeLastStatus'] = disputeLastStatus;
    data['fulfilledOn'] = fulfilledOn;
    data['refundedOn'] = refundedOn;
    if (customerPaid != null) {
      data['customerPaid'] = customerPaid!.toJson();
    }
    if (netAmount != null) {
      data['netAmount'] = netAmount!.toJson();
    }
    if (applicationFee != null) {
      data['applicationFee'] = applicationFee!.toJson();
    }
    data['shippingProvider'] = shippingProvider;
    data['shippingTracking'] = shippingTracking;
    data['shippingTrackingURL'] = shippingTrackingURL;
    if (customerInfo != null) {
      data['customerInfo'] = customerInfo!.toJson();
    }
    if (allAddresses != null) {
      data['allAddresses'] = allAddresses!.map((v) => v.toJson()).toList();
    }
    if (shippingAddress != null) {
      data['shippingAddress'] = shippingAddress!.toJson();
    }
    if (billingAddress != null) {
      data['billingAddress'] = billingAddress!.toJson();
    }
    if (purchasedItems != null) {
      data['purchasedItems'] = purchasedItems!.map((v) => v.toJson()).toList();
    }
    data['purchasedItemsCount'] = purchasedItemsCount;
    if (totals != null) {
      data['totals'] = totals!.toJson();
    }
    if (customData != null) {
      data['customData'] = customData!.map((v) => v.toJson()).toList();
    }
    data['paypalDetails'] = paypalDetails;
    if (stripeCard != null) {
      data['stripeCard'] = stripeCard!.toJson();
    }
    if (stripeDetails != null) {
      data['stripeDetails'] = stripeDetails!.toJson();
    }
    data['paymentProcessor'] = paymentProcessor;
    data['hasDownloads'] = hasDownloads;
    if (downloadFiles != null) {
      data['downloadFiles'] = downloadFiles!.map((v) => v.toJson()).toList();
    }
    if (metadata != null) {
      data['metadata'] = metadata!.toJson();
    }
    data['isCustomerDeleted'] = isCustomerDeleted;
    data['isShippingRequired'] = isShippingRequired;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    return data;
  }
}

class CustomerPaid {
  CustomerPaid({this.unit, this.value, this.string});

  CustomerPaid.fromJson(Map<String, dynamic> json) {
    unit = json['unit'];
    value = json['value'];
    string = json['string'];
  }
  String? unit;
  int? value;
  String? string;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['unit'] = unit;
    data['value'] = value;
    data['string'] = string;
    return data;
  }
}

class ApplicationFee {
  ApplicationFee({this.value, this.unit});

  ApplicationFee.fromJson(Map<String, dynamic> json) {
    value = json['value'];
    unit = json['unit'];
  }
  int? value;
  String? unit;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['value'] = value;
    data['unit'] = unit;
    return data;
  }
}

class CustomerInfo {
  CustomerInfo({this.fullName, this.email});

  CustomerInfo.fromJson(Map<String, dynamic> json) {
    fullName = json['fullName'];
    email = json['email'];
  }
  String? fullName;
  String? email;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['fullName'] = fullName;
    data['email'] = email;
    return data;
  }
}

class AllAddresses {
  AllAddresses({
    this.type,
    this.addressee,
    this.line1,
    this.line2,
    this.city,
    this.state,
    this.country,
    this.postalCode,
  });

  AllAddresses.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    addressee = json['addressee'];
    line1 = json['line1'];
    line2 = json['line2'];
    city = json['city'];
    state = json['state'];
    country = json['country'];
    postalCode = json['postalCode'];
  }
  String? type;
  String? addressee;
  String? line1;
  dynamic line2;
  String? city;
  String? state;
  String? country;
  String? postalCode;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['type'] = type;
    data['addressee'] = addressee;
    data['line1'] = line1;
    data['line2'] = line2;
    data['city'] = city;
    data['state'] = state;
    data['country'] = country;
    data['postalCode'] = postalCode;
    return data;
  }
}

class PurchasedItems {
  PurchasedItems({
    this.count,
    this.rowTotal,
    this.productId,
    this.productName,
    this.productSlug,
    this.variantId,
    this.variantName,
    this.variantSlug,
    this.variantSKU,
    this.variantImage,
    this.variantPrice,
    this.weight,
    this.height,
    this.width,
    this.length,
  });

  PurchasedItems.fromJson(Map<String, dynamic> json) {
    count = json['count'];
    rowTotal = json['rowTotal'] != null
        ? CustomerPaid.fromJson(json['rowTotal'])
        : null;
    productId = json['productId'];
    productName = json['productName'];
    productSlug = json['productSlug'];
    variantId = json['variantId'];
    variantName = json['variantName'];
    variantSlug = json['variantSlug'];
    variantSKU = json['variantSKU'];
    variantImage = json['variantImage'] != null
        ? VariantImage.fromJson(json['variantImage'])
        : null;
    variantPrice = json['variantPrice'] != null
        ? CustomerPaid.fromJson(json['variantPrice'])
        : null;
    weight = json['weight'];
    height = json['height'];
    width = json['width'];
    length = json['length'];
  }
  int? count;
  CustomerPaid? rowTotal;
  String? productId;
  String? productName;
  String? productSlug;
  String? variantId;
  String? variantName;
  String? variantSlug;
  String? variantSKU;
  VariantImage? variantImage;
  CustomerPaid? variantPrice;
  int? weight;
  int? height;
  double? width;
  double? length;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['count'] = count;
    if (rowTotal != null) {
      data['rowTotal'] = rowTotal!.toJson();
    }
    data['productId'] = productId;
    data['productName'] = productName;
    data['productSlug'] = productSlug;
    data['variantId'] = variantId;
    data['variantName'] = variantName;
    data['variantSlug'] = variantSlug;
    data['variantSKU'] = variantSKU;
    if (variantImage != null) {
      data['variantImage'] = variantImage!.toJson();
    }
    if (variantPrice != null) {
      data['variantPrice'] = variantPrice!.toJson();
    }
    data['weight'] = weight;
    data['height'] = height;
    data['width'] = width;
    data['length'] = length;
    return data;
  }
}

class VariantImage {
  VariantImage({this.fileId, this.url, this.alt, this.file});

  VariantImage.fromJson(Map<String, dynamic> json) {
    fileId = json['fileId'];
    url = json['url'];
    alt = json['alt'];
    file = json['file'];
  }
  String? fileId;
  String? url;
  dynamic alt;
  dynamic file;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['fileId'] = fileId;
    data['url'] = url;
    data['alt'] = alt;
    data['file'] = file;
    return data;
  }
}

class Totals {
  Totals({this.subtotal, this.extras, this.total});

  Totals.fromJson(Map<String, dynamic> json) {
    subtotal = json['subtotal'] != null
        ? CustomerPaid.fromJson(json['subtotal'])
        : null;
    if (json['extras'] != null) {
      extras = <Extras>[];
      json['extras'].forEach((v) {
        extras!.add(Extras.fromJson(v));
      });
    }
    total = json['total'] != null ? CustomerPaid.fromJson(json['total']) : null;
  }
  CustomerPaid? subtotal;
  List<Extras>? extras;
  CustomerPaid? total;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (subtotal != null) {
      data['subtotal'] = subtotal!.toJson();
    }
    if (extras != null) {
      data['extras'] = extras!.map((v) => v.toJson()).toList();
    }
    if (total != null) {
      data['total'] = total!.toJson();
    }
    return data;
  }
}

class Extras {
  Extras({this.type, this.name, this.description, this.price});

  Extras.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    name = json['name'];
    description = json['description'];
    price = json['price'] != null ? CustomerPaid.fromJson(json['price']) : null;
  }
  String? type;
  String? name;
  String? description;
  CustomerPaid? price;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['type'] = type;
    data['name'] = name;
    data['description'] = description;
    if (price != null) {
      data['price'] = price!.toJson();
    }
    return data;
  }
}

class CustomData {
  CustomData({this.name, this.textInput});

  CustomData.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    textInput = json['textInput'];
  }
  String? name;
  String? textInput;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['name'] = name;
    data['textInput'] = textInput;
    return data;
  }
}

class StripeCard {
  StripeCard({this.last4, this.brand, this.ownerName, this.expires});

  StripeCard.fromJson(Map<String, dynamic> json) {
    last4 = json['last4'];
    brand = json['brand'];
    ownerName = json['ownerName'];
    expires =
        json['expires'] != null ? Expires.fromJson(json['expires']) : null;
  }
  String? last4;
  String? brand;
  String? ownerName;
  Expires? expires;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['last4'] = last4;
    data['brand'] = brand;
    data['ownerName'] = ownerName;
    if (expires != null) {
      data['expires'] = expires!.toJson();
    }
    return data;
  }
}

class Expires {
  Expires({this.month, this.year});

  Expires.fromJson(Map<String, dynamic> json) {
    month = json['month'];
    year = json['year'];
  }
  int? month;
  int? year;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['month'] = month;
    data['year'] = year;
    return data;
  }
}

class StripeDetails {
  StripeDetails({
    this.customerId,
    this.paymentMethod,
    this.chargeId,
    this.disputeId,
    this.paymentIntentId,
    this.subscriptionId,
    this.refundId,
    this.refundReason,
  });

  StripeDetails.fromJson(Map<String, dynamic> json) {
    customerId = json['customerId'];
    paymentMethod = json['paymentMethod'];
    chargeId = json['chargeId'];
    disputeId = json['disputeId'];
    paymentIntentId = json['paymentIntentId'];
    subscriptionId = json['subscriptionId'];
    refundId = json['refundId'];
    refundReason = json['refundReason'];
  }
  String? customerId;
  String? paymentMethod;
  String? chargeId;
  dynamic disputeId;
  String? paymentIntentId;
  dynamic subscriptionId;
  dynamic refundId;
  dynamic refundReason;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['customerId'] = customerId;
    data['paymentMethod'] = paymentMethod;
    data['chargeId'] = chargeId;
    data['disputeId'] = disputeId;
    data['paymentIntentId'] = paymentIntentId;
    data['subscriptionId'] = subscriptionId;
    data['refundId'] = refundId;
    data['refundReason'] = refundReason;
    return data;
  }
}

class Metadata {
  Metadata({this.isBuyNow});

  Metadata.fromJson(Map<String, dynamic> json) {
    isBuyNow = json['isBuyNow'];
  }
  bool? isBuyNow;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['isBuyNow'] = isBuyNow;
    return data;
  }
}
