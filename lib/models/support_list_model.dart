import 'dart:convert';

GetSupportList supportListModelResponseFromJson(String str) =>
    GetSupportList.fromJson(json.decode(str));

String supportListModelResponseToJson(GetSupportList data) =>
    json.encode(data.toJson());

class GetSupportList {
  GetSupportList({
    this.limit,
    this.page,
    this.total,
    this.totalPages,
    this.customers,
  });

  GetSupportList.fromJson(Map<String, dynamic> json) {
    limit = json['limit'];
    page = json['page'];
    total = json['total'];
    totalPages = json['totalPages'];
    if (json['customers'] != null) {
      customers = <Customers>[];
      json['customers'].forEach((v) {
        customers!.add(Customers.fromJson(v));
      });
    }
  }
  dynamic limit;
  dynamic page;
  int? total;
  int? totalPages;
  List<Customers>? customers;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['limit'] = limit;
    data['page'] = page;
    data['total'] = total;
    data['totalPages'] = totalPages;
    if (customers != null) {
      data['customers'] = customers!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Customers {
  Customers({
    this.firstName,
    this.lastName,
    this.userId,
    this.phone,
    this.countryCode,
    this.countryCodeString,
    this.email,
    this.nFTs,
    // this.servers,
    this.orders,
    this.dailyEarnings,
    this.servers,
    this.issuanceLicenseDetails,
    this.refferalUsers,
    this.refferalStats,
    this.cryptoWallet,
  });

  Customers.fromJson(Map<String, dynamic> json) {
    firstName = json['firstName'] ?? '';
    lastName = json['lastName'] ?? '';
    userId = json['userId'];
    phone = json['phone'] != null
        ? Phone.fromJson(json['phone'])
        : Phone(countryCode: '', number: '');
    countryCode = json['countryCode'] ?? '';
    countryCodeString = json['countryCodeString'] ?? '';
    email = json['email'] ?? '';
    if (json['NFTs'] != null) {
      nFTs = <NFTs>[];
      json['NFTs'].forEach((v) {
        // print('NFTS data: $v');
        if (v != null) nFTs!.add(NFTs.fromJson(v));
      });
    }
    // if (json['servers'] != null) {
    //   servers = <Servers>[];
    //   json['servers'].forEach((v) {
    //     if (v != null) servers!.add(new Servers.fromJson(v));
    //   });
    // }
    if (json['orders'] != null) {
      orders = <Orders>[];
      json['orders'].forEach((v) {
        if (v != null) orders!.add(Orders.fromJson(v));
      });
    }
    if (json['dailyEarnings'] != null) {
      dailyEarnings = <DailyEarnings>[];
      json['dailyEarnings'].forEach((v) {
        dailyEarnings!.add(DailyEarnings.fromJson(v));
      });
    }
    if (json['servers'] != null) {
      servers = <Servers>[];
      json['servers'].forEach((v) {
        if (v != null) servers!.add(Servers.fromJson(v));
      });
    }
    if (json['issuanceLicenseDetails'] != null) {
      issuanceLicenseDetails = <IssuanceLicenseDetails>[];
      json['issuanceLicenseDetails'].forEach((v) {
        if (v != null) {
          issuanceLicenseDetails!.add(IssuanceLicenseDetails.fromJson(v));
        }
      });
    }
    if (json['refferalUsers'] != null) {
      refferalUsers = <RefferalUsers>[];
      json['refferalUsers'].forEach((v) {
        if (v != null) refferalUsers!.add(RefferalUsers.fromJson(v));
      });
    }
    if (json['refferalStats'] != null) {
      refferalStats = <RefferalStats>[];
      json['refferalStats'].forEach((v) {
        if (v != null) refferalStats!.add(RefferalStats.fromJson(v));
      });
    }
    cryptoWallet = json['cryptoWallet'] != null
        ? CryptoWallet.fromJson(json['cryptoWallet'])
        : CryptoWallet(address: '');
  }
  String? firstName;
  String? lastName;
  String? userId;
  Phone? phone;
  String? countryCode;
  String? countryCodeString;
  String? email;
  List<NFTs>? nFTs;
  List<Orders>? orders;
  List<DailyEarnings>? dailyEarnings;
  bool isCheck = false;
  List<Servers>? servers;
  List<IssuanceLicenseDetails>? issuanceLicenseDetails;
  List<RefferalUsers>? refferalUsers;
  List<RefferalStats>? refferalStats = [];
  CryptoWallet? cryptoWallet;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['firstName'] = firstName;
    data['lastName'] = lastName;
    data['userId'] = userId;
    if (phone != null) {
      data['phone'] = phone!.toJson();
    }
    data['email'] = email;
    if (nFTs != null) {
      data['NFTs'] = nFTs!.map((v) => v.toJson()).toList();
    }
    // if (this.servers != null) {
    //   data['servers'] = this.servers!.map((v) => v.toJson()).toList();
    // }
    if (orders != null) {
      data['orders'] = orders!.map((v) => v.toJson()).toList();
    }
    if (dailyEarnings != null) {
      data['dailyEarnings'] = dailyEarnings!.map((v) => v.toJson()).toList();
    }
    if (servers != null) {
      data['servers'] = servers!.map((v) => v.toJson()).toList();
    }
    if (issuanceLicenseDetails != null) {
      data['issuanceLicenseDetails'] =
          issuanceLicenseDetails!.map((v) => v.toJson()).toList();
    }
    if (refferalUsers != null) {
      data['refferalUsers'] = refferalUsers!.map((v) => v.toJson()).toList();
    }
    if (refferalStats != null) {
      data['refferalStats'] = refferalStats!.map((v) => v.toJson()).toList();
    }
    if (cryptoWallet != null) {
      data['cryptoWallet'] = cryptoWallet!.toJson();
    }
    return data;
  }
}

class CryptoWallet {
  CryptoWallet({this.address});

  CryptoWallet.fromJson(Map<String, dynamic> json) {
    final addr = json['address'];
    if (addr is String) {
      address = addr;
    } else {
      // If address is not a String (e.g., Map or null), set to empty string or null
      address = '';
    }
  }
  String? address;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['address'] = address;
    return data;
  }
}

class Phone {
  Phone({this.countryCode, this.number});

  Phone.fromJson(Map<String, dynamic> json) {
    countryCode = json['countryCode'] ?? '';
    number = json['number'] ?? '';
  }
  String? countryCode;
  String? number;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['countryCode'] = countryCode;
    data['number'] = number;
    return data;
  }
}

class NFTs {
  NFTs({
    this.sId,
    this.licenseId,
    this.issuanceId,
    this.nftTokenId,
    this.licenseAttached,
    this.userId,
    this.deviceLinked,
    this.totalEarning,
    this.totalEarningUSD,
    this.totalEarningDailyUsagePay,
    this.totalEarningConnectionPay,
    this.nft,
    this.description,
    this.createdAt,
    this.updatedAt,
    this.serverID,
  });

  NFTs.fromJson(Map<String, dynamic> json) {
    sId = json['_id'];
    licenseId = json['licenseId'];
    issuanceId = json['issuanceId'];
    nftTokenId = json['nftTokenId'];
    licenseAttached = json['licenseAttached'];
    userId = json['userId'];
    deviceLinked = json['deviceLinked'];
    totalEarning = json['totalEarning'].toDouble();
    totalEarningUSD = json['totalEarningUSD'].toDouble();
    totalEarningDailyUsagePay = json['totalEarningDailyUsagePay'].toDouble();
    totalEarningConnectionPay = json['totalEarningConnectionPay'].toDouble();
    nft = json['nft'] != null ? Nft.fromJson(json['nft']) : null;
    description = json['description'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    serverID = json['serverID'] ?? '';
  }
  String? sId;
  String? licenseId;
  String? issuanceId;
  String? nftTokenId;
  bool? licenseAttached;
  String? userId;
  bool? deviceLinked;
  double? totalEarning;
  double? totalEarningUSD;
  double? totalEarningDailyUsagePay;
  double? totalEarningConnectionPay;
  Nft? nft;
  String? description;
  String? createdAt;
  String? updatedAt;
  String? serverID;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['_id'] = sId;
    data['licenseId'] = licenseId;
    data['issuanceId'] = issuanceId;
    data['nftTokenId'] = nftTokenId;
    data['licenseAttached'] = licenseAttached;
    data['userId'] = userId;
    data['deviceLinked'] = deviceLinked;
    data['totalEarning'] = totalEarning;
    data['totalEarningUSD'] = totalEarningUSD;
    data['totalEarningDailyUsagePay'] = totalEarningDailyUsagePay;
    data['totalEarningConnectionPay'] = totalEarningConnectionPay;
    if (nft != null) {
      data['nft'] = nft!.toJson();
    }
    data['description'] = description;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    data['serverID'] = serverID;
    return data;
  }
}

class Nft {
  Nft({this.tokenId, this.imageUrl});

  Nft.fromJson(Map<String, dynamic> json) {
    tokenId = json['tokenId'];
    imageUrl = json['imageUrl'] ?? '';
  }
  dynamic tokenId;
  String? imageUrl;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['tokenId'] = tokenId;
    data['imageUrl'] = imageUrl;
    return data;
  }
}

class Servers {
  Servers({
    this.name,
    this.serverID,
    this.userId,
    this.deviceStatus,
    this.purchaseDate,
    this.licenseId,
    this.licenseLinked,
    this.cpuScore,
    this.cloudScore,
    this.gpuScore,
    this.ramScore,
    this.diskScore,
    this.normalizedGpuScore,
    this.normalizedCpuScore,
    this.normalizedRamScore,
    this.normalizedDiskScore,
    this.normalizedISPScore,
    this.normalizedDeviceScore,
    this.totalEarning,
    this.totalEarningsAfterRecentOnline,
    this.macAddress,
    this.model,
    this.manufacturer,
    this.warrantyExpiration,
    this.location,
    this.software,
    this.phone,
    this.leaseCount,
    this.orderStatus,
  });

  Servers.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    serverID = json['serverID'];
    userId = json['userId'];
    deviceStatus = json['deviceStatus'];
    purchaseDate = json['purchaseDate'] ?? '';
    licenseId = json['licenseId'] ?? '';
    licenseLinked = json['licenseLinked'];
    cpuScore = json['cpuScore'];
    cloudScore = json['cloudScore'].toDouble();
    gpuScore = json['gpuScore'];
    ramScore = json['ramScore'];
    diskScore = json['diskScore'];
    normalizedGpuScore = json['normalizedGpuScore'];
    normalizedCpuScore = json['normalizedCpuScore'];
    normalizedRamScore = json['normalizedRamScore'];
    normalizedDiskScore = json['normalizedDiskScore'];
    normalizedISPScore = json['normalizedISPScore'];
    normalizedDeviceScore = json['normalizedDeviceScore'];
    totalEarning = json['totalEarning'].toDouble();
    totalEarningsAfterRecentOnline = json['totalEarningsAfterRecentOnline'];
    macAddress = json['macAddress'];
    model = json['model'];
    manufacturer = json['manufacturer'];
    warrantyExpiration = json['warrantyExpiration'];
    location = json['location'];
    if (json['software'] != null) {
      software = <Software>[];
      json['software'].forEach((v) {
        software!.add(Software.fromJson(v));
      });
    }
    phone = json['phone'] != null ? Phone.fromJson(json['phone']) : null;
    leaseCount = json['leaseCount'];
    orderStatus = json['orderStatus'];
  }
  String? name;
  String? serverID;
  String? userId;
  bool? deviceStatus;
  String? purchaseDate;
  String? licenseId;
  bool? licenseLinked;
  int? cpuScore;
  double? cloudScore;
  int? gpuScore;
  double? ramScore;
  int? diskScore;
  double? normalizedGpuScore;
  double? normalizedCpuScore;
  double? normalizedRamScore;
  double? normalizedDiskScore;
  int? normalizedISPScore;
  double? normalizedDeviceScore;
  double? totalEarning;
  double? totalEarningsAfterRecentOnline;
  String? macAddress;
  String? model;
  String? manufacturer;
  String? warrantyExpiration;
  String? location;
  List<Software>? software;
  Phone? phone;
  int? leaseCount;
  String? orderStatus;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['name'] = name;
    data['serverID'] = serverID;
    data['userId'] = userId;
    data['deviceStatus'] = deviceStatus;
    data['purchaseDate'] = purchaseDate;
    data['licenseId'] = licenseId;
    data['licenseLinked'] = licenseLinked;
    data['cpuScore'] = cpuScore;
    data['cloudScore'] = cloudScore;
    data['gpuScore'] = gpuScore;
    data['ramScore'] = ramScore;
    data['diskScore'] = diskScore;
    data['normalizedGpuScore'] = normalizedGpuScore;
    data['normalizedCpuScore'] = normalizedCpuScore;
    data['normalizedRamScore'] = normalizedRamScore;
    data['normalizedDiskScore'] = normalizedDiskScore;
    data['normalizedISPScore'] = normalizedISPScore;
    data['normalizedDeviceScore'] = normalizedDeviceScore;
    data['totalEarning'] = totalEarning;
    data['totalEarningsAfterRecentOnline'] = totalEarningsAfterRecentOnline;
    data['macAddress'] = macAddress;
    data['model'] = model;
    data['manufacturer'] = manufacturer;
    data['warrantyExpiration'] = warrantyExpiration;
    data['location'] = location;
    if (software != null) {
      data['software'] = software!.map((v) => v.toJson()).toList();
    }
    if (phone != null) {
      data['phone'] = phone!.toJson();
    }
    data['leaseCount'] = leaseCount;
    data['orderStatus'] = orderStatus;
    return data;
  }
}

class Software {
  Software({
    this.name,
    this.version,
    this.licenseType,
    this.expirationDate,
    this.licenseKey,
  });

  Software.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    version = json['version'];
    licenseType = json['licenseType'];
    expirationDate = json['expirationDate'];
    licenseKey = json['licenseKey'];
  }
  String? name;
  String? version;
  String? licenseType;
  String? expirationDate;
  String? licenseKey;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['name'] = name;
    data['version'] = version;
    data['licenseType'] = licenseType;
    data['expirationDate'] = expirationDate;
    data['licenseKey'] = licenseKey;
    return data;
  }
}

class Orders {
  Orders({
    this.sId,
    this.trigger,
    this.orderId,
    this.status,
    this.comment,
    this.orderComment,
    this.acceptedOn,
    this.disputedOn,
    this.disputeUpdatedOn,
    this.disputeLastStatus,
    this.fulfilledOn,
    this.refundedOn,
    this.customerPaid,
    this.netAmount,
    this.applicationFee,
    this.shippingProvider,
    this.shippingTracking,
    this.shippingTrackingURL,
    this.customerInfo,
    this.allAddresses,
    this.shippingAddress,
    this.billingAddress,
    this.purchasedItems,
    this.purchasedItemsCount,
    this.totals,
    this.customData,
    this.paypalDetails,
    this.stripeCard,
    this.stripeDetails,
    this.paymentProcessor,
    this.hasDownloads,
    this.downloadFiles,
    this.metadata,
    this.isCustomerDeleted,
    this.isShippingRequired,
    this.createdAt,
    this.updatedAt,
    this.servers,
  });

  Orders.fromJson(Map<String, dynamic> json) {
    sId = json['_id'];
    trigger = json['trigger'];
    orderId = json['orderId'];
    status = json['status'];
    comment = json['comment'];
    orderComment = json['orderComment'];
    acceptedOn = json['acceptedOn'];
    disputedOn = json['disputedOn'];
    disputeUpdatedOn = json['disputeUpdatedOn'];
    disputeLastStatus = json['disputeLastStatus'];
    fulfilledOn = json['fulfilledOn'];
    refundedOn = json['refundedOn'];
    customerPaid = json['customerPaid'] != null
        ? CustomerPaid.fromJson(json['customerPaid'])
        : null;
    netAmount = json['netAmount'] != null
        ? CustomerPaid.fromJson(json['netAmount'])
        : null;
    applicationFee = json['applicationFee'] != null
        ? ApplicationFee.fromJson(json['applicationFee'])
        : null;
    shippingProvider = json['shippingProvider'];
    shippingTracking = json['shippingTracking'];
    shippingTrackingURL = json['shippingTrackingURL'];
    customerInfo = json['customerInfo'] != null
        ? CustomerInfo.fromJson(json['customerInfo'])
        : null;
    if (json['allAddresses'] != null) {
      allAddresses = <AllAddresses>[];
      json['allAddresses'].forEach((v) {
        allAddresses!.add(AllAddresses.fromJson(v));
      });
    }
    shippingAddress = json['shippingAddress'] != null
        ? AllAddresses.fromJson(json['shippingAddress'])
        : null;
    billingAddress = json['billingAddress'] != null
        ? AllAddresses.fromJson(json['billingAddress'])
        : null;
    if (json['purchasedItems'] != null) {
      purchasedItems = <PurchasedItems>[];
      json['purchasedItems'].forEach((v) {
        purchasedItems!.add(PurchasedItems.fromJson(v));
      });
    }
    purchasedItemsCount = json['purchasedItemsCount'];
    totals = json['totals'] != null ? Totals.fromJson(json['totals']) : null;
    if (json['customData'] != null) {
      customData = <CustomData>[];
      json['customData'].forEach((v) {
        customData!.add(CustomData.fromJson(v));
      });
    }
    paypalDetails = json['paypalDetails'];
    stripeCard = json['stripeCard'] != null
        ? StripeCard.fromJson(json['stripeCard'])
        : null;
    stripeDetails = json['stripeDetails'] != null
        ? StripeDetails.fromJson(json['stripeDetails'])
        : null;
    paymentProcessor = json['paymentProcessor'];
    hasDownloads = json['hasDownloads'];
    if (json['downloadFiles'] != null) {
      downloadFiles = [];
      json['downloadFiles'].forEach((v) {
        // downloadFiles!.add(Null.fromJson(v));
      });
    }
    metadata =
        json['metadata'] != null ? Metadata.fromJson(json['metadata']) : null;
    isCustomerDeleted = json['isCustomerDeleted'];
    isShippingRequired = json['isShippingRequired'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    if (json['servers'] != null) {
      servers = <Servers>[];
      json['servers'].forEach((v) {
        if (v != null) servers!.add(Servers.fromJson(v));
      });
    }
  }
  String? sId;
  String? trigger;
  String? orderId;
  String? status;
  String? comment;
  String? orderComment;
  String? acceptedOn;
  dynamic disputedOn;
  dynamic disputeUpdatedOn;
  dynamic disputeLastStatus;
  dynamic fulfilledOn;
  dynamic refundedOn;
  CustomerPaid? customerPaid;
  CustomerPaid? netAmount;
  ApplicationFee? applicationFee;
  dynamic shippingProvider;
  dynamic shippingTracking;
  dynamic shippingTrackingURL;
  CustomerInfo? customerInfo;
  List<AllAddresses>? allAddresses;
  AllAddresses? shippingAddress;
  AllAddresses? billingAddress;
  List<PurchasedItems>? purchasedItems;
  int? purchasedItemsCount;
  Totals? totals;
  List<CustomData>? customData;
  dynamic paypalDetails;
  StripeCard? stripeCard;
  StripeDetails? stripeDetails;
  String? paymentProcessor;
  bool? hasDownloads;
  // List<Null>? downloadFiles;
  List? downloadFiles;
  Metadata? metadata;
  bool? isCustomerDeleted;
  bool? isShippingRequired;
  String? createdAt;
  String? updatedAt;
  List<Servers>? servers;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['_id'] = sId;
    data['trigger'] = trigger;
    data['orderId'] = orderId;
    data['status'] = status;
    data['comment'] = comment;
    data['orderComment'] = orderComment;
    data['acceptedOn'] = acceptedOn;
    data['disputedOn'] = disputedOn;
    data['disputeUpdatedOn'] = disputeUpdatedOn;
    data['disputeLastStatus'] = disputeLastStatus;
    data['fulfilledOn'] = fulfilledOn;
    data['refundedOn'] = refundedOn;
    if (customerPaid != null) {
      data['customerPaid'] = customerPaid!.toJson();
    }
    if (netAmount != null) {
      data['netAmount'] = netAmount!.toJson();
    }
    if (applicationFee != null) {
      data['applicationFee'] = applicationFee!.toJson();
    }
    data['shippingProvider'] = shippingProvider;
    data['shippingTracking'] = shippingTracking;
    data['shippingTrackingURL'] = shippingTrackingURL;
    if (customerInfo != null) {
      data['customerInfo'] = customerInfo!.toJson();
    }
    if (allAddresses != null) {
      data['allAddresses'] = allAddresses!.map((v) => v.toJson()).toList();
    }
    if (shippingAddress != null) {
      data['shippingAddress'] = shippingAddress!.toJson();
    }
    if (billingAddress != null) {
      data['billingAddress'] = billingAddress!.toJson();
    }
    if (purchasedItems != null) {
      data['purchasedItems'] = purchasedItems!.map((v) => v.toJson()).toList();
    }
    data['purchasedItemsCount'] = purchasedItemsCount;
    if (totals != null) {
      data['totals'] = totals!.toJson();
    }
    if (customData != null) {
      data['customData'] = customData!.map((v) => v.toJson()).toList();
    }
    data['paypalDetails'] = paypalDetails;
    if (stripeCard != null) {
      data['stripeCard'] = stripeCard!.toJson();
    }
    if (stripeDetails != null) {
      data['stripeDetails'] = stripeDetails!.toJson();
    }
    data['paymentProcessor'] = paymentProcessor;
    data['hasDownloads'] = hasDownloads;
    if (downloadFiles != null) {
      data['downloadFiles'] = downloadFiles!.map((v) => v.toJson()).toList();
    }
    if (metadata != null) {
      data['metadata'] = metadata!.toJson();
    }
    data['isCustomerDeleted'] = isCustomerDeleted;
    data['isShippingRequired'] = isShippingRequired;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    return data;
  }
}

class CustomerPaid {
  CustomerPaid({this.unit, this.value, this.string});

  CustomerPaid.fromJson(Map<String, dynamic> json) {
    unit = json['unit'];
    value = json['value'];
    string = json['string'];
  }
  String? unit;
  int? value;
  String? string;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['unit'] = unit;
    data['value'] = value;
    data['string'] = string;
    return data;
  }
}

class ApplicationFee {
  ApplicationFee({this.value, this.unit});

  ApplicationFee.fromJson(Map<String, dynamic> json) {
    value = json['value'];
    unit = json['unit'];
  }
  int? value;
  String? unit;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['value'] = value;
    data['unit'] = unit;
    return data;
  }
}

class CustomerInfo {
  CustomerInfo({this.fullName, this.email});

  CustomerInfo.fromJson(Map<String, dynamic> json) {
    fullName = json['fullName'];
    email = json['email'];
  }
  String? fullName;
  String? email;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['fullName'] = fullName;
    data['email'] = email;
    return data;
  }
}

class AllAddresses {
  AllAddresses({
    this.type,
    this.addressee,
    this.line1,
    this.line2,
    this.city,
    this.state,
    this.country,
    this.postalCode,
  });

  AllAddresses.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    addressee = json['addressee'];
    line1 = json['line1'];
    line2 = json['line2'];
    city = json['city'];
    state = json['state'];
    country = json['country'];
    postalCode = json['postalCode'];
  }
  String? type;
  String? addressee;
  String? line1;
  dynamic line2;
  String? city;
  String? state;
  String? country;
  String? postalCode;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['type'] = type;
    data['addressee'] = addressee;
    data['line1'] = line1;
    data['line2'] = line2;
    data['city'] = city;
    data['state'] = state;
    data['country'] = country;
    data['postalCode'] = postalCode;
    return data;
  }
}

class PurchasedItems {
  PurchasedItems({
    this.count,
    this.rowTotal,
    this.productId,
    this.productName,
    this.productSlug,
    this.variantId,
    this.variantName,
    this.variantSlug,
    this.variantSKU,
    this.variantImage,
    this.variantPrice,
    this.weight,
    this.height,
    this.width,
    this.length,
  });

  PurchasedItems.fromJson(Map<String, dynamic> json) {
    count = json['count'];
    rowTotal = json['rowTotal'] != null
        ? CustomerPaid.fromJson(json['rowTotal'])
        : null;
    productId = json['productId'];
    productName = json['productName'];
    productSlug = json['productSlug'];
    variantId = json['variantId'];
    variantName = json['variantName'];
    variantSlug = json['variantSlug'];
    variantSKU = json['variantSKU'];
    variantImage = json['variantImage'] != null
        ? VariantImage.fromJson(json['variantImage'])
        : null;
    variantPrice = json['variantPrice'] != null
        ? CustomerPaid.fromJson(json['variantPrice'])
        : null;
    weight = json['weight'];
    height = json['height'].toDouble();
    width = json['width'];
    length = json['length'];
  }
  int? count;
  CustomerPaid? rowTotal;
  String? productId;
  String? productName;
  String? productSlug;
  String? variantId;
  String? variantName;
  String? variantSlug;
  String? variantSKU;
  VariantImage? variantImage;
  CustomerPaid? variantPrice;
  int? weight;
  double? height;
  double? width;
  double? length;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['count'] = count;
    if (rowTotal != null) {
      data['rowTotal'] = rowTotal!.toJson();
    }
    data['productId'] = productId;
    data['productName'] = productName;
    data['productSlug'] = productSlug;
    data['variantId'] = variantId;
    data['variantName'] = variantName;
    data['variantSlug'] = variantSlug;
    data['variantSKU'] = variantSKU;
    if (variantImage != null) {
      data['variantImage'] = variantImage!.toJson();
    }
    if (variantPrice != null) {
      data['variantPrice'] = variantPrice!.toJson();
    }
    data['weight'] = weight;
    data['height'] = height;
    data['width'] = width;
    data['length'] = length;
    return data;
  }
}

class VariantImage {
  VariantImage({this.fileId, this.url, this.alt, this.file});

  VariantImage.fromJson(Map<String, dynamic> json) {
    fileId = json['fileId'];
    url = json['url'];
    alt = json['alt'];
    file = json['file'];
  }
  String? fileId;
  String? url;
  dynamic alt;
  dynamic file;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['fileId'] = fileId;
    data['url'] = url;
    data['alt'] = alt;
    data['file'] = file;
    return data;
  }
}

class Totals {
  Totals({this.subtotal, this.extras, this.total});

  Totals.fromJson(Map<String, dynamic> json) {
    subtotal = json['subtotal'] != null
        ? CustomerPaid.fromJson(json['subtotal'])
        : null;
    if (json['extras'] != null) {
      extras = <Extras>[];
      json['extras'].forEach((v) {
        extras!.add(Extras.fromJson(v));
      });
    }
    total = json['total'] != null ? CustomerPaid.fromJson(json['total']) : null;
  }
  CustomerPaid? subtotal;
  List<Extras>? extras;
  CustomerPaid? total;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (subtotal != null) {
      data['subtotal'] = subtotal!.toJson();
    }
    if (extras != null) {
      data['extras'] = extras!.map((v) => v.toJson()).toList();
    }
    if (total != null) {
      data['total'] = total!.toJson();
    }
    return data;
  }
}

class Extras {
  Extras({this.type, this.name, this.description, this.price});

  Extras.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    name = json['name'];
    description = json['description'];
    price = json['price'] != null ? CustomerPaid.fromJson(json['price']) : null;
  }
  String? type;
  String? name;
  String? description;
  CustomerPaid? price;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['type'] = type;
    data['name'] = name;
    data['description'] = description;
    if (price != null) {
      data['price'] = price!.toJson();
    }
    return data;
  }
}

class CustomData {
  CustomData({this.name, this.textInput});

  CustomData.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    textInput = json['textInput'];
  }
  String? name;
  String? textInput;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['name'] = name;
    data['textInput'] = textInput;
    return data;
  }
}

class StripeCard {
  StripeCard({this.last4, this.brand, this.ownerName, this.expires});

  StripeCard.fromJson(Map<String, dynamic> json) {
    last4 = json['last4'];
    brand = json['brand'];
    ownerName = json['ownerName'];
    expires =
        json['expires'] != null ? Expires.fromJson(json['expires']) : null;
  }
  String? last4;
  String? brand;
  String? ownerName;
  Expires? expires;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['last4'] = last4;
    data['brand'] = brand;
    data['ownerName'] = ownerName;
    if (expires != null) {
      data['expires'] = expires!.toJson();
    }
    return data;
  }
}

class Expires {
  Expires({this.month, this.year});

  Expires.fromJson(Map<String, dynamic> json) {
    month = json['month'];
    year = json['year'];
  }
  int? month;
  int? year;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['month'] = month;
    data['year'] = year;
    return data;
  }
}

class StripeDetails {
  StripeDetails({
    this.customerId,
    this.paymentMethod,
    this.chargeId,
    this.disputeId,
    this.paymentIntentId,
    this.subscriptionId,
    this.refundId,
    this.refundReason,
  });

  StripeDetails.fromJson(Map<String, dynamic> json) {
    customerId = json['customerId'];
    paymentMethod = json['paymentMethod'];
    chargeId = json['chargeId'];
    disputeId = json['disputeId'];
    paymentIntentId = json['paymentIntentId'];
    subscriptionId = json['subscriptionId'];
    refundId = json['refundId'];
    refundReason = json['refundReason'];
  }
  String? customerId;
  String? paymentMethod;
  String? chargeId;
  dynamic disputeId;
  String? paymentIntentId;
  dynamic subscriptionId;
  dynamic refundId;
  dynamic refundReason;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['customerId'] = customerId;
    data['paymentMethod'] = paymentMethod;
    data['chargeId'] = chargeId;
    data['disputeId'] = disputeId;
    data['paymentIntentId'] = paymentIntentId;
    data['subscriptionId'] = subscriptionId;
    data['refundId'] = refundId;
    data['refundReason'] = refundReason;
    return data;
  }
}

class Metadata {
  Metadata({this.isBuyNow});

  Metadata.fromJson(Map<String, dynamic> json) {
    isBuyNow = json['isBuyNow'];
  }
  bool? isBuyNow;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['isBuyNow'] = isBuyNow;
    return data;
  }
}

class DailyEarnings {
  DailyEarnings({
    this.sId,
    this.userId,
    this.nxqEarning,
    this.nxqEarningsInUsd,
    this.date,
    this.createdAt,
    this.updatedAt,
  });

  DailyEarnings.fromJson(Map<String, dynamic> json) {
    sId = json['_id'];
    userId = json['userId'];
    nxqEarning = json['nxqEarning'];
    nxqEarningsInUsd = json['nxqEarningsInUsd'];
    date = json['date'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }
  String? sId;
  String? userId;
  double? nxqEarning;
  double? nxqEarningsInUsd;
  String? date;
  String? createdAt;
  String? updatedAt;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['_id'] = sId;
    data['userId'] = userId;
    data['nxqEarning'] = nxqEarning;
    data['nxqEarningsInUsd'] = nxqEarningsInUsd;
    data['date'] = date;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    return data;
  }
}

class IssuanceLicenseDetails {
  IssuanceLicenseDetails({
    this.sId,
    this.id,
    this.quantity,
    this.price,
    this.amount,
    this.status,
    this.paymentIntentId,
    this.offering,
    this.referenceId,
    this.investorIndividualName,
    this.investor,
    this.paymentMethod,
    this.hasInvestorSigned,
    this.watchlist,
    this.fromInvoice,
    this.accreditedInvestor,
    this.netWorth,
    this.annualIncome,
    this.usPerson,
    this.exemptFromBackupWithholding,
    this.amountInvestedInRegCfOfferingThisYear,
    this.userBrowserType,
    this.accreditedInvestorType,
    this.attorneyOrCpaEmail,
    this.sharesAmount,
    this.processedAt,
    this.receivedAt,
    this.investedAt,
    this.refundedAt,
    this.updatedat,
    this.watchlistentitySet,
    this.bonusShares,
    this.bonusSharesCode,
    this.bonusSharesPercentageTier,
    this.bonusSharesQuantityTier,
    this.createdAt,
    this.updatedAt,
  });

  IssuanceLicenseDetails.fromJson(Map<String, dynamic> json) {
    sId = json['_id'];
    id = json['id'];
    quantity = json['quantity'];
    price = json['price'];
    amount = json['amount'];
    status = json['status'];
    paymentIntentId = json['payment_intent_id'];
    offering = json['offering'];
    referenceId = json['reference_id'];
    investorIndividualName = json['investor_individual_name'];
    investor =
        json['investor'] != null ? Investor.fromJson(json['investor']) : null;
    paymentMethod = json['payment_method'];
    hasInvestorSigned = json['has_investor_signed'];
    watchlist = json['watchlist'] != null
        ? Watchlist.fromJson(json['watchlist'])
        : null;
    fromInvoice = json['from_invoice'];
    accreditedInvestor = json['accredited_investor'];
    netWorth = json['net_worth'];
    annualIncome = json['annual_income'];
    usPerson = json['us_person'];
    exemptFromBackupWithholding = json['exempt_from_backup_withholding'];
    amountInvestedInRegCfOfferingThisYear =
        json['amount_invested_in_reg_cf_offering_this_year'];
    userBrowserType = json['user_browser_type'];
    accreditedInvestorType = json['accredited_investor_type'];
    attorneyOrCpaEmail = json['attorney_or_cpa_email'];
    sharesAmount = json['shares_amount'];
    processedAt = json['processed_at'];
    receivedAt = json['received_at'];
    investedAt = json['invested_at'];
    refundedAt = json['refunded_at'];
    updatedat = json['updated_at'];
    if (json['watchlistentity_set'] != null) {
      watchlistentitySet = <WatchlistentitySet>[];
      json['watchlistentity_set'].forEach((v) {
        watchlistentitySet!.add(WatchlistentitySet.fromJson(v));
      });
    }
    bonusShares = json['bonus_shares'];
    bonusSharesCode = json['bonus_shares_code'];
    bonusSharesPercentageTier = json['bonus_shares_percentage_tier'];
    bonusSharesQuantityTier = json['bonus_shares_quantity_tier'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }
  String? sId;
  int? id;
  int? quantity;
  String? price;
  String? amount;
  String? status;
  String? paymentIntentId;
  int? offering;
  String? referenceId;
  String? investorIndividualName;
  Investor? investor;
  String? paymentMethod;
  bool? hasInvestorSigned;
  Watchlist? watchlist;
  bool? fromInvoice;
  bool? accreditedInvestor;
  dynamic netWorth;
  dynamic annualIncome;
  dynamic usPerson;
  dynamic exemptFromBackupWithholding;
  int? amountInvestedInRegCfOfferingThisYear;
  String? userBrowserType;
  dynamic accreditedInvestorType;
  String? attorneyOrCpaEmail;
  String? sharesAmount;
  String? processedAt;
  String? receivedAt;
  dynamic investedAt;
  dynamic refundedAt;
  String? updatedat;
  List<WatchlistentitySet>? watchlistentitySet;
  dynamic bonusShares;
  String? bonusSharesCode;
  dynamic bonusSharesPercentageTier;
  dynamic bonusSharesQuantityTier;
  String? createdAt;
  String? updatedAt;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['_id'] = sId;
    data['id'] = id;
    data['quantity'] = quantity;
    data['price'] = price;
    data['amount'] = amount;
    data['status'] = status;
    data['payment_intent_id'] = paymentIntentId;
    data['offering'] = offering;
    data['reference_id'] = referenceId;
    data['investor_individual_name'] = investorIndividualName;
    if (investor != null) {
      data['investor'] = investor!.toJson();
    }
    data['payment_method'] = paymentMethod;
    data['has_investor_signed'] = hasInvestorSigned;
    if (watchlist != null) {
      data['watchlist'] = watchlist!.toJson();
    }
    data['from_invoice'] = fromInvoice;
    data['accredited_investor'] = accreditedInvestor;
    data['net_worth'] = netWorth;
    data['annual_income'] = annualIncome;
    data['us_person'] = usPerson;
    data['exempt_from_backup_withholding'] = exemptFromBackupWithholding;
    data['amount_invested_in_reg_cf_offering_this_year'] =
        amountInvestedInRegCfOfferingThisYear;
    data['user_browser_type'] = userBrowserType;
    data['accredited_investor_type'] = accreditedInvestorType;
    data['attorney_or_cpa_email'] = attorneyOrCpaEmail;
    data['shares_amount'] = sharesAmount;
    data['processed_at'] = processedAt;
    data['received_at'] = receivedAt;
    data['invested_at'] = investedAt;
    data['refunded_at'] = refundedAt;
    data['updated_at'] = updatedAt;
    if (watchlistentitySet != null) {
      data['watchlistentity_set'] =
          watchlistentitySet!.map((v) => v.toJson()).toList();
    }
    data['bonus_shares'] = bonusShares;
    data['bonus_shares_code'] = bonusSharesCode;
    data['bonus_shares_percentage_tier'] = bonusSharesPercentageTier;
    data['bonus_shares_quantity_tier'] = bonusSharesQuantityTier;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    return data;
  }
}

class WatchlistentitySet {
  WatchlistentitySet({
    this.id,
    this.entity,
    this.hasHits,
    this.watchlistHits,
    this.ofacFincenStatus,
  });

  WatchlistentitySet.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    entity = json['entity'];
    hasHits = json['has_hits'];
    if (json['watchlist_hits'] != null) {
      watchlistHits = <Null>[];
      json['watchlist_hits'].forEach((v) {
        // watchlistHits!.add(new Null.fromJson(v));
      });
    }
    ofacFincenStatus = json['ofac_fincen_status'];
  }
  int? id;
  int? entity;
  bool? hasHits;
  List? watchlistHits;
  String? ofacFincenStatus;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['entity'] = entity;
    data['has_hits'] = hasHits;
    if (watchlistHits != null) {
      data['watchlist_hits'] = watchlistHits!.map((v) => v.toJson()).toList();
    }
    data['ofac_fincen_status'] = ofacFincenStatus;
    return data;
  }
}

class Investor {
  Investor({
    this.id,
    this.user,
    this.entity,
    this.type,
    this.receiveSms,
    this.receiveEmails,
    this.utmParameters,
  });

  Investor.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    user = json['user'] != null ? User.fromJson(json['user']) : null;
    entity = json['entity'] != null ? Entity.fromJson(json['entity']) : null;
    type = json['type'];
    receiveSms = json['receive_sms'];
    receiveEmails = json['receive_emails'];
    utmParameters = json['utm_parameters'];
  }
  int? id;
  User? user;
  Entity? entity;
  String? type;
  bool? receiveSms;
  bool? receiveEmails;
  dynamic utmParameters;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    if (user != null) {
      data['user'] = user!.toJson();
    }
    if (entity != null) {
      data['entity'] = entity!.toJson();
    }
    data['type'] = type;
    data['receive_sms'] = receiveSms;
    data['receive_emails'] = receiveEmails;
    data['utm_parameters'] = utmParameters;
    return data;
  }
}

class User {
  User({
    this.id,
    this.email,
    this.firstName,
    this.lastName,
    this.dateJoined,
    this.lastLogin,
  });

  User.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    email = json['email'];
    firstName = json['first_name'];
    lastName = json['last_name'];
    dateJoined = json['date_joined'];
    lastLogin = json['last_login'];
  }
  int? id;
  String? email;
  String? firstName;
  String? lastName;
  String? dateJoined;
  String? lastLogin;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['email'] = email;
    data['first_name'] = firstName;
    data['last_name'] = lastName;
    data['date_joined'] = dateJoined;
    data['last_login'] = lastLogin;
    return data;
  }
}

class Entity {
  Entity({
    this.id,
    this.email,
    this.phone,
    this.streetAddress1,
    this.streetAddress2,
    this.city,
    this.state,
    this.region,
    this.postalCode,
    this.country,
    this.kycStatus,
    this.firstName,
    this.lastName,
    this.dateOfBirth,
    this.netWorth,
    this.annualIncome,
    this.accreditedInvestor,
    this.citizenship,
    this.usPerson,
    this.exemptFromBackupWithholding,
    this.amountInvestedInRegCfOfferingThisYear,
    this.accreditedInvestorType,
    this.attorneyOrCpaEmail,
    this.taxIdNumber,
  });

  Entity.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    email = json['email'];
    phone = json['phone'];
    streetAddress1 = json['street_address_1'];
    streetAddress2 = json['street_address_2'];
    city = json['city'];
    state = json['state'];
    region = json['region'];
    postalCode = json['postal_code'];
    country = json['country'];
    kycStatus = json['kyc_status'];
    firstName = json['first_name'];
    lastName = json['last_name'];
    dateOfBirth = json['date_of_birth'];
    netWorth = json['net_worth'];
    annualIncome = json['annual_income'];
    accreditedInvestor = json['accredited_investor'];
    citizenship = json['citizenship'];
    usPerson = json['us_person'];
    exemptFromBackupWithholding = json['exempt_from_backup_withholding'];
    amountInvestedInRegCfOfferingThisYear =
        json['amount_invested_in_reg_cf_offering_this_year'];
    accreditedInvestorType = json['accredited_investor_type'];
    attorneyOrCpaEmail = json['attorney_or_cpa_email'];
    taxIdNumber = json['tax_id_number'];
  }
  int? id;
  String? email;
  String? phone;
  String? streetAddress1;
  String? streetAddress2;
  String? city;
  String? state;
  String? region;
  String? postalCode;
  String? country;
  String? kycStatus;
  String? firstName;
  String? lastName;
  String? dateOfBirth;
  dynamic netWorth;
  dynamic annualIncome;
  bool? accreditedInvestor;
  String? citizenship;
  bool? usPerson;
  bool? exemptFromBackupWithholding;
  int? amountInvestedInRegCfOfferingThisYear;
  String? accreditedInvestorType;
  String? attorneyOrCpaEmail;
  String? taxIdNumber;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['email'] = email;
    data['phone'] = phone;
    data['street_address_1'] = streetAddress1;
    data['street_address_2'] = streetAddress2;
    data['city'] = city;
    data['state'] = state;
    data['region'] = region;
    data['postal_code'] = postalCode;
    data['country'] = country;
    data['kyc_status'] = kycStatus;
    data['first_name'] = firstName;
    data['last_name'] = lastName;
    data['date_of_birth'] = dateOfBirth;
    data['net_worth'] = netWorth;
    data['annual_income'] = annualIncome;
    data['accredited_investor'] = accreditedInvestor;
    data['citizenship'] = citizenship;
    data['us_person'] = usPerson;
    data['exempt_from_backup_withholding'] = exemptFromBackupWithholding;
    data['amount_invested_in_reg_cf_offering_this_year'] =
        amountInvestedInRegCfOfferingThisYear;
    data['accredited_investor_type'] = accreditedInvestorType;
    data['attorney_or_cpa_email'] = attorneyOrCpaEmail;
    data['tax_id_number'] = taxIdNumber;
    return data;
  }
}

class Watchlist {
  Watchlist({
    this.id,
    this.entity,
    this.hasHits,
    this.watchlistHits,
    this.ofacFincenStatus,
  });

  Watchlist.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    entity = json['entity'];
    hasHits = json['has_hits'];
    if (json['watchlist_hits'] != null) {
      watchlistHits = <Null>[];
      json['watchlist_hits'].forEach((v) {
        // watchlistHits!.add(new Null.fromJson(v));
      });
    }
    ofacFincenStatus = json['ofac_fincen_status'];
  }
  int? id;
  int? entity;
  bool? hasHits;
  List<void>? watchlistHits;
  String? ofacFincenStatus;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['entity'] = entity;
    data['has_hits'] = hasHits;
    if (watchlistHits != null) {
      // data['watchlist_hits'] =
      //     this.watchlistHits!.map((v) => v.toJson()).toList();
    }
    data['ofac_fincen_status'] = ofacFincenStatus;
    return data;
  }
}

class RefferalUsers {
  RefferalUsers({
    this.sId,
    this.referredId,
    this.createdAt,
    this.referredByPhone,
    this.referredDate,
    this.updatedAt,
    this.userId,
  });

  RefferalUsers.fromJson(Map<String, dynamic> json) {
    sId = json['_id'];
    referredId = json['referredId'];
    createdAt = json['createdAt'];
    referredByPhone = json['referredByPhone'] != null
        ? Phone.fromJson(json['referredByPhone'])
        : null;
    referredDate = json['referredDate'];
    updatedAt = json['updatedAt'];
    userId = json['userId'];
  }
  String? sId;
  String? referredId;
  String? createdAt;
  Phone? referredByPhone;
  String? referredDate;
  String? updatedAt;
  String? userId;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['_id'] = sId;
    data['referredId'] = referredId;
    data['createdAt'] = createdAt;
    if (referredByPhone != null) {
      data['referredByPhone'] = referredByPhone!.toJson();
    }
    data['referredDate'] = referredDate;
    data['updatedAt'] = updatedAt;
    data['userId'] = userId;
    return data;
  }
}

class RefferalStats {
  RefferalStats({
    this.sId,
    this.userId,
    this.personalDevicesCount,
    this.deviceUpTime,
    this.managedDevicesCount,
    this.managedTeamCount,
    this.averageScore,
    this.normalizedScore,
    this.tier,
    this.createdAt,
    this.updatedAt,
    this.updatedOn,
  });

  RefferalStats.fromJson(Map<String, dynamic> json) {
    sId = json['_id'];
    userId = json['userId'];
    personalDevicesCount = json['personalDevicesCount'];
    // deviceUpTime = 0.00;
    deviceUpTime = json['deviceUpTime'].toDouble();
    managedDevicesCount = json['managedDevicesCount'];
    managedTeamCount = json['managedTeamCount'];
    // averageScore = 0.00;
    // normalizedScore = 0.00;
    averageScore = json['averageScore'] ?? 0.00;
    averageScore = averageScore!;
    normalizedScore = json['normalizedScore'] ?? 0.00;
    normalizedScore = normalizedScore!;
    tier = json['tier'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    updatedOn = json['updatedOn'];
  }
  String? sId;
  String? userId;
  int? personalDevicesCount;
  double? deviceUpTime;
  int? managedDevicesCount;
  int? managedTeamCount;
  double? averageScore;
  double? normalizedScore;
  String? tier;
  String? createdAt;
  String? updatedAt;
  String? updatedOn;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['_id'] = sId;
    data['userId'] = userId;
    data['personalDevicesCount'] = personalDevicesCount;
    data['deviceUpTime'] = deviceUpTime;
    data['managedDevicesCount'] = managedDevicesCount;
    data['managedTeamCount'] = managedTeamCount;
    data['averageScore'] = averageScore;
    data['normalizedScore'] = normalizedScore;
    data['tier'] = tier;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    data['updatedOn'] = updatedOn;
    return data;
  }
}
