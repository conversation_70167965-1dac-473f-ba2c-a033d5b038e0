//A Show dialog dialog to show error message
import 'package:admin_portal/configs/configs_export.dart';
import 'package:flutter/material.dart';

class ErrorDialog extends StatelessWidget {
  const ErrorDialog({super.key, this.message});
  final String? message;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        'Error',
        style: AppTextStyles.textStyleBold26(context),
      ),
      content: Text(
        message ?? '',
        style: AppTextStyles.textStyleRegular16(context),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.pop(context);
          },
          child: Text(
            'OK',
            style: AppTextStyles.textStyleRegular16(context),
          ),
        ),
      ],
    );
  }
}

void showErrorDialog(BuildContext context, String? message) {
  showDialog(
    context: context,
    builder: (context) {
      return ErrorDialog(message: message);
    },
  );
}
