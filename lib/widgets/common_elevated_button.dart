import 'package:admin_portal/configs/configs_export.dart';
import 'package:flutter/material.dart';

class CommonElevatedButton extends StatelessWidget {
  const CommonElevatedButton({
    super.key,
    required this.text,
    required this.onTap,
    this.borderRadius,
  });

  final String text;
  //onTap function parameter required
  final Function() onTap;
  final double? borderRadius;

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        // backgroundColor: AppColors.kLightPrimarySwatch,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius ?? 40),
        ),
      ),
      onPressed: onTap,
      child: Text(
        text,
        style: AppTextStyles.textStyleDashboardMessage(context).copyWith(
          color: AppColors.kWhite,
        ),
      ),
    );
  }
}
