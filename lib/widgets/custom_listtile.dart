import 'package:admin_portal/configs/configs_export.dart';
import 'package:flutter/material.dart';

class CustomListTile extends StatelessWidget {
  const CustomListTile({
    super.key,
    required this.title,
    this.tileColor,
    this.leadingButtonColor,
    this.onTap,
    this.showIcon,
    required this.isDarkTheme,
  });
  final String title;
  final Color? tileColor;
  final bool isDarkTheme;
  final Color? leadingButtonColor;
  final bool? showIcon;
  final Function()? onTap;
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 300,
      child: ListTile(
        onTap: onTap,
        tileColor: tileColor ?? AppColors.kWhite,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        leading: showIcon ?? true
            ? ClipRRect(
                borderRadius: BorderRadius.circular(10),
                child: Container(
                  width: 40,
                  height: 40,
                  color: leadingButtonColor ??
                      (isDarkTheme
                          ? AppColors.kDarkPrimarySwatch
                          : AppColors.kLightPrimarySwatch),
                  child: Icon(
                    Icons.add,
                    color: leadingButtonColor == null
                        ? AppColors.kWhite
                        : AppColors.kLightPrimarySwatch,
                  ),
                ),
              )
            : null,
        title: Text(
          title,
          style: AppTextStyles.textStyleBlueBold(context).copyWith(
            color: leadingButtonColor ??
                (isDarkTheme
                    ? AppColors.kDarkPrimarySwatch
                    : AppColors.kLightPrimarySwatch),
          ),
        ),
      ),
    );
  }
}
