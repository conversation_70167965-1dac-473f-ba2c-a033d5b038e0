import 'package:flutter/material.dart';

class DashBoardDataTable extends StatefulWidget {
  const DashBoardDataTable({super.key});

  @override
  _DashBoardDataTableState createState() => _DashBoardDataTableState();
}

class _DashBoardDataTableState extends State<DashBoardDataTable>
    with RestorationMixin {
  @override
  // TODO: implement restorationId
  String? get restorationId => throw UnimplementedError();

  @override
  void restoreState(RestorationBucket? oldBucket, bool initialRestore) {
    // TODO: implement restoreState
  }

  @override
  Widget build(BuildContext context) {
    return Container();
  }
}

class _RestorableMerchantSelections extends RestorableProperty<Set<int>> {
  @override
  Set<int> createDefaultValue() {
    // TODO: implement createDefaultValue
    throw UnimplementedError();
  }

  @override
  Set<int> fromPrimitives(Object? data) {
    // TODO: implement fromPrimitives
    throw UnimplementedError();
  }

  @override
  void initWithValue(Set<int> value) {
    // TODO: implement initWithValue
  }

  @override
  Object? toPrimitives() {
    // TODO: implement toPrimitives
    throw UnimplementedError();
  }
}
