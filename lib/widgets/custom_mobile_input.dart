import 'package:country_picker/country_picker.dart';
import 'package:admin_portal/configs/configs_export.dart';
import 'package:admin_portal/widgets/widgets_export.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CustomMobileInput extends StatefulWidget {
  const CustomMobileInput({
    super.key,
    this.onChanged,
    required this.phoneController,
    required this.countryCodeController,
    required this.countryCodeStringController,
    this.countryCodeString,
    required this.enabled,
    this.isVerified,
    this.hintText,
    this.isOptional,
    this.isProfile,
  });
  
  final Function(String countryCode)? onChanged;
  final TextEditingController countryCodeController;
  final TextEditingController phoneController;
  final TextEditingController countryCodeStringController;
  final String? countryCodeString;
  final String? hintText;
  final bool enabled;
  final bool? isVerified;
  final bool? isOptional;
  final bool? isProfile;

  @override
  State<CustomMobileInput> createState() => _CustomMobileInputState();
}

class _CustomMobileInputState extends State<CustomMobileInput> {
  String _flag = '🇺🇸'; // Default flag

  @override
  void initState() {
    super.initState();
    // Set default country code if not set
    if (widget.countryCodeController.text.isEmpty) {
      widget.countryCodeController.text = '+1';
      widget.countryCodeStringController.text = 'US';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // Country code picker
        GestureDetector(
          onTap: widget.enabled ? showCountryCodePicker : null,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.kBlack),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _flag,
                  style: const TextStyle(fontSize: 20),
                ),
                const SizedBox(width: 8),
                Text(
                  widget.countryCodeController.text,
                  style: AppTextStyles.textStyleRegular14(context),
                ),
                const SizedBox(width: 4),
                Icon(
                  Icons.keyboard_arrow_down,
                  color: AppColors.kBlack,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
        const SizedBox(width: 12),
        // Phone number input
        Expanded(
          child: CustomTextFormField(
            hintText: widget.hintText ?? 'Phone number',
            controller: widget.phoneController,
            validator: widget.isOptional == null
                ? (value) {
                    if (value == null || value.isEmpty) {
                      return 'Phone number is required';
                    }
                    if (value.length < 10) {
                      return 'Please enter a valid phone number';
                    }
                    return null;
                  }
                : null,
            textInputAction: TextInputAction.next,
            textInputType: TextInputType.phone,
            inputFormatter: [FilteringTextInputFormatter.digitsOnly],
            suffixIcon: (widget.isVerified ?? false)
                ? IconButton(
                    onPressed: () {},
                    icon: const Icon(
                      Icons.check_rounded,
                      color: Colors.green,
                    ),
                  )
                : null,
            onChange: (value) {
              if (widget.onChanged != null) {
                widget.onChanged!(widget.countryCodeController.text);
              }
            },
          ),
        ),
      ],
    );
  }

  void showCountryCodePicker() {
    showCountryPicker(
      context: context,
      onSelect: (country) {
        setState(() {
          _flag = country.flagEmoji;
        });
        widget.countryCodeController.text = '+${country.phoneCode}';
        widget.countryCodeStringController.text = country.countryCode;
        if (widget.onChanged != null) {
          widget.onChanged!(widget.countryCodeController.text);
        }
      },
      countryListTheme: CountryListThemeData(
        backgroundColor: AppColors.kWhite,
        textStyle: AppTextStyles.textStyleRegular14(context),
        bottomSheetHeight: MediaQuery.of(context).size.height * 0.7,
        inputDecoration: InputDecoration(
          hintText: 'Search',
          hintStyle: AppTextStyles.textStyleFormFieldHintText(context),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          suffixIcon: Icon(
            Icons.search,
            color: AppColors.kBlack,
          ),
        ),
      ),
    );
  }
}