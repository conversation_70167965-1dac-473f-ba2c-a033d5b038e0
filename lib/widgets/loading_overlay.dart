// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:ui';

import 'package:admin_portal/utils/constants/color_constants.dart';
import 'package:flutter/material.dart';

class LoadingOverlay extends StatelessWidget {
  const LoadingOverlay({
    super.key,
    required this.isLoading,
    this.child,
  });
  final bool isLoading;
  final Widget? child;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        if (child != null) child!,
        if (isLoading)
          BackdropFilter(
            filter: ImageFilter.blur(
              sigmaX: 4,
              sigmaY: 4,
            ),
            child: Container(
              height: MediaQuery.of(context).size.height,
              width: MediaQuery.of(context).size.width,
              color: kBlackColor.withOpacity(0.2),
              child: const Center(
                child: CircularProgressIndicator(
                  color: kPrimaryColor,
                  strokeWidth: 1.5,
                ),
              ),
            ),
          ),
      ],
    );
  }
}
