import 'package:admin_portal/configs/configs_export.dart';
import 'package:admin_portal/models/order_list_model_new.dart';
import 'package:admin_portal/provider/provider_export.dart';
import 'package:admin_portal/utils/utils_export.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class HostnameSelectionWidget extends StatefulWidget {
  const HostnameSelectionWidget({
    super.key,
    required this.items,
    required this.hostDropDownList,
  });
  final List<Servers> items;
  final List<HostNameForDropdown> hostDropDownList;

  @override
  _HostnameSelectionWidgetState createState() =>
      _HostnameSelectionWidgetState();
}

class _HostnameSelectionWidgetState extends State<HostnameSelectionWidget> {
  late Map<String, String?> selectedHostnames;

  @override
  void initState() {
    super.initState();
    _initializeSelectedHostnames();
  }

  void _initializeSelectedHostnames() {
    selectedHostnames = {};

    for (final server in widget.items) {
      selectedHostnames[server.serverID!] = server.hostName?.isNotEmpty == true
          ? server.hostName
          : AppStrings.dropdownDefalutName;
    }
  }

  List<String> _returnHostNameListString(BuildContext context) {
    final itemList = <String>[];

    for (var i = 0; i <= widget.hostDropDownList.length; i++) {
      itemList.add(
        i == 0
            ? AppStrings.dropdownDefalutName
            : widget.hostDropDownList[i - 1].hostName!,
      );
    }

    return itemList;
  }

  List<String> _getAvailableHostnames(String? currentHostname) {
    return _returnHostNameListString(context)
        .where(
          (hostName) =>
              !selectedHostnames.values.contains(hostName) ||
              hostName == currentHostname,
        )
        .toList();
  }

  @override
  void dispose() {
    // Clear the selectedHostnames map when the dialog is closed
    selectedHostnames.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(0),
      child: DecoratedBox(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: AppColors.kBlack),
        ),
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: SizedBox(
            width: 2000,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: widget.items.isEmpty ? 1 : widget.items.length + 1,
              itemBuilder: (context, index) {
                if (index == 0) {
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 20),
                    child: Row(
                      children: [
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.left,
                            'Server ID',
                            style:
                                AppTextStyles.textStyleBold16(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.left,
                            'Hardware Model',
                            style:
                                AppTextStyles.textStyleBold16(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                        Expanded(
                          child: SelectableText(
                            textAlign: TextAlign.center,
                            'Hostname',
                            style:
                                AppTextStyles.textStyleBold16(context).copyWith(
                              color: Provider.of<DarkThemeProvider>(
                                context,
                              ).darkTheme
                                  ? AppColors.kDarkPrimarySwatch
                                  : AppColors.kLightPrimarySwatch,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }
                index -= 1;

                final row = widget.items[index];
                final currentHostname = selectedHostnames[row.serverID!];

                return Padding(
                  padding: const EdgeInsets.only(),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Expanded(
                        child: SelectableText(
                          textAlign: TextAlign.left,
                          row.serverID!,
                          style: AppTextStyles.textStyleRegular16(context),
                        ),
                      ),
                      Expanded(
                        child: SelectableText(
                          textAlign: TextAlign.left,
                          row.model!,
                          style: AppTextStyles.textStyleRegular16(context),
                        ),
                      ),
                      Expanded(
                        child: DropdownSearch<String>(
                          popupProps: const PopupProps.menu(
                            showSearchBox: true,
                            showSelectedItems: true,
                          ),
                          items: _getAvailableHostnames(currentHostname),
                          dropdownDecoratorProps: const DropDownDecoratorProps(
                            dropdownSearchDecoration: InputDecoration(
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: Colors.transparent,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: Colors.transparent,
                                ),
                              ),
                              disabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: Colors.transparent,
                                ),
                              ),
                            ),
                          ),
                          onChanged: (value) {
                            setState(() {
                              if (currentHostname != null &&
                                  currentHostname.isNotEmpty &&
                                  currentHostname !=
                                      AppStrings.dropdownDefalutName) {
                                selectedHostnames.remove(row.serverID);
                              }

                              if (value == AppStrings.dropdownDefalutName) {
                                row.selectedHostName = '';
                              } else {
                                selectedHostnames[row.serverID!] = value;
                                row.selectedHostName = value;
                              }
                            });
                          },
                          selectedItem: currentHostname,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}
