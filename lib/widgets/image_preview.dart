import 'package:admin_portal/configs/themes/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:responsive_framework/responsive_framework.dart';

class UploadedImagePreview extends StatelessWidget {
  const UploadedImagePreview({
    super.key,
    this.memoryTransparentLogo,
    this.memoryDarkLogo,
    this.onTransparentLogoPressed,
    this.onDarkLogoPressed,
  });
  final Uint8List? memoryTransparentLogo;
  final Uint8List? memoryDarkLogo;
  final Function()? onTransparentLogoPressed;
  final Function()? onDarkLogoPressed;
  @override
  Widget build(BuildContext context) {
    return Container(
      height: ResponsiveValue(
        context,
        conditionalValues: [
          Condition.equals(name: MOBILE, value: 200.toDouble()),
          Condition.equals(name: TABLET, value: 200.toDouble()),
          Condition.equals(name: DESKTOP, value: 260.toDouble()),
        ],
      ).value,
      width: ResponsiveValue(
        context,
        conditionalValues: [
          Condition.equals(name: MOBI<PERSON>, value: 200.toDouble()),
          Condition.equals(name: TABLET, value: 200.toDouble()),
          Condition.equals(name: DESKTOP, value: 400.toDouble()),
        ],
      ).value,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: AppColors.kLightGrayLight,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              if (memoryTransparentLogo != null)
                Image.memory(
                  memoryTransparentLogo!,
                  fit: BoxFit.cover,
                ),
              IconButton(
                hoverColor: AppColors.kLightGrayLight,
                icon: const Icon(Icons.delete),
                onPressed: onTransparentLogoPressed,
              ),
            ],
          ),
          // VerticalDivider(
          //   color: AppColors.kLightGrayDark,
          //   thickness: 1,
          //   width: 20,
          // ),
          if (memoryDarkLogo != null)
            Stack(
              alignment: Alignment.center,
              children: [
                Image.memory(
                  memoryDarkLogo!,
                  fit: BoxFit.cover,
                ),
                IconButton(
                  hoverColor: AppColors.kLightGrayLight,
                  icon: const Icon(Icons.delete),
                  onPressed: onDarkLogoPressed,
                ),
              ],
            ),
        ],
      ),
    );
  }
}
