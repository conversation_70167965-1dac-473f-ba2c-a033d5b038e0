import 'package:admin_portal/configs/app_textstyles.dart';
import 'package:admin_portal/configs/themes/app_colors.dart';
import 'package:flutter/material.dart';

class CustomSearchField extends StatefulWidget {
  const CustomSearchField({
    super.key,
    required this.textController,
    this.onChanged,
    this.onSearchPressed,
  });
  final TextEditingController textController;
  final Function(String)? onChanged;
  final Function(String)? onSearchPressed;

  @override
  State<CustomSearchField> createState() => _CustomSearchFieldState();
}

class _CustomSearchFieldState extends State<CustomSearchField> {
  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: widget.textController,
      cursorColor: AppColors.kDarkGrayLight,
      style: AppTextStyles.textStyleRegular16(context)
          .copyWith(color: AppColors.kBlack),
      onSubmitted: widget.onSearchPressed,
      decoration: InputDecoration(
        filled: true,
        fillColor: AppColors.kDarkGrayLight.withOpacity(0.22),
        focusColor: AppColors.kWhite.withOpacity(0.53),
        hintText: 'Search',
        hintStyle: AppTextStyles.textStyleRegular16(context)
            .copyWith(color: AppColors.kDarkGrayLight),
        contentPadding: const EdgeInsets.symmetric(horizontal: 8),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(15),
          borderSide: BorderSide(color: AppColors.kWhite.withOpacity(0.53)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(15),
          borderSide: BorderSide(color: AppColors.kWhite.withOpacity(0.53)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(15),
          borderSide: BorderSide(color: AppColors.kWhite.withOpacity(0.53)),
        ),

        prefixIcon: Padding(
          padding: const EdgeInsets.only(left: 12),
          child: Icon(
            Icons.search,
            color: AppColors.kDarkGrayLight,
          ),
          // child: SvgPicture.asset(
          //   'assets/images/carbon_search.svg',
          //   colorFilter:
          //       ColorFilter.mode(AppColors.lightGray, BlendMode.srcIn),
          // ),
        ),
        // prefixIcon: Icon(
        //   Icons.search,
        //   color: AppColors.lightGray,
        // ),
        prefixIconConstraints: const BoxConstraints(
          minWidth: 42,
          maxWidth: 44,
          minHeight: 18,
          maxHeight: 20,
        ),
      ),
      onChanged: widget.onChanged,
    );
  }
}
