import 'package:admin_portal/configs/configs_export.dart';
import 'package:flutter/material.dart';
import 'package:iconsax/iconsax.dart';
import 'package:responsive_framework/responsive_framework.dart';

class CommonFileUploadButton extends StatelessWidget {
  const CommonFileUploadButton({
    super.key,
    required this.title,
    this.onPressed,
    this.isUploaded = false,
    this.fileName,
  });
  final String title;
  final Function()? onPressed;
  final bool isUploaded;
  final String? fileName;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SizedBox(
          width: ResponsiveValue(
            context,
            conditionalValues: [
              Condition.equals(name: MOBILE, value: 200.toDouble()),
              Condition.equals(name: TABLET, value: 200.toDouble()),
              Condition.equals(name: DESKTOP, value: 200.toDouble()),
            ],
          ).value,
          child: ElevatedButton(
            style: Theme.of(context).elevatedButtonTheme.style!.copyWith(
                  backgroundColor: WidgetStateProperty.all<Color>(
                    isUploaded
                        ? AppColors.kLightPrimarySwatch
                        : AppColors.kWhite,
                  ),
                  padding: WidgetStateProperty.all<EdgeInsetsGeometry>(
                    const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                  ),
                  shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                    RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                      side: BorderSide(
                        color: AppColors.kLightPrimarySwatch,
                      ),
                    ),
                  ),
                ),
            onPressed: onPressed,
            child: Row(
              children: [
                Icon(
                  Iconsax.gallery_export,
                  color: isUploaded
                      ? AppColors.kWhite
                      : AppColors.kLightPrimarySwatch,
                ),
                const SizedBox(
                  width: 10,
                ),
                Text(
                  title,
                  style: AppTextStyles.textStyleBold14(context).copyWith(
                    color: isUploaded
                        ? AppColors.kWhite
                        : AppColors.kLightPrimarySwatch,
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(
          width: 10,
        ),
        if (fileName != null)
          Text(
            fileName!,
            style: AppTextStyles.textStyleRegular14(context).copyWith(
              color: AppColors.kBlack,
              decoration: TextDecoration.underline,
            ),
          ),
      ],
    );
  }
}
