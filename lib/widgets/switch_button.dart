import 'package:admin_portal/configs/configs_export.dart';
import 'package:admin_portal/utils/utils_export.dart';
import 'package:flutter/material.dart';

class SwitchButton extends StatelessWidget {
  const SwitchButton({super.key, required this.onChanged});
  final Function(bool) onChanged;
  @override
  Widget build(BuildContext context) {
    return Switch(
      value: true,
      onChanged: onChanged,
      activeTrackColor: Colors.white,
      activeColor: Colors.white,
      activeThumbImage: const AssetImage(ImageConstants.switchOn),
      inactiveThumbImage: const AssetImage(ImageConstants.switchOn),
      trackOutlineColor:
          WidgetStateProperty.resolveWith((states) => AppColors.kBlack),
    );
  }
}
