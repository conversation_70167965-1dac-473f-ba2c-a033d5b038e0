import 'package:admin_portal/configs/configs_export.dart';
import 'package:admin_portal/provider/provider_export.dart';
import 'package:admin_portal/utils/utils_export.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

AppBar buildAppBar(BuildContext context, String title) {
  final themeChange = Provider.of<DarkThemeProvider>(context);
  return AppBar(
    centerTitle: false,
    backgroundColor: Theme.of(context).colorScheme.surface,
    title: Text(
      title,
      style: AppTextStyles.textStyleDashboardTitle(context),
    ),
    actions: [
      Switch(
        value: themeChange.darkTheme,
        activeColor: AppColors.kWhite,
        inactiveTrackColor: AppColors.kWhite,
        thumbColor: WidgetStateProperty.all(
          themeChange.darkTheme ? AppColors.kBlack : AppColors.kWhite,
        ),
        onChanged: (value) {
          themeChange.darkTheme = value;
        },
        activeThumbImage: const AssetImage(ImageConstants.switchOn),
        inactiveThumbImage: const AssetImage(ImageConstants.switchOff),
      ),
      // Container(
      //   height: 40,
      //   width: 40,
      //   margin: EdgeInsets.only(left: 20, right: 20),
      //   decoration: BoxDecoration(
      //     color: AppColors.kLightGrayLight,
      //     borderRadius: BorderRadius.circular(20),
      //   ),
      //   child: Image.asset(
      //     ImageConstants.notificatioIcon,
      //     height: 20,
      //     width: 20,
      //   ),
      // ),
      // Image.asset(ImageConstants.profileIcon)
      const SizedBox(
        width: 80,
      ),
    ],
    // shape: Border(bottom: BorderSide(color: AppColors.kBlack, width: 0)),
  );
}
