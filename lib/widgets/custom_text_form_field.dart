import 'package:admin_portal/configs/configs_export.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CustomTextFormField extends StatefulWidget {
  const CustomTextFormField({
    super.key,
    this.prefixIcon,
    required this.hintText,
    required this.controller,
    required this.validator,
    this.label,
    this.labelColor,
    this.width,
    this.focusNode,
    this.textInputAction,
    this.onFieldSubmitted,
    this.autoFocus,
    this.readOnly,
    this.suffixIcon,
    this.enableObscure = false,
    this.textInputType,
    this.inputFormatter,
    this.onChange,
    this.hintStyle,
    this.isEnabled = true,
  });
  final Widget? prefixIcon;
  final String hintText;
  final Widget? suffixIcon;
  final bool enableObscure;
  final bool? readOnly;
  final TextEditingController controller;
  final String? Function(String?)? validator;
  final bool? autoFocus;
  final String? label;
  final Color? labelColor;
  final double? width;
  final FocusNode? focusNode;
  final TextInputAction? textInputAction;
  final Function(String)? onFieldSubmitted;
  final TextInputType? textInputType;
  final List<TextInputFormatter>? inputFormatter;
  final Function(String)? onChange;
  final TextStyle? hintStyle;
  final bool isEnabled;
  @override
  State<CustomTextFormField> createState() => _CustomTextFormFieldState();
}

class _CustomTextFormFieldState extends State<CustomTextFormField> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.width ?? MediaQuery.of(context).size.width * 0.5,
      // height: 100,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.label != null)
            Text(
              widget.label!,
              style: AppTextStyles.textStyleDashboardMessage(context).copyWith(
                  // color: widget.labelColor ?? AppColors.kLightPrimarySwatch,
                  ),
            ),
          TextFormField(
            enabled: widget.isEnabled,
            readOnly: widget.readOnly ?? false,
            controller: widget.controller,
            focusNode: widget.focusNode,
            textInputAction: widget.textInputAction,
            keyboardType: widget.textInputType,
            inputFormatters: widget.inputFormatter,
            autofocus: widget.autoFocus ?? false,
            autovalidateMode: AutovalidateMode.disabled,
            decoration: InputDecoration(
              prefix: widget.prefixIcon,
              hintText: widget.hintText,
              hintStyle: widget.hintStyle ?? AppTextStyles.textStyleFormFieldHintText(context),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide(color: AppColors.kBlack),
              ),
              suffixIconConstraints:
                  const BoxConstraints(minHeight: 32, maxHeight: 34),
              suffixIcon: widget.suffixIcon,
            ),
            obscureText: widget.enableObscure,
            validator: widget.validator,
            onFieldSubmitted: widget.onFieldSubmitted,
            onChanged: widget.onChange,
          ),
        ],
      ),
    );
  }
}
