import 'package:admin_portal/configs/configs_export.dart';
import 'package:admin_portal/utils/utils_export.dart';
import 'package:flutter/material.dart';
import 'package:iconsax/iconsax.dart';
import 'package:responsive_framework/responsive_framework.dart';

class ImagePreviewPlaceholder extends StatelessWidget {
  const ImagePreviewPlaceholder({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: ResponsiveValue(
        context,
        conditionalValues: [
          Condition.equals(name: MOBILE, value: 200.toDouble()),
          Condition.equals(name: TABLET, value: 200.toDouble()),
          Condition.equals(name: DESKTOP, value: 260.toDouble()),
        ],
      ).value,
      width: ResponsiveValue(
        context,
        conditionalValues: [
          Condition.equals(name: MOBILE, value: 200.toDouble()),
          Condition.equals(name: TABLET, value: 200.toDouble()),
          Condition.equals(name: DESKTOP, value: 400.toDouble()),
        ],
      ).value,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: AppColors.kLightGrayLight,
      ),
      margin: const EdgeInsets.only(top: 20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Iconsax.gallery,
            size: 50,
            color: AppColors.kLightGrayDark,
          ),
          Text(
            AppStrings.previewCap,
            style: AppTextStyles.textStyleRegular14(context)
                .copyWith(color: AppColors.kLightGrayDark),
          ),
        ],
      ),
    );
  }
}
