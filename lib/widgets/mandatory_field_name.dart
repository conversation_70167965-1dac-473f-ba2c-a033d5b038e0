import 'package:admin_portal/configs/configs_export.dart';
import 'package:flutter/material.dart';

class MandatoryFieldName extends StatelessWidget {
  const MandatoryFieldName({
    super.key,
    required this.message,
    required this.isBold,
  });
  final String message;
  final bool isBold;
  @override
  Widget build(BuildContext context) {
    return RichText(
      text: TextSpan(
        text: '* ',
        style: TextStyle(color: AppColors.kRed),
        children: <TextSpan>[
          TextSpan(
            text: message,
            style: isBold
                ? AppTextStyles.textStyleBold14(context)
                    .copyWith(color: AppColors.kLightGrayDark)
                : AppTextStyles.textStyleRegular14(context)
                    .copyWith(color: AppColors.kLightGrayDark),
          ),
        ],
      ),
    );
  }
}
