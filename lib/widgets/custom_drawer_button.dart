import 'package:admin_portal/configs/configs_export.dart';
import 'package:admin_portal/provider/provider_export.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:responsive_framework/responsive_framework.dart';

class CustomDrawerButton extends StatelessWidget {
  const CustomDrawerButton({
    super.key,
    required this.title,
    required this.iconPath,
    required this.isSelected,
    this.selectedColor,
  });
  final String title;
  // final String iconPath;
  final IconData iconPath;

  final bool isSelected;
  final Color? selectedColor;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 20, right: 20),
      child: DecoratedBox(
        // style: ButtonStyle(
        //     foregroundColor: MaterialStateProperty.all<Color>(Colors.white),
        //     backgroundColor: MaterialStateProperty.all<Color>(
        //         Provider.of<DarkThemeProvider>(context).darkTheme
        //             ? AppColors.kDarkPrimarySwatch
        //             : AppColors.kLightPrimarySwatch),
        //     shape: MaterialStateProperty.all<RoundedRectangleBorder>(
        //         RoundedRectangleBorder(
        //             borderRadius: BorderRadius.circular(20.0),
        //             side: BorderSide(color: Colors.blueAccent)))),
        // onPressed: () {},
        decoration: BoxDecoration(
          color: isSelected
              ? Provider.of<DarkThemeProvider>(context).darkTheme
                  ? AppColors.kDarkPrimarySwatch
                  : AppColors.kLightPrimarySwatch
              : Provider.of<DarkThemeProvider>(context).darkTheme
                  ? AppColors.kDarkGrayLight
                  : AppColors.kLightGray.withOpacity(0.7),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          children: [
            Padding(
              padding: const EdgeInsets.all(12),
              child: Icon(
                iconPath,
                size: 20,
                color: isSelected
                    ? AppColors.kWhite
                    : Provider.of<DarkThemeProvider>(context).darkTheme
                        ? AppColors.kWhite
                        : AppColors.kBlack,
              ),
            ),
            const SizedBox(
              width: 20,
            ),
            Text(
              title,
              style: GoogleFonts.lato(
                fontSize: ResponsiveValue(
                  context,
                  conditionalValues: [
                    Condition.equals(name: MOBILE, value: 14.toDouble()),
                    Condition.equals(name: TABLET, value: 14.toDouble()),
                    Condition.equals(name: DESKTOP, value: 14.toDouble()),
                  ],
                ).value,
                fontWeight: FontWeight.bold,
                color: isSelected
                    ? AppColors.kWhite
                    : Provider.of<DarkThemeProvider>(context).darkTheme
                        ? AppColors.kWhite
                        : AppColors.kBlack,
              ),
            ),
          ],
        ),
      ),
    );
    // return Padding(
    //   padding: const EdgeInsets.only(left: 20, right: 20),
    //   child: ListTile(
    //     selected: isSelected,

    //     shape: RoundedRectangleBorder(
    //       borderRadius: BorderRadius.circular(40),
    //     ),
    //     // leading: Image.asset(
    //     //   iconPath,
    //     //   height: 20,
    //     //   width: 20,
    //     //   color: isSelected ? AppColors.kWhite : AppColors.kBlack,
    //     // ),
    //     leading: Icon(iconPath,
    //         size: 20,
    //         color: isSelected
    //             ? AppColors.kWhite
    //             : Provider.of<DarkThemeProvider>(context).darkTheme
    //                 ? AppColors.kWhite
    //                 : AppColors.kBlack),
    //     title: Text(title,
    //         style: GoogleFonts.lato(
    //           fontSize: ResponsiveValue(context, conditionalValues: [
    //             Condition.equals(name: MOBILE, value: 14.0),
    //             Condition.equals(name: TABLET, value: 14.0),
    //             Condition.equals(name: DESKTOP, value: 14.0),
    //           ]).value,
    //           fontWeight: FontWeight.bold,
    //           color: isSelected
    //               ? AppColors.kWhite
    //               : Provider.of<DarkThemeProvider>(context).darkTheme
    //                   ? AppColors.kWhite
    //                   : AppColors.kBlack,
    //         )),
    //   ),
    // );
  }
}
