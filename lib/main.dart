import 'package:admin_portal/configs/themes/app_themes.dart';
import 'package:admin_portal/handlers/error_manager.dart';
import 'package:admin_portal/provider/provider_export.dart';
import 'package:admin_portal/routes/app_routes.dart';
import 'package:admin_portal/utils/utils_export.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:responsive_framework/responsive_framework.dart';

void main() async {
  // Required for async calls in `main`
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize ErrorManager instance.
  if (!kDebugMode) {
    _setupErrorManagement();
  }
  // Initialize PreferenceUtils instance.
  await PreferenceUtils().init();

  // Fetch app version
  const appVersion = String.fromEnvironment('APP_VERSION') ?? 'Unknown';

  // Log to the browser console
  print('App Version: $appVersion');
  runApp(const MyApp());
}

void _setupErrorManagement() async {
  await ErrorManager.init();
  FlutterError.onError = (details) {
    ErrorManager.reportError(
      details.exception,
      details.stack,
    );
  };
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  // This widget is the root of your application.
  DarkThemeProvider themeChangeProvider = DarkThemeProvider();

  @override
  void initState() {
    super.initState();
    getCurrentAppTheme();
  }

  void getCurrentAppTheme() async {
    themeChangeProvider.darkTheme =
        await themeChangeProvider.darkThemePreference.getTheme();
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // ChangeNotifierProvider<DashBoardProvider>(
        //     create: (_) => DashBoardProvider()),
        ChangeNotifierProvider<ManageMerchantProvider>(
          // create: (context) => ManageMerchantProvider(GoRouter.of(context))),
          create: (context) => ManageMerchantProvider(),
        ),
        ChangeNotifierProvider<MerchantDetailsProvider>(
          create: (context) => MerchantDetailsProvider(),
        ),
        ChangeNotifierProvider<MyTeamProvider>(
          create: (context) => MyTeamProvider(),
        ),
        ChangeNotifierProvider<DarkThemeProvider>(
          create: (context) => themeChangeProvider,
        ),
        ChangeNotifierProvider(
          create: (context) => AuthProvider(),
        ),
        // themeChangeProvider
      ],
      // create: (_) => themeChangeProvider,
      child: Consumer<DarkThemeProvider>(
        builder: (context, value, child) {
          return MaterialApp.router(
            title: 'NexQloud Admin Portal',
            debugShowCheckedModeBanner: false,
            themeMode: ThemeMode.light,
            theme: AppTheme.themeData(themeChangeProvider.darkTheme, context),
            darkTheme: AppTheme.themeData(true, context),
            builder: (context, child) => ResponsiveBreakpoints.builder(
              child: child!,
              breakpoints: [
                const Breakpoint(start: 0, end: 450, name: MOBILE),
                const Breakpoint(start: 451, end: 800, name: TABLET),
                const Breakpoint(start: 801, end: 1920, name: DESKTOP),
                const Breakpoint(start: 1921, end: double.infinity, name: '4K'),
              ],
            ),
            routerConfig: AppRoutes.router,
          );
        },
      ),
    );
  }
}
