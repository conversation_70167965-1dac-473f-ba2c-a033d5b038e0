import 'package:flutter_test/flutter_test.dart';
import 'package:admin_portal/services/platform_storage_service.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('PlatformStorageService Tests', () {
    setUp(() async {
      // Clear any existing data before each test
      await PlatformStorageService.clearAllData();
    });

    tearDown(() async {
      // Clean up after each test
      await PlatformStorageService.clearAllData();
    });

    test('should store and retrieve user data', () async {
      const userId = 'test_user_123';
      const userName = 'Test User';
      const token = 'test_token_456';

      // Store user data
      await PlatformStorageService.storeUserData(
        userId: userId,
        userName: userName,
        token: token,
      );

      // Retrieve and verify data
      final retrievedUserId = await PlatformStorageService.getUserId();
      final retrievedUserName = await PlatformStorageService.getUserName();
      final retrievedToken = await PlatformStorageService.getToken();
      final isLoggedIn = await PlatformStorageService.isUserLoggedIn();

      expect(retrievedUserId, equals(userId));
      expect(retrievedUserName, equals(userName));
      expect(retrievedToken, equals(token));
      expect(isLoggedIn, isTrue);
    });

    test('should store and retrieve user data without token', () async {
      const userId = 'test_user_789';
      const userName = 'Test User 2';

      // Store user data without token
      await PlatformStorageService.storeUserData(
        userId: userId,
        userName: userName,
      );

      // Retrieve and verify data
      final retrievedUserId = await PlatformStorageService.getUserId();
      final retrievedUserName = await PlatformStorageService.getUserName();
      final retrievedToken = await PlatformStorageService.getToken();
      final isLoggedIn = await PlatformStorageService.isUserLoggedIn();

      expect(retrievedUserId, equals(userId));
      expect(retrievedUserName, equals(userName));
      expect(retrievedToken, isNull);
      expect(isLoggedIn, isTrue);
    });

    test('should clear all data', () async {
      const userId = 'test_user_clear';
      const userName = 'Test User Clear';

      // Store some data
      await PlatformStorageService.storeUserData(
        userId: userId,
        userName: userName,
      );

      // Verify data is stored
      expect(await PlatformStorageService.isUserLoggedIn(), isTrue);

      // Clear all data
      await PlatformStorageService.clearAllData();

      // Verify data is cleared
      final retrievedUserId = await PlatformStorageService.getUserId();
      final retrievedUserName = await PlatformStorageService.getUserName();
      final isLoggedIn = await PlatformStorageService.isUserLoggedIn();

      expect(retrievedUserId, isNull);
      expect(retrievedUserName, isNull);
      expect(isLoggedIn, isFalse);
    });

    test('should return false for isUserLoggedIn when no data', () async {
      final isLoggedIn = await PlatformStorageService.isUserLoggedIn();
      expect(isLoggedIn, isFalse);
    });

    test('should get all user data', () async {
      const userId = 'test_user_all';
      const userName = 'Test User All';
      const token = 'test_token_all';

      // Store user data
      await PlatformStorageService.storeUserData(
        userId: userId,
        userName: userName,
        token: token,
      );

      // Get all user data
      final allData = await PlatformStorageService.getAllUserData();

      expect(allData['userId'], equals(userId));
      expect(allData['userName'], equals(userName));
      expect(allData['token'], equals(token));
      expect(allData['isLogin'], equals('true'));
    });
  });
}
